import { User, Role, UserRole } from '@prisma/client'

// 扩展的用户类型
export interface ExtendedUser extends Omit<User, 'username'> {
  username: string | null
  userRoles?: (UserRole & { role: Role })[]
  currencyBalance?: {
    id: string
    userId: string
    balance: number
    frozenBalance: number
    totalEarned: number
    totalSpent: number
    createdAt: Date
    updatedAt: Date
  } | null
}

// 登录表单类型
export interface LoginFormData {
  email: string
  password: string
  remember?: boolean
}

// 注册表单类型
export interface RegisterFormData {
  name: string
  email: string
  password: string
  confirmPassword: string
  agreeToTerms: boolean
}

// 用户名注册表单类型
export interface UsernameRegisterFormData {
  username: string
  email: string
  name: string
  password: string
  confirmPassword: string
  agreeToTerms: boolean
}

// 密码重置表单类型
export interface ResetPasswordFormData {
  email: string
}

// 新密码表单类型
export interface NewPasswordFormData {
  password: string
  confirmPassword: string
  token: string
}

// 会话类型
export interface SessionUser {
  id: string
  email: string
  username?: string | null
  name?: string | null
  avatar?: string | null
  roles: string[]
  permissions: string[]
  membershipLevel: 'guest' | 'registered' | 'vip' | 'diamond' | 'admin'
}

// 权限枚举
export enum Permission {
  // 基础权限
  READ_PUBLIC = 'read:public',
  READ_BASIC = 'read:basic',
  
  // 高级功能权限
  USE_SIMULATOR = 'use:simulator',
  EXPORT_DATA = 'export:data',
  SAVE_CONFIG = 'save:config',
  
  // VIP 权限
  USE_ADVANCED_FEATURES = 'use:advanced_features',
  UNLIMITED_ACCESS = 'unlimited:access',
  PRIORITY_SUPPORT = 'priority:support',
  
  // 管理权限
  MANAGE_USERS = 'manage:users',
  MANAGE_CONTENT = 'manage:content',
  VIEW_ANALYTICS = 'view:analytics',
  SYSTEM_CONFIG = 'system:config'
}

// 用户角色枚举
export enum UserRoleEnum {
  GUEST = 'guest',
  REGISTERED = 'registered',
  VIP = 'vip',
  DIAMOND = 'diamond',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin'
}

// API 响应类型
export interface AuthResponse {
  success: boolean
  message?: string
  user?: SessionUser
  error?: string
}

// 邮箱验证类型
export interface EmailVerificationData {
  token: string
  email: string
}

// 设备指纹类型
export interface DeviceFingerprintData {
  visitorId: string
  confidence: number
  components: Record<string, any>
  metadata?: Record<string, any>
}
