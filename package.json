{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "type-check": "tsc --noEmit"}, "dependencies": {"@auth/prisma-adapter": "^2.7.4", "@fingerprintjs/fingerprintjs": "^4.5.1", "@hookform/resolvers": "^3.9.1", "@prisma/client": "^5.22.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "latest", "autoprefixer": "^10.4.20", "bcryptjs": "^2.4.3", "child_process": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "embla-carousel-react": "8.5.1", "fs": "latest", "input-otp": "1.4.1", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.454.0", "next": "14.2.16", "next-auth": "5.0.0-beta.25", "next-themes": "^0.4.4", "path": "latest", "rate-limiter-flexible": "^5.0.3", "react": "^18", "react-day-picker": "8.10.1", "react-dom": "^18", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "redis": "^4.7.0", "resend": "^4.0.1", "sonner": "^1.7.1", "stripe": "^17.3.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.3", "vaul": "^0.9.6", "winston": "^3.17.0", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/crypto-js": "^4.2.2", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "postcss": "^8.5", "prisma": "^5.22.0", "tailwindcss": "^3.4.17", "tsx": "^4.19.2", "typescript": "^5"}}