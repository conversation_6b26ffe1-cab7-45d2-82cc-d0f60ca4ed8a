# 🚀 增量部署使用指南

## 概述

增量部署系统允许您只打包和部署修改过的文件，而不是整个项目，大大减少传输时间和部署复杂度。

## 🛠️ 可用工具

### 1. 统一增量部署工具（推荐）

```bash
node scripts/incremental-deploy.js <模式> [选项]
```

#### 可用模式：

| 模式 | 描述 | 使用场景 |
|------|------|----------|
| `hash` | 基于文件哈希检测变更 | 全面检测所有文件变化 |
| `git` | 基于 Git 提交差异 | 有 Git 版本控制的项目 |
| `quick` | 预定义模板快速更新 | 常见配置更新 |
| `custom` | 自定义文件更新 | 指定特定文件 |
| `nextauth-fix` | NextAuth 反向代理修复 | 当前问题的专用修复 |

### 2. 快速更新模板

| 模板名 | 描述 | 包含文件 |
|--------|------|----------|
| `nextauth` | NextAuth 配置更新 | lib/auth.ts, lib/auth-config.ts |
| `env` | 环境变量更新 | .env.local, .env.example |
| `prisma` | Prisma 配置更新 | prisma/schema.prisma, prisma/seed.ts |
| `components` | 组件更新 | components/**/*.tsx |
| `api` | API 路由更新 | app/api/**/*.ts |
| `pages` | 页面更新 | app/**/*.tsx |
| `styles` | 样式更新 | *.css, tailwind.config.js |

## 📋 使用示例

### 针对当前 NextAuth 问题的修复

```bash
# 创建 NextAuth 修复包
node scripts/incremental-deploy.js nextauth-fix
```

这会创建包含以下内容的更新包：
- 修复后的 `lib/auth.ts`
- 正确配置的 `.env.local` 模板
- 自动应用脚本

### 基于文件哈希的增量更新

```bash
# 首次运行会创建哈希基线
node scripts/incremental-deploy.js hash

# 后续运行会检测变化
node scripts/incremental-deploy.js hash
```

### 基于 Git 的增量更新

```bash
# 检测最近一次提交的变化
node scripts/incremental-deploy.js git

# 检测指定提交范围的变化
node scripts/incremental-deploy.js git HEAD~3 HEAD
```

### 快速模板更新

```bash
# NextAuth 配置更新
node scripts/incremental-deploy.js quick nextauth

# 环境变量更新
node scripts/incremental-deploy.js quick env

# Prisma 配置更新
node scripts/incremental-deploy.js quick prisma
```

### 自定义文件更新

```bash
# 更新指定文件
node scripts/incremental-deploy.js custom lib/auth.ts .env.local

# 更新多个文件
node scripts/incremental-deploy.js custom components/auth/* app/login/page.tsx
```

## 🚀 部署流程

### 1. 创建增量包

```bash
# 选择合适的模式创建增量包
node scripts/incremental-deploy.js nextauth-fix
```

### 2. 上传到服务器

```bash
# 上传增量包目录
scp -r nextauth-fix-2025-06-23T15-47-55/ user@server:/tmp/

# 或者压缩后上传
zip -r update.zip nextauth-fix-2025-06-23T15-47-55/
scp update.zip user@server:/tmp/
```

### 3. 在服务器上应用更新

```bash
# 进入项目目录
cd /root/svc/mxd/mxd-tool

# 复制更新包
cp -r /tmp/nextauth-fix-2025-06-23T15-47-55/* ./

# 应用更新
chmod +x apply-nextauth-fix.sh
./apply-nextauth-fix.sh
```

## 📁 增量包结构

每个增量包都包含：

```
update-package/
├── update-manifest.json     # 更新清单
├── apply-*.sh              # Linux 应用脚本
├── apply-*.bat             # Windows 应用脚本
├── rollback.sh             # 回滚脚本（如果支持）
├── 修改的文件...            # 实际要更新的文件
└── README.md               # 使用说明
```

### 更新清单示例

```json
{
  "type": "nextauth-fix",
  "name": "NextAuth 反向代理修复",
  "timestamp": "2025-06-23T15-47-55",
  "files": ["lib/auth.ts", ".env.local"],
  "needsBuild": false,
  "needsRestart": true,
  "instructions": [
    "修复 NextAuth.js 反向代理问题",
    "更新环境变量配置",
    "重启应用即可生效"
  ]
}
```

## 🔧 高级功能

### 自动备份

所有应用脚本都会自动创建备份：

```bash
backup-nextauth-20250623_154755/
├── lib/
│   └── auth.ts.bak
└── .env.local.bak
```

### 智能分析

系统会自动分析变更类型并确定：
- 是否需要重新构建
- 是否需要安装依赖
- 是否需要重新生成 Prisma 客户端
- 是否需要重启应用

### 回滚支持

```bash
# 如果更新出现问题，可以快速回滚
./rollback.sh
```

## 🎯 最佳实践

### 1. 开发环境测试

```bash
# 在开发环境先测试增量包
node scripts/incremental-deploy.js nextauth-fix
cd nextauth-fix-*/
./apply-nextauth-fix.sh
```

### 2. 分阶段部署

```bash
# 1. 配置文件更新
node scripts/incremental-deploy.js quick env

# 2. 代码逻辑更新
node scripts/incremental-deploy.js quick nextauth

# 3. 数据库相关更新
node scripts/incremental-deploy.js quick prisma
```

### 3. 版本管理

```bash
# 使用 Git 标签管理版本
git tag v1.2.1
node scripts/incremental-deploy.js git v1.2.0 v1.2.1
```

## 🚨 注意事项

### 1. 依赖关系

- 如果更新了 `package.json`，需要运行 `npm install`
- 如果更新了 `prisma/schema.prisma`，需要运行 `npx prisma generate`
- 如果更新了组件或页面，需要重新构建

### 2. 数据库迁移

增量包不包含数据库迁移，如需迁移请单独执行：

```bash
npx prisma migrate deploy
```

### 3. 环境变量

更新环境变量后需要重启应用才能生效。

### 4. 缓存清理

某些更新可能需要清理缓存：

```bash
rm -rf .next/cache
npm run build
```

## 📞 故障排除

### 1. 应用脚本权限问题

```bash
chmod +x apply-*.sh
```

### 2. 文件路径问题

确保在项目根目录运行应用脚本。

### 3. 依赖冲突

如果出现依赖冲突，运行完整安装：

```bash
rm -rf node_modules
npm install --omit=dev
```

### 4. 构建失败

检查错误日志，可能需要：
- 更新 Node.js 版本
- 清理缓存
- 检查代码语法

## 🎉 成功验证

更新完成后，验证以下功能：

- [ ] 应用正常启动
- [ ] 登录功能正常
- [ ] API 接口响应正常
- [ ] 页面渲染正确
- [ ] 数据库连接正常

---

**总结**：增量部署系统大大简化了生产环境的更新流程，特别适合频繁的小幅更新。选择合适的模式，遵循最佳实践，可以实现快速、安全的部署。
