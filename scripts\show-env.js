#!/usr/bin/env node

/**
 * 简化版环境变量显示脚本
 * 可以在任何时候运行来查看当前环境变量状态
 */

// 关键环境变量列表
const keyEnvVars = [
  // 应用基础
  { key: 'NODE_ENV', category: '应用', required: true },
  { key: 'PORT', category: '应用', required: false },
  { key: 'NEXT_PUBLIC_APP_URL', category: '应用', required: false },
  
  // 认证
  { key: 'NEXTAUTH_URL', category: '认证', required: true },
  { key: 'NEXTAUTH_SECRET', category: '认证', required: true, sensitive: true },
  { key: 'AUTH_TRUST_HOST', category: '认证', required: false },
  
  // 数据库
  { key: 'DATABASE_URL', category: '数据库', required: true, sensitive: true },
  { key: 'REDIS_URL', category: '数据库', required: false },
  
  // 邮件
  { key: 'RESEND_API_KEY', category: '邮件', required: false, sensitive: true },
  { key: 'EMAIL_FROM', category: '邮件', required: false },
  
  // 第三方
  { key: 'GOOGLE_CLIENT_ID', category: '第三方', required: false },
  { key: 'GOOGLE_CLIENT_SECRET', category: '第三方', required: false, sensitive: true }
]

function maskValue(value, sensitive = false) {
  if (!value) return '❌ 未设置'
  
  if (sensitive) {
    if (value.length <= 8) return '✅ ***已设置***'
    return `✅ ${value.substring(0, 4)}...${value.substring(value.length - 4)}`
  }
  
  return `✅ ${value}`
}

function showEnvironmentVariables() {
  console.log('\n🔧 环境变量状态检查')
  console.log('='.repeat(50))
  console.log(`时间: ${new Date().toLocaleString('zh-CN')}`)
  console.log(`环境: ${process.env.NODE_ENV || 'development'}`)
  console.log(`进程: ${process.pid}`)
  console.log('')
  
  // 按分类分组
  const categories = {}
  keyEnvVars.forEach(env => {
    if (!categories[env.category]) {
      categories[env.category] = []
    }
    categories[env.category].push(env)
  })
  
  let totalRequired = 0
  let configuredRequired = 0
  
  Object.entries(categories).forEach(([category, envs]) => {
    console.log(`📋 ${category}配置:`)
    
    envs.forEach(env => {
      const value = process.env[env.key]
      const displayValue = maskValue(value, env.sensitive)
      const requiredMark = env.required ? ' (必需)' : ' (可选)'
      
      console.log(`   ${env.key}${requiredMark}: ${displayValue}`)
      
      if (env.required) {
        totalRequired++
        if (value) configuredRequired++
      }
    })
    console.log('')
  })
  
  // 显示统计
  console.log('📊 配置统计:')
  console.log(`   必需配置: ${configuredRequired}/${totalRequired}`)
  console.log(`   完成度: ${Math.round(configuredRequired / totalRequired * 100)}%`)
  
  if (configuredRequired < totalRequired) {
    console.log('\n⚠️  警告: 存在未配置的必需环境变量!')
  } else {
    console.log('\n✅ 所有必需的环境变量都已配置')
  }
  
  console.log('')
}

// 如果直接运行
if (require.main === module) {
  showEnvironmentVariables()
}

module.exports = { showEnvironmentVariables }
