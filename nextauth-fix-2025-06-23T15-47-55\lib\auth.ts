import NextAuth from "next-auth"
import { PrismaAdapter } from "@auth/prisma-adapter"
import { prisma } from "@/lib/db"
import type { ExtendedUser, SessionUser } from "@/types/auth"
import { getEnabledProviders } from "./auth-providers"
import { getAuthConfig, validateAuthConfig } from "./auth-config"

// 验证认证配置
const configValidation = validateAuthConfig()
if (!configValidation.valid) {
  console.error('❌ 认证配置验证失败:', configValidation.errors)
  throw new Error(`认证配置错误: ${configValidation.errors.join(', ')}`)
}

export const { handlers, auth, signIn, signOut } = NextAuth({
  trustHost: true, // 信任代理服务器

  // 使用安全的 cookies 配置
  useSecureCookies: process.env.NODE_ENV === "production",

  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },

  jwt: {
    maxAge: 60 * 60 * 24 * 30, // 30 days
  },

  // 使用动态认证提供商
  providers: getEnabledProviders(),

  callbacks: {
    async redirect({ url, baseUrl }) {
      // 处理重定向逻辑，确保使用正确的域名
      if (url.startsWith("/")) {
        return `${baseUrl}${url}`
      }

      // 如果是相同域名，允许重定向
      if (new URL(url).origin === baseUrl) {
        return url
      }

      // 默认重定向到首页
      return baseUrl
    },
    async jwt({ token, user, account }) {
      // 首次登录时添加用户信息
      if (user && user.id) {
        token.userId = user.id
        token.email = user.email || ''
        token.name = user.name
        token.avatar = user.avatar
        token.roles = await getUserRoles(user.id)
        token.permissions = await getUserPermissions(user.id)
      }

      return token
    },
    
    async session({ session, token }) {
      // 将token信息传递给session
      if (token) {
        const user: SessionUser = {
          id: token.userId as string,
          email: token.email as string,
          name: token.name as string | null,
          avatar: token.avatar as string | null,
          roles: token.roles as string[],
          permissions: token.permissions as string[],
          membershipLevel: getMembershipLevel(token.roles as string[])
        }
        session.user = user as any
      }

      return session
    }
  },
  
  pages: {
    signIn: '/login',
    error: '/auth/error',
  },


  
  events: {
    async signIn({ user, account, profile, isNewUser }) {
      // 登录事件处理
      console.log('User signed in:', user.email)
    },
    
    async signOut() {
      // 登出事件处理
      console.log('User signed out')
    }
  }
})

// 导出 authOptions 以保持向后兼容
export const authOptions = {
  adapter: PrismaAdapter(prisma) as any,
  session: { strategy: "jwt" as const },
  providers: [],
  callbacks: {},
  pages: {
    signIn: '/login',
    signUp: '/register',
    error: '/auth/error',
  }
}

// 获取用户角色
async function getUserRoles(userId: string): Promise<string[]> {
  const userRoles = await prisma.userRole.findMany({
    where: { 
      userId,
      OR: [
        { expiresAt: null },
        { expiresAt: { gt: new Date() } }
      ]
    },
    include: { role: true }
  })
  
  return userRoles.map(ur => ur.role.name)
}

// 获取用户权限
async function getUserPermissions(userId: string): Promise<string[]> {
  const roles = await getUserRoles(userId)
  const permissions = new Set<string>()
  
  for (const roleName of roles) {
    const role = await prisma.role.findUnique({
      where: { name: roleName }
    })
    
    if (role && role.permissions) {
      const rolePermissions = role.permissions as string[]
      rolePermissions.forEach(permission => permissions.add(permission))
    }
  }
  
  return Array.from(permissions)
}

// 获取会员等级
function getMembershipLevel(roles: string[]): SessionUser['membershipLevel'] {
  if (roles.includes('admin') || roles.includes('super_admin')) return 'admin'
  if (roles.includes('diamond')) return 'diamond'
  if (roles.includes('vip')) return 'vip'
  if (roles.includes('registered')) return 'registered'
  return 'guest'
}
