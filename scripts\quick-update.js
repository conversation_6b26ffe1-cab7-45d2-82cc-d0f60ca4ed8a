#!/usr/bin/env node

/**
 * 快速增量更新脚本
 * 针对特定文件的快速更新，无需完整构建
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// 预定义的更新模板
const updateTemplates = {
  // NextAuth 配置更新
  nextauth: {
    name: 'NextAuth 配置更新',
    files: [
      'lib/auth.ts',
      'lib/auth-config.ts',
      'lib/auth-providers.ts'
    ],
    needsBuild: false,
    needsRestart: true,
    instructions: [
      '更新 NextAuth 配置文件',
      '无需重新构建',
      '重启应用即可生效'
    ]
  },

  // 环境变量更新
  env: {
    name: '环境变量更新',
    files: [
      '.env.local',
      '.env.example'
    ],
    needsBuild: false,
    needsRestart: true,
    instructions: [
      '更新环境变量配置',
      '无需重新构建',
      '重启应用即可生效'
    ]
  },

  // Prisma 更新
  prisma: {
    name: 'Prisma 配置更新',
    files: [
      'prisma/schema.prisma',
      'prisma/seed.ts',
      'prisma/seed-roles.ts'
    ],
    needsBuild: false,
    needsRestart: true,
    postUpdate: [
      'npx prisma generate'
    ],
    instructions: [
      '更新 Prisma 配置',
      '重新生成 Prisma 客户端',
      '重启应用'
    ]
  },

  // 组件更新
  components: {
    name: '组件更新',
    files: [
      'components/**/*.tsx',
      'components/**/*.ts'
    ],
    needsBuild: true,
    needsRestart: true,
    instructions: [
      '更新 React 组件',
      '需要重新构建',
      '重启应用'
    ]
  },

  // API 路由更新
  api: {
    name: 'API 路由更新',
    files: [
      'app/api/**/*.ts',
      'lib/**/*.ts'
    ],
    needsBuild: false,
    needsRestart: true,
    instructions: [
      '更新 API 路由',
      '无需重新构建',
      '重启应用即可生效'
    ]
  },

  // 页面更新
  pages: {
    name: '页面更新',
    files: [
      'app/**/*.tsx',
      'app/**/*.ts'
    ],
    needsBuild: true,
    needsRestart: true,
    instructions: [
      '更新页面组件',
      '需要重新构建',
      '重启应用'
    ]
  },

  // 样式更新
  styles: {
    name: '样式更新',
    files: [
      'app/globals.css',
      'components/**/*.css',
      'tailwind.config.js'
    ],
    needsBuild: true,
    needsRestart: true,
    instructions: [
      '更新样式文件',
      '需要重新构建',
      '重启应用'
    ]
  }
}

function showUsage() {
  console.log('🚀 快速增量更新工具')
  console.log('')
  console.log('用法:')
  console.log('  node scripts/quick-update.js <模板名称>')
  console.log('  node scripts/quick-update.js custom <文件1> <文件2> ...')
  console.log('')
  console.log('可用模板:')
  Object.keys(updateTemplates).forEach(key => {
    const template = updateTemplates[key]
    console.log(`  ${key.padEnd(12)} - ${template.name}`)
  })
  console.log('')
  console.log('示例:')
  console.log('  node scripts/quick-update.js nextauth')
  console.log('  node scripts/quick-update.js custom lib/auth.ts .env.local')
}

function expandGlob(pattern) {
  // 简单的 glob 展开（仅支持 **/*.ext 模式）
  if (pattern.includes('**/*')) {
    const [basePath, extension] = pattern.split('**/*')
    const files = []
    
    function scanDir(dir) {
      if (!fs.existsSync(dir)) return
      
      const items = fs.readdirSync(dir)
      items.forEach(item => {
        const fullPath = path.join(dir, item)
        const stat = fs.statSync(fullPath)
        
        if (stat.isDirectory()) {
          scanDir(fullPath)
        } else if (fullPath.endsWith(extension)) {
          files.push(fullPath)
        }
      })
    }
    
    scanDir(basePath || '.')
    return files
  }
  
  return [pattern]
}

function createQuickUpdate(templateName, customFiles = []) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19)
  const outputDir = `quick-update-${timestamp}`
  
  let template
  let filesToUpdate = []

  if (templateName === 'custom') {
    template = {
      name: '自定义更新',
      files: customFiles,
      needsBuild: true,
      needsRestart: true,
      instructions: ['自定义文件更新']
    }
  } else {
    template = updateTemplates[templateName]
    if (!template) {
      console.log(`❌ 未知模板: ${templateName}`)
      showUsage()
      return
    }
  }

  console.log(`🔄 创建 ${template.name} 更新包...`)

  // 展开文件列表
  template.files.forEach(filePattern => {
    const expandedFiles = expandGlob(filePattern)
    expandedFiles.forEach(file => {
      if (fs.existsSync(file)) {
        filesToUpdate.push(file)
      }
    })
  })

  if (filesToUpdate.length === 0) {
    console.log('❌ 没有找到要更新的文件')
    return
  }

  // 创建输出目录
  fs.mkdirSync(outputDir, { recursive: true })

  console.log('📁 复制文件...')
  filesToUpdate.forEach(file => {
    const destPath = path.join(outputDir, file)
    const destDir = path.dirname(destPath)
    
    if (!fs.existsSync(destDir)) {
      fs.mkdirSync(destDir, { recursive: true })
    }
    
    fs.copyFileSync(file, destPath)
    console.log(`   ✓ ${file}`)
  })

  // 创建更新清单
  const manifest = {
    type: 'quick-update',
    template: templateName,
    name: template.name,
    timestamp,
    files: filesToUpdate,
    needsBuild: template.needsBuild,
    needsRestart: template.needsRestart,
    postUpdate: template.postUpdate || [],
    instructions: template.instructions
  }

  fs.writeFileSync(
    path.join(outputDir, 'update-manifest.json'),
    JSON.stringify(manifest, null, 2)
  )

  // 创建应用脚本
  createQuickUpdateScript(outputDir, manifest)

  console.log(`✅ 快速更新包已创建: ${outputDir}`)
  console.log('')
  console.log('📋 使用说明:')
  console.log(`1. 上传 ${outputDir} 目录到服务器`)
  console.log('2. 在服务器上运行: ./apply-quick-update.sh')
  console.log('')
  
  return outputDir
}

function createQuickUpdateScript(outputDir, manifest) {
  const script = `#!/bin/bash

# 快速更新应用脚本
echo "🚀 应用快速更新: ${manifest.name}"
echo "时间: ${manifest.timestamp}"
echo ""

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 备份文件
echo "📁 备份原文件..."
BACKUP_DIR="backup-quick-$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

${manifest.files.map(file => 
  `[ -f "${file}" ] && cp -p "${file}" "$BACKUP_DIR/" || true`
).join('\n')}

echo "✅ 备份完成: $BACKUP_DIR"

# 停止应用（如果需要重启）
${manifest.needsRestart ? `
echo "🛑 停止应用..."
pkill -f "next start" || true
sleep 2
` : ''}

# 复制新文件
echo "📋 更新文件..."
${manifest.files.map(file => 
  `cp "${file}" "${file}"`
).join('\n')}

echo "✅ 文件更新完成"

# 执行后续命令
${manifest.postUpdate.map(cmd => `
echo "🔧 执行: ${cmd}"
${cmd}
`).join('')}

# 重新构建（如果需要）
${manifest.needsBuild ? `
echo "🔨 重新构建..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ 构建失败"
    exit 1
fi
` : ''}

# 重启应用（如果需要）
${manifest.needsRestart ? `
echo "🚀 重启应用..."
npm start &
sleep 3

# 检查应用是否启动成功
if pgrep -f "next start" > /dev/null; then
    echo "✅ 应用启动成功"
else
    echo "❌ 应用启动失败，请检查日志"
fi
` : ''}

echo ""
echo "🎉 快速更新完成!"
echo "备份位置: $BACKUP_DIR"
`

  fs.writeFileSync(path.join(outputDir, 'apply-quick-update.sh'), script)
  
  // 设置执行权限
  try {
    execSync(`chmod +x "${path.join(outputDir, 'apply-quick-update.sh')}"`)
  } catch {}
}

// 命令行处理
if (require.main === module) {
  const args = process.argv.slice(2)
  
  if (args.length === 0) {
    showUsage()
    process.exit(1)
  }
  
  const templateName = args[0]
  const customFiles = args.slice(1)
  
  createQuickUpdate(templateName, customFiles)
}

module.exports = { createQuickUpdate, updateTemplates }
