#!/bin/bash

# 冒险岛情报站 - 服务器初始化脚本
# 适用于 Ubuntu 20.04/22.04

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
DOMAIN=""
EMAIL=""
USE_NGINX=true
POSTGRES_PASSWORD=""
REDIS_PASSWORD=""

# 显示帮助信息
show_help() {
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -d, --domain DOMAIN        设置域名"
    echo "  -e, --email EMAIL          设置邮箱（用于SSL证书）"
    echo "  --no-nginx                 不安装Nginx"
    echo "  --postgres-password PWD    设置PostgreSQL密码"
    echo "  --redis-password PWD       设置Redis密码"
    echo "  -h, --help                 显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -d example.com -e <EMAIL>"
    echo "  $0 --no-nginx --postgres-password mypass123"
}

# 参数解析
while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--domain)
            DOMAIN="$2"
            shift 2
            ;;
        -e|--email)
            EMAIL="$2"
            shift 2
            ;;
        --no-nginx)
            USE_NGINX=false
            shift
            ;;
        --postgres-password)
            POSTGRES_PASSWORD="$2"
            shift 2
            ;;
        --redis-password)
            REDIS_PASSWORD="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
    exit 1
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO:${NC} $1"
}

# 检查root权限
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "此脚本需要root权限运行"
    fi
}

# 生成随机密码
generate_password() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-25
}

# 更新系统
update_system() {
    log "更新系统..."
    apt update && apt upgrade -y
    apt install -y curl wget git unzip software-properties-common build-essential
}

# 配置防火墙
setup_firewall() {
    log "配置防火墙..."
    ufw --force enable
    ufw allow ssh
    
    if [ "$USE_NGINX" = true ]; then
        ufw allow 80
        ufw allow 443
    else
        ufw allow 3000
    fi
    
    ufw status
}

# 创建应用用户
create_app_user() {
    log "创建应用用户..."
    
    if ! id "maplestory" &>/dev/null; then
        adduser --disabled-password --gecos "" maplestory
        usermod -aG sudo maplestory
        
        # 创建必要目录
        sudo -u maplestory mkdir -p /home/<USER>/{app,logs,backups}
        
        log "用户 maplestory 创建成功"
    else
        info "用户 maplestory 已存在"
    fi
}

# 安装Node.js
install_nodejs() {
    log "安装 Node.js..."
    
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    apt-get install -y nodejs
    
    # 安装PM2
    npm install -g pm2
    
    # 验证安装
    node --version
    npm --version
    pm2 --version
}

# 安装PostgreSQL
install_postgresql() {
    log "安装 PostgreSQL..."
    
    apt install -y postgresql postgresql-contrib
    systemctl start postgresql
    systemctl enable postgresql
    
    # 设置密码
    if [ -z "$POSTGRES_PASSWORD" ]; then
        POSTGRES_PASSWORD=$(generate_password)
        warning "PostgreSQL密码已自动生成: $POSTGRES_PASSWORD"
    fi
    
    # 配置数据库
    sudo -u postgres psql << EOF
CREATE DATABASE mxd_info_db;
CREATE USER maplestory WITH PASSWORD '$POSTGRES_PASSWORD';
GRANT ALL PRIVILEGES ON DATABASE mxd_info_db TO maplestory;
ALTER USER maplestory CREATEDB;
\q
EOF
    
    # 配置连接
    PG_VERSION=$(sudo -u postgres psql -t -c "SELECT version();" | grep -oP '\d+\.\d+' | head -1)
    PG_CONFIG_DIR="/etc/postgresql/$PG_VERSION/main"
    
    # 允许本地连接
    echo "local   all   maplestory   md5" >> "$PG_CONFIG_DIR/pg_hba.conf"
    
    systemctl restart postgresql
    
    log "PostgreSQL 安装完成"
}

# 安装Redis
install_redis() {
    log "安装 Redis..."
    
    apt install -y redis-server
    
    # 设置密码
    if [ -z "$REDIS_PASSWORD" ]; then
        REDIS_PASSWORD=$(generate_password)
        warning "Redis密码已自动生成: $REDIS_PASSWORD"
    fi
    
    # 配置Redis
    sed -i "s/# requirepass foobared/requirepass $REDIS_PASSWORD/" /etc/redis/redis.conf
    sed -i "s/bind 127.0.0.1/bind 127.0.0.1/" /etc/redis/redis.conf
    
    systemctl restart redis-server
    systemctl enable redis-server
    
    log "Redis 安装完成"
}

# 安装Nginx
install_nginx() {
    if [ "$USE_NGINX" = false ]; then
        info "跳过 Nginx 安装"
        return
    fi
    
    log "安装 Nginx..."
    
    apt install -y nginx
    systemctl start nginx
    systemctl enable nginx
    
    # 删除默认配置
    rm -f /etc/nginx/sites-enabled/default
    
    log "Nginx 安装完成"
}

# 配置SSL证书
setup_ssl() {
    if [ "$USE_NGINX" = false ] || [ -z "$DOMAIN" ] || [ -z "$EMAIL" ]; then
        info "跳过 SSL 配置"
        return
    fi
    
    log "配置 SSL 证书..."
    
    # 安装Certbot
    apt install -y certbot python3-certbot-nginx
    
    # 获取证书
    certbot --nginx -d "$DOMAIN" -d "www.$DOMAIN" --email "$EMAIL" --agree-tos --non-interactive
    
    # 设置自动续期
    (crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -
    
    log "SSL 证书配置完成"
}

# 创建Nginx配置
create_nginx_config() {
    if [ "$USE_NGINX" = false ] || [ -z "$DOMAIN" ]; then
        info "跳过 Nginx 配置创建"
        return
    fi
    
    log "创建 Nginx 配置..."
    
    cat > "/etc/nginx/sites-available/maplestory" << EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN www.$DOMAIN;
    
    # SSL配置将由Certbot自动添加
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 静态文件
    location /_next/static/ {
        alias /home/<USER>/app/.next/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 主应用
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    location /health {
        access_log off;
        return 200 "healthy\\n";
        add_header Content-Type text/plain;
    }
}
EOF
    
    # 启用配置
    ln -sf /etc/nginx/sites-available/maplestory /etc/nginx/sites-enabled/
    
    # 测试配置
    nginx -t
    systemctl reload nginx
    
    log "Nginx 配置创建完成"
}

# 创建环境变量模板
create_env_template() {
    log "创建环境变量模板..."
    
    cat > "/home/<USER>/app/.env.production.template" << EOF
# 数据库配置
DATABASE_URL="postgresql://maplestory:$POSTGRES_PASSWORD@localhost:5432/mxd_info_db"
REDIS_URL="redis://:$REDIS_PASSWORD@localhost:6379"

# NextAuth配置
NEXTAUTH_URL="https://$DOMAIN"
NEXTAUTH_SECRET="$(generate_password)"

# 邮件服务 (需要配置)
RESEND_API_KEY="re_your-production-resend-api-key"
EMAIL_FROM="noreply@$DOMAIN"

# 第三方服务 (需要配置)
NEXT_PUBLIC_FINGERPRINT_JS_API_KEY="your-production-fingerprint-js-key"
STRIPE_SECRET_KEY="sk_live_your-production-stripe-key"
STRIPE_WEBHOOK_SECRET="whsec_your-production-webhook-secret"

# 安全配置
JWT_SECRET="$(generate_password)"
ENCRYPTION_KEY="$(generate_password)"

# 应用配置
NEXT_PUBLIC_APP_URL="https://$DOMAIN"
NEXT_PUBLIC_APP_NAME="冒险岛情报站"

# 生产配置
NODE_ENV="production"
LOG_LEVEL="info"

# 虚拟货币配置
CURRENCY_NAME="欢乐豆"
DEFAULT_CURRENCY_AMOUNT="100"

# 功能开关
ENABLE_REGISTRATION="true"
ENABLE_EMAIL_VERIFICATION="true"
ENABLE_FINGERPRINT_TRACKING="true"
EOF
    
    chown maplestory:maplestory /home/<USER>/app/.env.production.template
    
    log "环境变量模板创建完成"
}

# 设置监控
setup_monitoring() {
    log "设置系统监控..."
    
    # 安装监控工具
    apt install -y htop iotop nethogs fail2ban
    
    # 配置fail2ban
    cat > "/etc/fail2ban/jail.local" << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true
logpath = /var/log/auth.log

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
logpath = /var/log/nginx/error.log
maxretry = 3
EOF
    
    systemctl restart fail2ban
    systemctl enable fail2ban
    
    log "监控设置完成"
}

# 显示完成信息
show_completion_info() {
    log "服务器初始化完成！"
    echo ""
    echo "=== 配置信息 ==="
    echo "PostgreSQL 密码: $POSTGRES_PASSWORD"
    echo "Redis 密码: $REDIS_PASSWORD"
    echo "应用目录: /home/<USER>/app"
    echo "日志目录: /home/<USER>/logs"
    echo "备份目录: /home/<USER>/backups"
    echo ""
    echo "=== 下一步操作 ==="
    echo "1. 切换到应用用户: sudo su - maplestory"
    echo "2. 克隆应用代码到 /home/<USER>/app"
    echo "3. 复制并编辑环境变量: cp .env.production.template .env.production"
    echo "4. 运行部署脚本: ./scripts/deploy.sh"
    echo ""
    if [ "$USE_NGINX" = true ] && [ -n "$DOMAIN" ]; then
        echo "网站地址: https://$DOMAIN"
    else
        echo "应用地址: http://your-server-ip:3000"
    fi
}

# 主函数
main() {
    log "开始服务器初始化..."
    
    check_root
    update_system
    setup_firewall
    create_app_user
    install_nodejs
    install_postgresql
    install_redis
    install_nginx
    create_nginx_config
    setup_ssl
    create_env_template
    setup_monitoring
    show_completion_info
}

# 执行主函数
main "$@"
