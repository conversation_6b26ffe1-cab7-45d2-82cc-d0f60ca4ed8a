@echo off
echo Starting MapleStory Info Station...
echo.

REM Check Node.js
node -v >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    echo Please install Node.js 18.17+ from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js version:
node -v

REM Check if in correct directory
if not exist "package.json" (
    echo ERROR: package.json not found
    echo Please run this script in the correct project directory
    pause
    exit /b 1
)

REM Check if built
if not exist ".next" (
    echo ERROR: .next directory not found
    echo Please ensure the project is built
    pause
    exit /b 1
)

REM Install dependencies if needed
if not exist "node_modules" (
    echo Installing dependencies...
    npm install --production
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Create .env.local if not exists
if not exist ".env.local" (
    echo Creating .env.local file...
    echo DATABASE_URL="postgresql://postgres:password@localhost:5432/mxd_info_db" > .env.local
    echo NEXTAUTH_URL="http://localhost:3000" >> .env.local
    echo NEXTAUTH_SECRET="your-secret-key-change-this" >> .env.local
    echo NODE_ENV="production" >> .env.local
    echo PORT="3000" >> .env.local
    echo.
    echo Created .env.local file. Please edit it with your configuration.
)

REM Generate Prisma client if needed
if exist "prisma" (
    echo Generating Prisma client...
    npx prisma generate
)

REM Set environment
set NODE_ENV=production
set PORT=3000

echo.
echo Starting application on http://localhost:3000
echo Press Ctrl+C to stop
echo.

REM Start the application using npx
npx next start -p 3000

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to start application
    echo Please check:
    echo 1. Environment variables in .env.local
    echo 2. Database connection
    echo 3. Port 3000 is not in use
    pause
)

echo.
echo Application stopped
pause
