import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import type { Prisma } from '@prisma/client'

// 定义验证令牌类型
type VerificationToken = {
  identifier: string
  token: string
  expires: Date
}

// 清理过期验证令牌的 API
export async function POST(request: NextRequest) {
  console.log('🧹 开始清理过期验证令牌')

  try {
    const now = new Date()
    
    // 查找过期的令牌
    const expiredTokens = await prisma.verificationToken.findMany({
      where: {
        expires: {
          lt: now
        }
      }
    })

    console.log(`🔍 找到 ${expiredTokens.length} 个过期令牌`)

    if (expiredTokens.length === 0) {
      return NextResponse.json({
        success: true,
        message: '没有过期令牌需要清理',
        deletedCount: 0
      })
    }

    // 删除过期令牌
    const deleteResult = await prisma.verificationToken.deleteMany({
      where: {
        expires: {
          lt: now
        }
      }
    })

    console.log(`✅ 成功删除 ${deleteResult.count} 个过期令牌`)

    return NextResponse.json({
      success: true,
      message: `成功清理 ${deleteResult.count} 个过期令牌`,
      deletedCount: deleteResult.count,
      expiredTokens: expiredTokens.map((token: VerificationToken) => ({
        identifier: token.identifier,
        expires: token.expires,
        expiredHours: Math.floor((now.getTime() - token.expires.getTime()) / (1000 * 60 * 60))
      }))
    })

  } catch (error) {
    console.error('❌ 清理过期令牌失败:', error)
    return NextResponse.json(
      { error: '清理过期令牌失败' },
      { status: 500 }
    )
  }
}

// 获取过期令牌统计信息
export async function GET(request: NextRequest) {
  console.log('📊 获取过期令牌统计信息')

  try {
    const now = new Date()
    
    // 统计过期令牌
    const expiredCount = await prisma.verificationToken.count({
      where: {
        expires: {
          lt: now
        }
      }
    })

    // 统计有效令牌
    const validCount = await prisma.verificationToken.count({
      where: {
        expires: {
          gte: now
        }
      }
    })

    // 获取最近过期的令牌信息
    const recentExpired = await prisma.verificationToken.findMany({
      where: {
        expires: {
          lt: now,
          gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) // 最近7天过期的
        }
      },
      orderBy: {
        expires: 'desc'
      },
      take: 10
    })

    return NextResponse.json({
      success: true,
      statistics: {
        expiredCount,
        validCount,
        totalCount: expiredCount + validCount,
        recentExpired: recentExpired.map((token: VerificationToken) => ({
          identifier: token.identifier,
          expires: token.expires,
          expiredHours: Math.floor((now.getTime() - token.expires.getTime()) / (1000 * 60 * 60))
        }))
      }
    })

  } catch (error) {
    console.error('❌ 获取令牌统计信息失败:', error)
    return NextResponse.json(
      { error: '获取统计信息失败' },
      { status: 500 }
    )
  }
}
