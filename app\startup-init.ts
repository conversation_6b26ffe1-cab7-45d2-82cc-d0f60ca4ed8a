/**
 * 应用启动初始化
 * 在应用启动时导入必要的模块和执行初始化逻辑
 */

// 防止重复初始化
let isInitialized = false

// 导入启动日志（仅在服务器端执行，且仅在运行时）
if (typeof window === 'undefined' &&
    !isInitialized &&
    process.env.NODE_ENV !== 'test' &&
    process.argv.includes('start')) { // 仅在 npm start 时执行

  isInitialized = true

  import('../lib/startup-logger')
    .then(({ logStartupInfo }) => {
      // 手动调用启动日志
      setTimeout(() => {
        logStartupInfo()
      }, 500)
    })
    .catch((error) => {
      console.warn('⚠️ 启动日志模块加载失败:', error.message)
    })
}

export {}
