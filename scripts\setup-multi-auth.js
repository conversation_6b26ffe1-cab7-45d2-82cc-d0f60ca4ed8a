#!/usr/bin/env node

/**
 * 多认证方式增强功能安装脚本
 * 自动配置环境变量和数据库迁移
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🚀 开始安装多认证方式增强功能...\n')

// 检查必要的文件
function checkRequiredFiles() {
  console.log('📋 检查必要文件...')
  
  const requiredFiles = [
    'lib/auth-config.ts',
    'lib/auth-providers.ts',
    'components/auth/EnhancedLoginForm.tsx',
    'components/auth/EnhancedRegisterForm.tsx',
    'app/api/auth/register-username/route.ts'
  ]
  
  const missingFiles = requiredFiles.filter(file => !fs.existsSync(file))
  
  if (missingFiles.length > 0) {
    console.error('❌ 缺少必要文件:')
    missingFiles.forEach(file => console.error(`   - ${file}`))
    process.exit(1)
  }
  
  console.log('✅ 所有必要文件都存在\n')
}

// 检查环境变量配置
function checkEnvironmentConfig() {
  console.log('🔧 检查环境变量配置...')
  
  const envFile = '.env.local'
  const envExampleFile = '.env.example'
  
  if (!fs.existsSync(envFile)) {
    if (fs.existsSync(envExampleFile)) {
      console.log(`📝 复制 ${envExampleFile} 到 ${envFile}`)
      fs.copyFileSync(envExampleFile, envFile)
      console.log('⚠️  请编辑 .env.local 文件并配置必要的环境变量')
    } else {
      console.error('❌ 未找到 .env.example 文件')
      process.exit(1)
    }
  }
  
  // 检查关键环境变量
  const envContent = fs.readFileSync(envFile, 'utf8')
  const requiredVars = [
    'DATABASE_URL',
    'NEXTAUTH_URL',
    'NEXTAUTH_SECRET'
  ]
  
  const missingVars = requiredVars.filter(varName => 
    !envContent.includes(`${varName}=`) || 
    envContent.includes(`${varName}="your-`) ||
    envContent.includes(`${varName}="change-this`)
  )
  
  if (missingVars.length > 0) {
    console.warn('⚠️  以下环境变量需要配置:')
    missingVars.forEach(varName => console.warn(`   - ${varName}`))
    console.warn('   请编辑 .env.local 文件并设置正确的值\n')
  } else {
    console.log('✅ 环境变量配置检查通过\n')
  }
}

// 安装必要的依赖
function installDependencies() {
  console.log('📦 检查并安装必要的依赖...')
  
  const packageJsonPath = 'package.json'
  if (!fs.existsSync(packageJsonPath)) {
    console.error('❌ 未找到 package.json 文件')
    process.exit(1)
  }
  
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
  const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies }
  
  const requiredDeps = [
    'next-auth',
    '@auth/prisma-adapter',
    '@radix-ui/react-tabs'
  ]
  
  const missingDeps = requiredDeps.filter(dep => !dependencies[dep])
  
  if (missingDeps.length > 0) {
    console.log('📥 安装缺少的依赖包...')
    try {
      execSync(`npm install ${missingDeps.join(' ')}`, { stdio: 'inherit' })
      console.log('✅ 依赖包安装完成\n')
    } catch (error) {
      console.error('❌ 依赖包安装失败:', error.message)
      process.exit(1)
    }
  } else {
    console.log('✅ 所有必要的依赖包都已安装\n')
  }
}

// 执行数据库迁移
function runDatabaseMigration() {
  console.log('🗄️  执行数据库迁移...')
  
  try {
    // 检查 Prisma 是否可用
    execSync('npx prisma --version', { stdio: 'pipe' })
    
    // 生成 Prisma 客户端
    console.log('📝 生成 Prisma 客户端...')
    execSync('npx prisma generate', { stdio: 'inherit' })
    
    // 执行数据库迁移
    console.log('🔄 执行数据库迁移...')
    execSync('npx prisma migrate dev --name add-username-support', { stdio: 'inherit' })
    
    console.log('✅ 数据库迁移完成\n')
  } catch (error) {
    console.warn('⚠️  数据库迁移失败，请手动执行:')
    console.warn('   npx prisma migrate dev --name add-username-support')
    console.warn('   或者执行 prisma/migrations/add_username_support.sql 中的 SQL 语句\n')
  }
}

// 验证安装
function validateInstallation() {
  console.log('🔍 验证安装结果...')
  
  try {
    // 检查 TypeScript 编译
    console.log('📝 检查 TypeScript 编译...')
    execSync('npx tsc --noEmit', { stdio: 'pipe' })
    console.log('✅ TypeScript 编译检查通过')
    
    // 检查 Next.js 构建
    console.log('🏗️  检查 Next.js 构建...')
    execSync('npm run build', { stdio: 'pipe' })
    console.log('✅ Next.js 构建检查通过')
    
  } catch (error) {
    console.warn('⚠️  构建检查失败，请检查代码是否有错误')
    console.warn('   运行 npm run build 查看详细错误信息')
  }
  
  console.log('\n🎉 多认证方式增强功能安装完成！')
}

// 显示后续步骤
function showNextSteps() {
  console.log('\n📋 后续步骤:')
  console.log('1. 编辑 .env.local 文件，配置必要的环境变量')
  console.log('2. 如果要启用 Google OAuth，请配置 GOOGLE_CLIENT_ID 和 GOOGLE_CLIENT_SECRET')
  console.log('3. 根据需要调整 ENABLED_AUTH_PROVIDERS 环境变量')
  console.log('4. 运行 npm run dev 启动开发服务器')
  console.log('5. 访问 /login 和 /register 页面测试新功能')
  console.log('\n📚 详细文档请参考: 多认证方式增强实现指南.md')
}

// 主函数
function main() {
  try {
    checkRequiredFiles()
    checkEnvironmentConfig()
    installDependencies()
    runDatabaseMigration()
    validateInstallation()
    showNextSteps()
  } catch (error) {
    console.error('\n❌ 安装过程中发生错误:', error.message)
    process.exit(1)
  }
}

// 运行脚本
if (require.main === module) {
  main()
}
