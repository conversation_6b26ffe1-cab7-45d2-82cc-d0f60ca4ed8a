import { NextRequest, NextResponse } from 'next/server'
import { hash } from 'bcryptjs'
import { z } from 'zod'
import { prisma } from '@/lib/db'
import { sendVerificationEmail } from '@/lib/email'
import { generateVerificationToken } from '@/lib/tokens'

const registerSchema = z.object({
  name: z.string().min(2).max(50),
  email: z.string().email(),
  password: z.string().min(8).regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
})

export async function POST(request: NextRequest) {
  console.log('📝 开始处理用户注册请求')

  try {
    const body = await request.json()
    console.log('📝 接收到注册数据:', {
      name: body.name,
      email: body.email,
      passwordLength: body.password?.length
    })

    const { name, email, password } = registerSchema.parse(body)
    console.log('✅ 数据验证通过')

    // 检查用户是否已存在
    console.log('🔍 检查用户是否已存在:', email)
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      console.log('❌ 用户已存在:', email)

      // 如果用户已验证，直接返回错误
      if (existingUser.emailVerified) {
        return NextResponse.json(
          { error: '该邮箱已被注册并已激活，请直接登录' },
          { status: 400 }
        )
      }

      // 如果用户未验证，提供重新发送激活邮件的选项
      console.log('⚠️ 用户已注册但未激活:', email)
      return NextResponse.json(
        {
          error: '该邮箱已注册但未激活',
          message: '该邮箱已注册但未激活，请查收验证邮件或重新发送激活邮件',
          canResendEmail: true,
          email: email
        },
        { status: 409 } // 409 Conflict 状态码表示资源冲突
      )
    }

    console.log('✅ 邮箱可用，继续注册流程')

    // 加密密码
    console.log('🔐 开始加密密码')
    const hashedPassword = await hash(password, 12)
    console.log('✅ 密码加密完成')

    // 创建用户
    console.log('👤 开始创建用户记录')
    const user = await prisma.user.create({
      data: {
        name,
        email,
        hashedPassword,
        isActive: true,
        emailVerified: null // 需要邮箱验证
      }
    })
    console.log('✅ 用户创建成功:', { id: user.id, email: user.email })

    // 为新用户分配注册用户角色
    console.log('🎭 开始分配用户角色')
    const registeredRole = await prisma.role.findUnique({
      where: { name: 'registered' }
    })

    if (registeredRole) {
      await prisma.userRole.create({
        data: {
          userId: user.id,
          roleId: registeredRole.id,
          grantedAt: new Date()
        }
      })
      console.log('✅ 用户角色分配成功: registered')
    } else {
      console.log('⚠️ 未找到 registered 角色')
    }

    // 创建虚拟货币余额
    console.log('💰 开始创建虚拟货币余额')
    await prisma.currencyBalance.create({
      data: {
        userId: user.id,
        balance: 100, // 新用户赠送100欢乐豆
        frozenBalance: 0,
        totalEarned: 100,
        totalSpent: 0
      }
    })
    console.log('✅ 虚拟货币余额创建成功: 100欢乐豆')

    // 生成验证令牌并发送邮件
    console.log('🎫 开始生成验证令牌')
    const verificationToken = generateVerificationToken()
    console.log('✅ 验证令牌生成成功:', verificationToken.substring(0, 8) + '...')

    // 存储验证令牌
    console.log('💾 开始存储验证令牌到数据库')
    await prisma.verificationToken.create({
      data: {
        identifier: email,
        token: verificationToken,
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时后过期
      }
    })
    console.log('✅ 验证令牌存储成功')

    // 发送验证邮件
    console.log('📧 开始发送验证邮件到:', email)
    try {
      await sendVerificationEmail(email, verificationToken)
      console.log('✅ 验证邮件发送成功')
    } catch (emailError) {
      console.error('❌ 验证邮件发送失败:', emailError)
      // 邮件发送失败不应该阻止注册流程
    }

    console.log('🎉 用户注册流程完成')
    return NextResponse.json({
      success: true,
      message: '注册成功，请查收验证邮件'
    })

  } catch (error) {
    console.error('❌ 注册过程发生错误:', error)

    if (error instanceof z.ZodError) {
      console.error('❌ 数据验证错误:', error.errors)
      return NextResponse.json(
        { error: '输入数据格式错误' },
        { status: 400 }
      )
    }

    console.error('❌ 注册失败，未知错误:', error)
    return NextResponse.json(
      { error: '注册失败，请稍后重试' },
      { status: 500 }
    )
  }
}
