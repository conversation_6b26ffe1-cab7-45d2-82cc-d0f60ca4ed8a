# 路由结构：

```mermaid
graph TD
A[app/] --> B[layout.tsx<br/>根布局 + SessionProvider]
A --> C[page.tsx<br/>首页 /]
A --> D["(auth)/<br/>认证路由组"]
A --> E["(dashboard)/<br/>仪表板路由组"]
A --> F[tools/<br/>工具页面]
A --> G[cms-216/<br/>道具展示]

D --> D1[layout.tsx<br/>认证页面布局]
D --> D2[login/page.tsx<br/>登录页面 /login]
D --> D3[register/page.tsx<br/>注册页面 /register]
D --> D4[reset-password/page.tsx<br/>密码重置 /reset-password]
D --> D5[verify-email/page.tsx<br/>邮箱验证 /verify-email]

E --> E1[layout.tsx<br/>仪表板布局 + 权限检查]
E --> E2[dashboard/page.tsx<br/>仪表板首页 /dashboard]
E --> E3[profile/page.tsx<br/>个人资料 /profile]
E --> E4[transactions/page.tsx<br/>交易记录 /transactions]
E --> E5[settings/page.tsx<br/>设置页面 /settings]

F --> F1[page.tsx<br/>工具首页 /tools]
F --> F2[enhancement/<br/>强化模拟器]

G --> G1[page.tsx<br/>道具展示 /cms-216]

style D fill:#e1f5fe
style E fill:#f3e5f5
style D1 fill:#e1f5fe
style E1 fill:#f3e5f5
```


### 🏛️ 认证架构图

```mermaid
graph TB
    subgraph "客户端"
        A[登录表单] --> B[signIn 调用]
        C[useSession Hook] --> D[用户状态]
        E[HTTP-Only Cookie] --> F[自动携带认证]
    end
    
    subgraph "NextAuth.js 中间层"
        G[Credentials Provider] --> H[用户验证]
        I[JWT Callback] --> J[Token 生成]
        K[Session Callback] --> L[Session 构造]
    end
    
    subgraph "服务端"
        M[auth 函数] --> N[服务端组件认证]
        O[API 路由保护] --> P[权限验证]
        Q[中间件拦截] --> R[路由保护]
    end
    
    subgraph "数据层"
        S[(PostgreSQL)] --> T[用户数据]
        U[(Redis)] --> V[缓存会话]
    end
    
    B --> G
    H --> S
    J --> E
    L --> C
    F --> M
    F --> O
    F --> Q
```
