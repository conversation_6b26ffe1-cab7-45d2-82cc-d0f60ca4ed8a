# Prisma 查询引擎错误解决方案

## 🔍 错误描述

```
thread '<unnamed>' panicked at query-engine/query-engine-node-api/src/engine.rs:76:45:
Failed to deserialize constructor options.
missing field `enableTracing`
```

## 🎯 根本原因

1. **Prisma 客户端版本不匹配**：开发环境和生产环境的 Prisma 版本不一致
2. **查询引擎二进制文件缺失**：Rocky Linux 环境缺少对应的查询引擎文件
3. **环境变量配置问题**：缺少必要的 Prisma 配置
4. **构建目标不匹配**：Prisma 客户端没有为 Rocky Linux 生成正确的二进制文件

## ✅ 解决方案

### 🚀 快速解决（推荐）

在您的 Rocky Linux 服务器上运行：

```bash
# 方法一：使用快速修复脚本
chmod +x fix-prisma-error.sh
./fix-prisma-error.sh

# 方法二：完全重置 Prisma
chmod +x reset-prisma.sh
./reset-prisma.sh
```

### 🔧 手动解决步骤

#### 1. 停止应用并清理
```bash
# 停止应用
pkill -f "next start" || true

# 清理 Prisma 文件
rm -rf node_modules/.prisma
rm -rf node_modules/@prisma
```

#### 2. 重新安装 Prisma
```bash
# 重新安装最新版本
npm uninstall @prisma/client prisma
npm install @prisma/client@latest prisma@latest
```

#### 3. 更新 Prisma Schema
确保 `prisma/schema.prisma` 包含正确的 binaryTargets：

```prisma
generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "rhel-openssl-1.0.x"]
}
```

#### 4. 生成客户端
```bash
# 强制重新生成
npx prisma generate --force
```

#### 5. 配置环境变量
创建或更新 `.env.local`：

```bash
# 数据库配置
DATABASE_URL="postgresql://postgres:password@localhost:5432/mxd_info_db"

# NextAuth 配置
NEXTAUTH_URL="https://mxd.hyhuman.xyz"
NEXTAUTH_SECRET="your-secret-key"

# 应用配置
NODE_ENV="production"
PORT="3000"

# Prisma 配置
PRISMA_QUERY_ENGINE_LIBRARY="/root/svc/mxd/mxd-tool/node_modules/.prisma/client/libquery_engine-rhel-openssl-1.0.x.so.node"
PRISMA_QUERY_ENGINE_BINARY="/root/svc/mxd/mxd-tool/node_modules/.prisma/client/query-engine-rhel-openssl-1.0.x"

# 禁用遥测
CHECKPOINT_DISABLE="1"
```

#### 6. 测试 Prisma
```bash
# 运行测试脚本
node test-prisma.js

# 或手动测试
npx prisma db pull --force
```

#### 7. 启动应用
```bash
# 使用详细输出启动
RUST_BACKTRACE=1 pnpm start
```

## 🔍 故障排除

### 检查 Prisma 文件
```bash
# 检查客户端文件
ls -la node_modules/.prisma/client/

# 应该包含以下文件：
# - index.js
# - libquery_engine-rhel-openssl-1.0.x.so.node
# - query-engine-rhel-openssl-1.0.x
```

### 检查环境
```bash
# 检查 Node.js 版本
node --version  # 需要 18.17+

# 检查 Prisma 版本
npx prisma --version

# 检查数据库连接
npx prisma db pull --force
```

### 常见问题

#### 1. 查询引擎文件缺失
```bash
# 重新生成并指定目标
npx prisma generate --force
```

#### 2. 权限问题
```bash
# 检查文件权限
chmod +x node_modules/.prisma/client/query-engine-*
```

#### 3. 环境变量问题
```bash
# 检查环境变量加载
printenv | grep PRISMA
```

#### 4. 数据库连接问题
```bash
# 测试数据库连接
psql "postgresql://postgres:password@localhost:5432/mxd_info_db" -c "SELECT 1;"
```

## 📋 预防措施

### 1. 统一环境
- 在开发环境中也使用相同的 binaryTargets
- 定期同步 Prisma 版本

### 2. 部署检查清单
- [ ] Prisma 客户端已生成
- [ ] 查询引擎文件存在
- [ ] 环境变量配置正确
- [ ] 数据库连接正常
- [ ] Node.js 版本兼容

### 3. 监控脚本
创建健康检查脚本：

```bash
#!/bin/bash
# health-check.sh

echo "🔍 Prisma 健康检查..."

# 检查客户端文件
if [ -f "node_modules/.prisma/client/index.js" ]; then
    echo "✅ Prisma 客户端文件存在"
else
    echo "❌ Prisma 客户端文件缺失"
    exit 1
fi

# 检查查询引擎
if ls node_modules/.prisma/client/libquery_engine-*.so.node 1> /dev/null 2>&1; then
    echo "✅ 查询引擎库文件存在"
else
    echo "❌ 查询引擎库文件缺失"
    exit 1
fi

# 测试数据库连接
if npx prisma db pull --force > /dev/null 2>&1; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接失败"
    exit 1
fi

echo "🎉 Prisma 健康检查通过"
```

## 🚀 自动化部署

### 部署脚本集成
在部署脚本中添加 Prisma 检查：

```bash
# 在启动前检查 Prisma
if ! ./health-check.sh; then
    echo "Prisma 检查失败，运行修复脚本..."
    ./fix-prisma-error.sh
fi

# 启动应用
pnpm start
```

### Docker 部署（可选）
如果问题持续存在，考虑使用 Docker：

```dockerfile
FROM node:18-alpine

# 安装 Prisma 依赖
RUN apk add --no-cache openssl

WORKDIR /app
COPY package*.json ./
RUN npm install

COPY . .
RUN npx prisma generate

EXPOSE 3000
CMD ["npm", "start"]
```

## 📞 技术支持

如果问题仍然存在：

1. **收集信息**：
   ```bash
   # 收集系统信息
   uname -a
   node --version
   npm --version
   npx prisma --version
   
   # 收集错误日志
   RUST_BACKTRACE=1 pnpm start 2>&1 | tee error.log
   ```

2. **检查依赖**：
   ```bash
   npm list @prisma/client
   npm list prisma
   ```

3. **验证环境**：
   ```bash
   ldd node_modules/.prisma/client/libquery_engine-*.so.node
   ```

---

**总结**：这个错误主要是由于 Prisma 客户端在 Rocky Linux 环境中的兼容性问题。通过重新生成客户端、配置正确的 binaryTargets 和环境变量，可以有效解决这个问题。
