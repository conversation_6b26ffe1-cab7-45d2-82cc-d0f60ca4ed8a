#!/bin/bash

# 生产环境启动脚本 (使用 npx 确保命令可用)
echo "🚀 启动 MapleStory 信息站 (生产环境)..."

# 设置错误时退出
set -e

# 检查 Node.js 版本
echo "检查 Node.js 环境..."
node_version=$(node -v)
echo "Node.js 版本: $node_version"

# 检查 npm 版本
npm_version=$(npm -v)
echo "npm 版本: $npm_version"

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 未找到 package.json 文件"
    echo "请确保在正确的项目目录中运行此脚本"
    exit 1
fi

# 检查 .next 目录是否存在
if [ ! -d ".next" ]; then
    echo "❌ 错误: 未找到 .next 构建目录"
    echo "请确保项目已经构建完成"
    exit 1
fi

# 检查环境变量
if [ ! -f ".env.local" ]; then
    echo "⚠️  警告: .env.local 文件不存在"
    echo "创建示例环境变量文件..."
    cat > .env.local << EOF
# 数据库配置
DATABASE_URL="postgresql://postgres:password@localhost:5432/mxd_info_db"

# NextAuth 配置
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-change-this-in-production"

# 应用配置
NODE_ENV="production"
PORT="3000"

# 邮件配置 (可选)
# SMTP_HOST="smtp.gmail.com"
# SMTP_PORT="587"
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-app-password"

# Redis 配置 (可选)
# REDIS_URL="redis://localhost:6379"
EOF
    echo "✅ 已创建 .env.local 文件，请根据需要修改配置"
fi

# 安装依赖（如果需要）
if [ ! -d "node_modules" ]; then
    echo "📦 安装生产依赖..."
    npm install --production --silent
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
else
    echo "✅ 依赖已安装"
fi

# 生成 Prisma 客户端（如果需要）
if [ -d "prisma" ]; then
    echo "🔧 生成 Prisma 客户端..."
    npx prisma generate --silent
    if [ $? -ne 0 ]; then
        echo "❌ Prisma 客户端生成失败"
        exit 1
    fi
else
    echo "ℹ️  未找到 prisma 目录，跳过 Prisma 设置"
fi

# 检查端口是否被占用
PORT=${PORT:-3000}
if command -v lsof >/dev/null 2>&1; then
    if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null; then
        echo "⚠️  警告: 端口 $PORT 已被占用"
        echo "请停止占用端口的进程或修改 PORT 环境变量"
        echo "查看占用进程: lsof -i :$PORT"
    fi
fi

# 启动应用
echo ""
echo "🌟 启动应用..."
echo "应用将在 http://localhost:$PORT 启动"
echo "按 Ctrl+C 停止应用"
echo ""

# 使用 npx 确保 next 命令可用
export NODE_ENV=production
npx next start -p $PORT

# 如果到达这里，说明应用已停止
echo ""
echo "📴 应用已停止"
