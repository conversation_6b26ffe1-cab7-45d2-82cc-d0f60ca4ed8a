#!/bin/bash

# 修复邮件中的 URL 问题
# 确保邮件中使用正确的生产环境 URL

echo "🔧 修复邮件 URL 配置问题..."
echo ""

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 检查当前环境变量
echo "📋 当前环境变量状态:"
echo "   NEXTAUTH_URL: ${NEXTAUTH_URL:-未设置}"
echo "   NEXT_PUBLIC_APP_URL: ${NEXT_PUBLIC_APP_URL:-未设置}"
echo "   APP_URL: ${APP_URL:-未设置}"
echo ""

# 检查 .env.local 文件
if [ -f ".env.local" ]; then
    echo "📄 检查 .env.local 文件:"
    
    if grep -q "NEXTAUTH_URL.*https://mxd.hyhuman.xyz" .env.local; then
        echo "   ✅ NEXTAUTH_URL 已正确配置"
    else
        echo "   ❌ NEXTAUTH_URL 配置有问题"
    fi
    
    if grep -q "NEXT_PUBLIC_APP_URL.*https://mxd.hyhuman.xyz" .env.local; then
        echo "   ✅ NEXT_PUBLIC_APP_URL 已配置"
    else
        echo "   ⚠️  NEXT_PUBLIC_APP_URL 未配置"
    fi
    
    if grep -q "APP_URL.*https://mxd.hyhuman.xyz" .env.local; then
        echo "   ✅ APP_URL 已配置"
    else
        echo "   ⚠️  APP_URL 未配置"
    fi
else
    echo "❌ 未找到 .env.local 文件"
    exit 1
fi

echo ""

# 修复环境变量配置
echo "🔧 修复环境变量配置..."

# 备份原文件
cp .env.local .env.local.backup.$(date +%Y%m%d_%H%M%S)
echo "✅ 已备份 .env.local 文件"

# 确保 APP_URL 存在
if ! grep -q "^APP_URL=" .env.local; then
    echo "📝 添加 APP_URL 环境变量..."
    echo "" >> .env.local
    echo "# 应用 URL (用于邮件等服务器端功能)" >> .env.local
    echo "APP_URL=\"https://mxd.hyhuman.xyz\"" >> .env.local
    echo "✅ 已添加 APP_URL"
fi

# 确保 NEXTAUTH_URL 正确
if ! grep -q "NEXTAUTH_URL.*https://mxd.hyhuman.xyz" .env.local; then
    echo "📝 修复 NEXTAUTH_URL..."
    sed -i 's|NEXTAUTH_URL=.*|NEXTAUTH_URL="https://mxd.hyhuman.xyz"|' .env.local
    echo "✅ 已修复 NEXTAUTH_URL"
fi

# 确保 NEXT_PUBLIC_APP_URL 正确
if ! grep -q "NEXT_PUBLIC_APP_URL.*https://mxd.hyhuman.xyz" .env.local; then
    echo "📝 修复 NEXT_PUBLIC_APP_URL..."
    if grep -q "NEXT_PUBLIC_APP_URL=" .env.local; then
        sed -i 's|NEXT_PUBLIC_APP_URL=.*|NEXT_PUBLIC_APP_URL="https://mxd.hyhuman.xyz"|' .env.local
    else
        echo "" >> .env.local
        echo "# 客户端应用 URL" >> .env.local
        echo "NEXT_PUBLIC_APP_URL=\"https://mxd.hyhuman.xyz\"" >> .env.local
    fi
    echo "✅ 已修复 NEXT_PUBLIC_APP_URL"
fi

echo ""
echo "📋 修复后的配置:"
echo "   NEXTAUTH_URL: $(grep NEXTAUTH_URL .env.local | cut -d'=' -f2)"
echo "   NEXT_PUBLIC_APP_URL: $(grep NEXT_PUBLIC_APP_URL .env.local | cut -d'=' -f2)"
echo "   APP_URL: $(grep APP_URL .env.local | cut -d'=' -f2)"

echo ""
echo "🔄 重启应用以使配置生效..."

# 停止应用
echo "🛑 停止当前应用..."
pkill -f "next start" || true
pkill -f "npm start" || true
sleep 2

# 重启应用
echo "🚀 重启应用..."
if command -v pm2 >/dev/null 2>&1; then
    pm2 restart maplestory-info-station || npm start &
else
    npm start &
fi

echo ""
echo "🎉 修复完成!"
echo ""
echo "📋 验证步骤:"
echo "1. 注册一个新用户"
echo "2. 检查邮件中的验证链接"
echo "3. 确认链接使用 https://mxd.hyhuman.xyz"
echo ""
echo "🔍 如果问题仍然存在:"
echo "1. 检查应用日志中的环境变量调试信息"
echo "2. 确认应用已完全重启"
echo "3. 检查邮件服务配置"
echo ""
echo "📞 调试命令:"
echo "   查看应用日志: pm2 logs maplestory-info-station"
echo "   检查环境变量: node -e \"console.log(process.env.NEXTAUTH_URL)\""
