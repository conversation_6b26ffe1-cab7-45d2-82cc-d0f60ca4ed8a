import { NextRequest, NextResponse } from 'next/server'
import { hash } from 'bcryptjs'
import { z } from 'zod'
import { prisma } from '@/lib/db'
import { sendVerificationEmail } from '@/lib/email'
import { generateVerificationToken } from '@/lib/tokens'
import { getAuthConfig } from '@/lib/auth-config'

const registerUsernameSchema = z.object({
  username: z.string()
    .min(3, '用户名至少3个字符')
    .max(20, '用户名不能超过20个字符')
    .regex(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'),
  // email: z.string().email('请输入有效的邮箱地址'),
  // name: z.string().min(2, '姓名至少2个字符').max(50, '姓名不能超过50个字符'),
  password: z.string()
    .min(8, '密码至少8位')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, '密码必须包含大小写字母和数字')
})

export async function POST(request: NextRequest) {
  console.log('📝 开始处理用户名注册请求')

  try {
    // 检查是否启用用户名注册
    const config = getAuthConfig()
    if (!config.allowUsernameLogin) {
      return NextResponse.json(
        { error: '用户名注册功能未启用' },
        { status: 403 }
      )
    }

    const body = await request.json()
    console.log('📋 接收到注册数据:', { ...body, password: '[HIDDEN]' })

    // const { username, email, name, password } = registerUsernameSchema.parse(body)
    const { username, password } = registerUsernameSchema.parse(body)

    // 检查用户名是否已存在
    console.log('🔍 检查用户名是否已存在:', username)
    const existingUsername = await prisma.user.findUnique({
      where: { username }
    })

    if (existingUsername) {
      console.log('❌ 用户名已存在:', username)
      return NextResponse.json(
        { error: '该用户名已被使用' },
        { status: 400 }
      )
    }

    // 检查邮箱是否已存在
    /*console.log('🔍 检查邮箱是否已存在:', email)
    const existingEmail = await prisma.user.findUnique({
      where: { email }
    })

    if (existingEmail) {
      console.log('❌ 邮箱已存在:', email)
      return NextResponse.json(
        { error: '该邮箱已被注册' },
        { status: 400 }
      )
    }*/

    // 密码加密
    console.log('🔐 开始加密密码')
    const hashedPassword = await hash(password, 12)
    console.log('✅ 密码加密完成')

    // 创建用户
    const email = username+'@noemail.com'
    const name = username
    console.log('👤 开始创建用户')
    const user = await prisma.user.create({
      data: {
        username,
        email,
        name,
        hashedPassword,
        isActive: true,
        emailVerified: config.requireEmailVerification ? null : new Date(),
      }
    })
    console.log('✅ 用户创建成功:', user.id)

    // 分配默认角色
    console.log('🎭 开始分配默认角色')
    const registeredRole = await prisma.role.findUnique({
      where: { name: 'registered' }
    })

    if (registeredRole) {
      await prisma.userRole.create({
        data: {
          userId: user.id,
          roleId: registeredRole.id
        }
      })
      console.log('✅ 默认角色分配成功')
    } else {
      console.warn('⚠️ 未找到 registered 角色')
    }

    // 创建虚拟货币余额
    console.log('💰 开始创建虚拟货币余额')
    await prisma.currencyBalance.create({
      data: {
        userId: user.id,
        balance: 100, // 新用户赠送100欢乐豆
        frozenBalance: 0,
        totalEarned: 100,
        totalSpent: 0
      }
    })
    console.log('✅ 虚拟货币余额创建成功: 100欢乐豆')

    // 如果需要邮箱验证，生成验证令牌并发送邮件
    if (config.requireEmailVerification) {
      console.log('🎫 开始生成验证令牌')
      const verificationToken = generateVerificationToken()
      console.log('✅ 验证令牌生成成功:', verificationToken.substring(0, 8) + '...')

      // 存储验证令牌
      console.log('💾 开始存储验证令牌到数据库')
      await prisma.verificationToken.create({
        data: {
          identifier: email,
          token: verificationToken,
          expires: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时后过期
        }
      })
      console.log('✅ 验证令牌存储成功')

      // 发送验证邮件
      console.log('📧 开始发送验证邮件到:', email)
      try {
        await sendVerificationEmail(email, verificationToken)
        console.log('✅ 验证邮件发送成功')
      } catch (emailError) {
        console.error('❌ 验证邮件发送失败:', emailError)
        // 邮件发送失败不应该阻止注册流程
      }
    }

    console.log('🎉 用户名注册流程完成')
    return NextResponse.json({
      success: true,
      message: config.requireEmailVerification 
        ? '注册成功，请查收验证邮件' 
        : '注册成功，可以直接登录'
    })

  } catch (error) {
    console.error('❌ 用户名注册过程发生错误:', error)

    if (error instanceof z.ZodError) {
      console.error('❌ 数据验证错误:', error.errors)
      return NextResponse.json(
        { error: '输入数据格式错误', details: error.errors },
        { status: 400 }
      )
    }

    console.error('❌ 注册失败，未知错误:', error)
    return NextResponse.json(
      { error: '注册失败，请稍后重试' },
      { status: 500 }
    )
  }
}
