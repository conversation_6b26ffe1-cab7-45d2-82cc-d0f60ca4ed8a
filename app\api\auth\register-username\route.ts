import { NextRequest, NextResponse } from 'next/server'
import { hash } from 'bcryptjs'
import { z } from 'zod'
import { prisma } from '@/lib/db'
import { sendVerificationEmail } from '@/lib/email'
import { generateVerificationToken } from '@/lib/tokens'
import { getAuthConfig } from '@/lib/auth-config'

const registerUsernameSchema = z.object({
  username: z.string()
    .min(3, '用户名至少3个字符')
    .max(20, '用户名不能超过20个字符')
    .regex(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'),
  // email: z.string().email('请输入有效的邮箱地址'),
  // name: z.string().min(2, '姓名至少2个字符').max(50, '姓名不能超过50个字符'),
  password: z.string()
    .min(8, '密码至少8位')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, '密码必须包含大小写字母和数字')
})

export async function POST(request: NextRequest) {
  console.log('📝 开始处理用户名注册请求')

  try {
    // 检查是否启用用户名注册
    const config = getAuthConfig()
    if (!config.allowUsernameLogin) {
      return NextResponse.json(
        { error: '用户名注册功能未启用' },
        { status: 403 }
      )
    }

    const body = await request.json()
    console.log('📋 接收到注册数据:', { ...body, password: '[HIDDEN]' })

    // const { username, email, name, password } = registerUsernameSchema.parse(body)
    const { username, password } = registerUsernameSchema.parse(body)

    // 检查用户名是否已存在
    console.log('🔍 检查用户名是否已存在:', username)
    const existingUsername = await prisma.user.findUnique({
      where: { username }
    })

    if (existingUsername) {
      console.log('❌ 用户名已存在:', username)
      return NextResponse.json(
        { error: '该用户名已被使用' },
        { status: 400 }
      )
    }

    // 检查邮箱是否已存在
    /*console.log('🔍 检查邮箱是否已存在:', email)
    const existingEmail = await prisma.user.findUnique({
      where: { email }
    })

    if (existingEmail) {
      console.log('❌ 邮箱已存在:', email)
      return NextResponse.json(
        { error: '该邮箱已被注册' },
        { status: 400 }
      )
    }*/

    // 密码加密
    console.log('🔐 开始加密密码')
    const hashedPassword = await hash(password, 12)
    console.log('✅ 密码加密完成')

    // 创建用户 - 用户名注册不需要邮箱
    console.log('👤 开始创建用户')
    const user = await prisma.user.create({
      data: {
        username,
        email: null, // 用户名注册不需要邮箱
        name: username, // 使用用户名作为显示名称
        hashedPassword,
        isActive: true,
        emailVerified: null, // 用户名注册不需要邮箱验证
      }
    })
    console.log('✅ 用户创建成功:', user.id)

    // 分配默认角色
    console.log('🎭 开始分配默认角色')
    const registeredRole = await prisma.role.findUnique({
      where: { name: 'registered' }
    })

    if (registeredRole) {
      await prisma.userRole.create({
        data: {
          userId: user.id,
          roleId: registeredRole.id
        }
      })
      console.log('✅ 默认角色分配成功')
    } else {
      console.warn('⚠️ 未找到 registered 角色')
    }

    // 创建虚拟货币余额
    console.log('💰 开始创建虚拟货币余额')
    await prisma.currencyBalance.create({
      data: {
        userId: user.id,
        balance: 100, // 新用户赠送100欢乐豆
        frozenBalance: 0,
        totalEarned: 100,
        totalSpent: 0
      }
    })
    console.log('✅ 虚拟货币余额创建成功: 100欢乐豆')

    // 用户名注册不需要邮箱验证流程
    console.log('✅ 用户名注册无需邮箱验证，直接完成注册')

    console.log('🎉 用户名注册流程完成')
    return NextResponse.json({
      success: true,
      message: '注册成功，可以直接登录'
    })

  } catch (error) {
    console.error('❌ 用户名注册过程发生错误:', error)

    if (error instanceof z.ZodError) {
      console.error('❌ 数据验证错误:', error.errors)
      return NextResponse.json(
        { error: '输入数据格式错误', details: error.errors },
        { status: 400 }
      )
    }

    console.error('❌ 注册失败，未知错误:', error)
    return NextResponse.json(
      { error: '注册失败，请稍后重试' },
      { status: 500 }
    )
  }
}
