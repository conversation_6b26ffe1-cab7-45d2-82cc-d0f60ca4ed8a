# 生产环境部署说明

## 📁 目录结构

```
deploy/
├── .next/              # 构建输出（必需）
├── public/             # 静态资源（必需）
├── prisma/             # 数据库配置（必需）
├── docs/               # 数据文件（必需）
├── package.json        # 依赖配置（必需）
├── next.config.mjs     # Next.js配置（必需）
├── start.sh           # Linux/Mac 启动脚本
├── start.bat          # Windows 启动脚本
└── README-DEPLOY.md   # 本文件
```

## 🚀 部署步骤

### 1. 环境要求

- Node.js 18.17+ 
- npm 或 pnpm
- PostgreSQL 数据库
- Redis（可选，用于缓存）

### 2. 配置环境变量

创建 `.env.local` 文件：

```bash
# 数据库配置
DATABASE_URL="postgresql://postgres:postgres@localhost:5433/mxd_info_db"

# NextAuth 配置
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-secret-key"

# 邮件配置（可选）
SMTP_HOST="your-smtp-host"
SMTP_PORT="587"
SMTP_USER="your-email"
SMTP_PASS="your-password"

# Redis 配置（可选）
REDIS_URL="redis://localhost:6379"
```

### 3. 安装依赖

```bash
npm install --production
```

### 4. 数据库设置

```bash
# 生成 Prisma 客户端
npx prisma generate

# 运行数据库迁移
npx prisma migrate deploy
```

### 5. 启动应用

**Linux/Mac:**
```bash
chmod +x start.sh
./start.sh
```

**Windows:**
```cmd
start.bat
```

**手动启动:**
```bash
npm start
```

## 🔧 生产环境配置

### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # 静态文件缓存
    location /_next/static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        proxy_pass http://localhost:3000;
    }
}
```

### PM2 配置

```json
{
  "name": "maplestory-info-station",
  "script": "npm",
  "args": "start",
  "instances": "max",
  "exec_mode": "cluster",
  "env": {
    "NODE_ENV": "production",
    "PORT": "3000"
  }
}
```

## 📊 监控和日志

- 应用日志: `pm2 logs`
- 性能监控: `pm2 monit`
- 重启应用: `pm2 restart maplestory-info-station`

## 🔒 安全建议

1. 使用强密码和密钥
2. 配置防火墙规则
3. 启用 HTTPS
4. 定期更新依赖
5. 监控应用性能和错误

## 📞 支持

如有问题，请检查：
1. Node.js 版本是否正确
2. 环境变量是否配置完整
3. 数据库连接是否正常
4. 端口是否被占用
