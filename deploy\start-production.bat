@echo off
echo Starting MapleStory Info Station (Production)...
echo.

REM 检查 Node.js 版本
echo 检查 Node.js 环境...
node -v >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: Node.js 未安装或不在 PATH 中
    echo 请安装 Node.js 18.17+ 版本
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node -v') do set node_version=%%i
echo Node.js 版本: %node_version%

REM 检查 npm 版本
npm -v >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: npm 未安装或不在 PATH 中
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm -v') do set npm_version=%%i
echo npm 版本: %npm_version%

REM 检查是否在正确的目录
if not exist "package.json" (
    echo ❌ 错误: 未找到 package.json 文件
    echo 请确保在正确的项目目录中运行此脚本
    pause
    exit /b 1
)

REM 检查 .next 目录是否存在
if not exist ".next" (
    echo ❌ 错误: 未找到 .next 构建目录
    echo 请确保项目已经构建完成
    pause
    exit /b 1
)

REM 检查环境变量文件
if not exist ".env.local" (
    echo ⚠️  警告: .env.local 文件不存在
    echo 创建示例环境变量文件...
    (
        echo # 数据库配置
        echo DATABASE_URL="postgresql://postgres:password@localhost:5432/mxd_info_db"
        echo.
        echo # NextAuth 配置
        echo NEXTAUTH_URL="http://localhost:3000"
        echo NEXTAUTH_SECRET="your-secret-key-change-this-in-production"
        echo.
        echo # 应用配置
        echo NODE_ENV="production"
        echo PORT="3000"
        echo.
        echo # 邮件配置 ^(可选^)
        echo # SMTP_HOST="smtp.gmail.com"
        echo # SMTP_PORT="587"
        echo # SMTP_USER="<EMAIL>"
        echo # SMTP_PASS="your-app-password"
        echo.
        echo # Redis 配置 ^(可选^)
        echo # REDIS_URL="redis://localhost:6379"
    ) > .env.local
    echo ✅ 已创建 .env.local 文件，请根据需要修改配置
    echo.
)

REM 安装依赖（如果需要）
if not exist "node_modules" (
    echo 📦 安装生产依赖...
    npm install --production --silent
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
) else (
    echo ✅ 依赖已安装
)

REM 生成 Prisma 客户端（如果需要）
if exist "prisma" (
    echo 🔧 生成 Prisma 客户端...
    npx prisma generate --silent
    if %errorlevel% neq 0 (
        echo ❌ Prisma 客户端生成失败
        pause
        exit /b 1
    )
) else (
    echo ℹ️  未找到 prisma 目录，跳过 Prisma 设置
)

REM 设置环境变量
set NODE_ENV=production
if not defined PORT set PORT=3000

REM 检查端口是否被占用
netstat -an | find ":%PORT%" | find "LISTENING" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  警告: 端口 %PORT% 已被占用
    echo 请停止占用端口的进程或修改 PORT 环境变量
    echo 查看占用进程: netstat -ano ^| find ":%PORT%"
    echo.
    echo 是否继续启动? ^(y/n^)
    set /p continue=
    if /i not "%continue%"=="y" (
        echo 启动已取消
        pause
        exit /b 0
    )
)

REM 启动应用
echo.
echo 🌟 启动应用...
echo 应用将在 http://localhost:%PORT% 启动
echo 按 Ctrl+C 停止应用
echo.

REM 使用 npx 确保 next 命令可用
npx next start -p %PORT%

REM 如果启动失败，显示错误信息
if %errorlevel% neq 0 (
    echo.
    echo ❌ 应用启动失败
    echo 请检查:
    echo 1. 环境变量配置是否正确
    echo 2. 数据库连接是否正常
    echo 3. 端口 %PORT% 是否被占用
    echo 4. 查看上方的错误信息
    echo.
    echo 常见解决方案:
    echo - 检查 .env.local 文件配置
    echo - 确保数据库服务正在运行
    echo - 尝试使用不同的端口: set PORT=3001
    pause
) else (
    echo.
    echo 📴 应用已停止
    pause
)
