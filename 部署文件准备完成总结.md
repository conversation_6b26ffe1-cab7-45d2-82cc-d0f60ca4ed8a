# 🚀 部署文件准备完成总结

## ✅ 部署文件已准备就绪

您的 MapleStory 信息站项目已经完全准备好部署到生产环境！

## 📁 可用的部署方案

### 方案一：使用 deploy 目录 (推荐)

**位置**: `./deploy/`
**大小**: 约 50MB
**包含内容**:
- ✅ 完整的构建输出 (`.next/`)
- ✅ 静态资源 (`public/`)
- ✅ 数据库配置 (`prisma/`)
- ✅ 数据文件 (`docs/starforcePB.csv`)
- ✅ 配置文件 (`package.json`, `next.config.mjs`)
- ✅ 启动脚本 (`start.sh`, `start.bat`)
- ✅ 详细部署说明 (`README-DEPLOY.md`)

**使用方法**:
```bash
# 直接上传整个 deploy 目录到服务器
scp -r deploy/ user@your-server:/path/to/app/
```

### 方案二：使用压缩包

**文件**: `maplestory-info-station-2025-06-23.zip`
**大小**: 约 25MB (压缩后)
**优势**: 
- 文件传输更快
- 版本管理方便
- 适合通过网页上传

**使用方法**:
```bash
# 上传压缩包
scp maplestory-info-station-2025-06-23.zip user@your-server:/path/to/app/

# 在服务器上解压
ssh user@your-server "cd /path/to/app && unzip maplestory-info-station-2025-06-23.zip"
```

## 🔧 部署环境要求

### 服务器要求
- **Node.js**: 18.17+ (LTS 推荐)
- **内存**: 最少 2GB RAM
- **存储**: 最少 10GB 可用空间
- **数据库**: PostgreSQL 13+

### 网络要求
- **端口**: 3000 (应用端口)
- **域名**: 配置域名和 SSL 证书
- **防火墙**: 开放 80, 443, 22 端口

## 📝 快速部署步骤

### 1. 上传文件
```bash
# 方案一：直接上传目录
scp -r deploy/ user@server:/opt/maplestory-info/

# 方案二：上传压缩包
scp maplestory-info-station-2025-06-23.zip user@server:/opt/
ssh user@server "cd /opt && unzip maplestory-info-station-2025-06-23.zip -d maplestory-info"
```

### 2. 配置环境变量
```bash
cd /opt/maplestory-info
cp .env.example .env.local

# 编辑环境变量
nano .env.local
```

**必需的环境变量**:
```bash
DATABASE_URL="postgresql://postgres:password@localhost:5432/mxd_info_db"
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-secret-key"
```

### 3. 安装依赖
```bash
npm install --production
```

### 4. 设置数据库
```bash
npx prisma generate
npx prisma migrate deploy
```

### 5. 启动应用
```bash
# 方法一：直接启动
npm start

# 方法二：使用启动脚本
chmod +x start.sh
./start.sh

# 方法三：使用 PM2 (推荐)
npm install -g pm2
pm2 start npm --name "maplestory-info" -- start
pm2 startup
pm2 save
```

## 🌐 Web 服务器配置

### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
    
    location /_next/static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        proxy_pass http://localhost:3000;
    }
}
```

## 📊 部署验证清单

部署完成后，请验证以下功能：

- [ ] **网站访问**: https://your-domain.com 可以正常打开
- [ ] **用户注册**: 注册功能正常工作
- [ ] **用户登录**: 登录功能正常工作
- [ ] **强化模拟器**: `/tools/enhancement` 页面正常
- [ ] **API 接口**: `/api/enhancement/enhance` 响应正常
- [ ] **数据库连接**: 用户数据可以正常保存
- [ ] **星力数据**: 星力概率数据正确加载
- [ ] **静态资源**: 图片、CSS、JS 文件正常加载
- [ ] **SSL 证书**: HTTPS 正常工作
- [ ] **性能监控**: 应用运行稳定

## 🔒 安全配置建议

### 1. 防火墙设置
```bash
# Ubuntu UFW
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 2. SSL 证书 (Let's Encrypt)
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 3. 数据库安全
```bash
# 设置强密码
sudo -u postgres psql
\password postgres
```

## 📈 性能优化建议

### 1. 启用 Gzip 压缩
```nginx
gzip on;
gzip_types text/plain text/css application/json application/javascript;
```

### 2. 配置缓存
```nginx
location /_next/static/ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 3. 使用 PM2 集群模式
```bash
pm2 start npm --name "maplestory-info" -i max -- start
```

## 🚨 故障排除

### 常见问题及解决方案

1. **端口被占用**:
   ```bash
   sudo lsof -i :3000
   sudo kill -9 <PID>
   ```

2. **数据库连接失败**:
   - 检查 `DATABASE_URL` 配置
   - 确认 PostgreSQL 服务运行
   - 验证数据库用户权限

3. **静态文件 404**:
   - 确认 `.next/static/` 目录存在
   - 检查文件权限
   - 验证 Nginx 配置

4. **内存不足**:
   - 增加服务器内存
   - 配置 swap 分区
   - 优化应用配置

### 日志查看
```bash
# PM2 日志
pm2 logs

# Nginx 日志
sudo tail -f /var/log/nginx/error.log

# 应用日志
tail -f ~/.pm2/logs/maplestory-info-out.log
```

## 📞 技术支持

### 部署文档
- **详细部署指南**: `生产环境部署指南.md`
- **服务器配置**: `deploy/README-DEPLOY.md`
- **API 文档**: 访问 `/api/enhancement/enhance` 查看

### 联系方式
如果在部署过程中遇到问题，请：
1. 查看相关日志文件
2. 检查环境变量配置
3. 验证服务器环境要求
4. 参考故障排除指南

## 🎉 部署成功！

恭喜！您的 MapleStory 信息站现在已经完全准备好部署到生产环境。

**下一步**:
1. 选择合适的部署方案
2. 准备服务器环境
3. 上传部署文件
4. 配置环境变量
5. 启动应用
6. 配置域名和 SSL
7. 进行功能验证

祝您部署顺利！🚀✨
