#!/bin/bash

# 修复 NextAuth.js 在反向代理环境下的配置问题

echo "🔧 修复 NextAuth.js 反向代理配置..."
echo ""

# 检查当前环境变量
if [ -f ".env.local" ]; then
    echo "✅ 找到 .env.local 文件"
    
    # 备份现有配置
    cp .env.local .env.local.backup.$(date +%Y%m%d_%H%M%S)
    echo "✅ 已备份现有配置"
else
    echo "❌ 未找到 .env.local 文件"
    exit 1
fi

# 更新环境变量配置
echo "📝 更新环境变量配置..."

# 创建新的 .env.local 文件
cat > .env.local << 'EOF'
# 数据库配置
DATABASE_URL="postgresql://postgres:password@localhost:5432/mxd_info_db"

# NextAuth 配置 - 修复反向代理问题
NEXTAUTH_URL="https://mxd.hyhuman.xyz"
NEXTAUTH_SECRET="$(openssl rand -base64 32)"

# 信任代理配置 - 重要！
NEXTAUTH_URL_INTERNAL="http://localhost:3000"
AUTH_TRUST_HOST="true"

# 强制使用 HTTPS cookies
NEXTAUTH_COOKIE_SECURE="true"

# 应用配置
NODE_ENV="production"
PORT="3000"

# Prisma 配置
PRISMA_QUERY_ENGINE_LIBRARY="/root/svc/mxd/mxd-tool/node_modules/.prisma/client/libquery_engine-rhel-openssl-1.0.x.so.node"
PRISMA_QUERY_ENGINE_BINARY="/root/svc/mxd/mxd-tool/node_modules/.prisma/client/query-engine-rhel-openssl-1.0.x"
CHECKPOINT_DISABLE="1"

# 邮件配置 (可选)
# SMTP_HOST="smtp.gmail.com"
# SMTP_PORT="587"
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-app-password"

# Redis 配置 (可选)
# REDIS_URL="redis://localhost:6379"

# 调试配置 (可以在生产环境移除)
# DEBUG="next-auth:*"
EOF

echo "✅ 已更新 .env.local 文件"

# 检查 NextAuth 配置文件
echo "🔍 检查 NextAuth 配置文件..."

if [ -f "lib/auth.ts" ]; then
    echo "✅ 找到 NextAuth 配置文件"
    
    # 检查是否包含 trustHost 配置
    if grep -q "trustHost" lib/auth.ts; then
        echo "✅ 已包含 trustHost 配置"
    else
        echo "⚠️  需要更新 NextAuth 配置文件"
        echo "请在 lib/auth.ts 中添加 trustHost: true 配置"
    fi
else
    echo "⚠️  未找到 NextAuth 配置文件"
fi

# 创建 NextAuth 配置补丁
echo "📄 创建 NextAuth 配置补丁..."

cat > nextauth-config-patch.js << 'EOF'
// NextAuth.js 反向代理配置补丁
// 在您的 NextAuth 配置中添加以下选项

const nextAuthConfig = {
  // 信任代理服务器
  trustHost: true,
  
  // 使用安全的 cookies 配置
  useSecureCookies: process.env.NODE_ENV === "production",
  
  // 配置 cookies
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === "production" 
        ? "__Secure-next-auth.session-token" 
        : "next-auth.session-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
        domain: process.env.NODE_ENV === "production" ? ".hyhuman.xyz" : undefined
      }
    }
  },
  
  // 回调配置
  callbacks: {
    async redirect({ url, baseUrl }) {
      // 确保重定向到正确的域名
      if (url.startsWith("/")) {
        return `${baseUrl}${url}`
      }
      
      // 如果是相同域名，允许重定向
      if (new URL(url).origin === baseUrl) {
        return url
      }
      
      // 默认重定向到首页
      return baseUrl
    }
  }
}

module.exports = nextAuthConfig
EOF

echo "✅ 已创建配置补丁文件"

# 创建测试脚本
echo "🧪 创建测试脚本..."

cat > test-nextauth.js << 'EOF'
// 测试 NextAuth 配置

const https = require('https')
const http = require('http')

function testEndpoint(url, description) {
  return new Promise((resolve) => {
    console.log(`🔍 测试 ${description}: ${url}`)
    
    const client = url.startsWith('https') ? https : http
    
    const req = client.get(url, (res) => {
      console.log(`   状态码: ${res.statusCode}`)
      console.log(`   重定向: ${res.headers.location || '无'}`)
      resolve(res.statusCode)
    })
    
    req.on('error', (err) => {
      console.log(`   错误: ${err.message}`)
      resolve(0)
    })
    
    req.setTimeout(5000, () => {
      console.log(`   超时`)
      req.destroy()
      resolve(0)
    })
  })
}

async function runTests() {
  console.log('🧪 NextAuth 端点测试...\n')
  
  const baseUrl = 'https://mxd.hyhuman.xyz'
  
  await testEndpoint(`${baseUrl}/api/auth/signin`, 'NextAuth 登录页面')
  await testEndpoint(`${baseUrl}/api/auth/signout`, 'NextAuth 登出页面')
  await testEndpoint(`${baseUrl}/api/auth/session`, 'NextAuth 会话端点')
  await testEndpoint(`${baseUrl}/api/auth/providers`, 'NextAuth 提供商端点')
  
  console.log('\n✅ 测试完成')
  console.log('如果所有端点返回 200 或 302，说明配置正确')
}

runTests()
EOF

echo "✅ 已创建测试脚本"

# 重启应用提示
echo ""
echo "🎯 下一步操作:"
echo "1. 检查并更新 NextAuth 配置文件 (lib/auth.ts)"
echo "2. 重启应用: pnpm start"
echo "3. 运行测试: node test-nextauth.js"
echo "4. 测试登录功能: https://mxd.hyhuman.xyz/login"
echo ""
echo "📋 重要配置项:"
echo "- NEXTAUTH_URL: https://mxd.hyhuman.xyz"
echo "- AUTH_TRUST_HOST: true"
echo "- trustHost: true (在 NextAuth 配置中)"
echo ""
echo "🔧 如果问题仍然存在:"
echo "1. 检查 Nginx 日志: sudo tail -f /var/log/nginx/error.log"
echo "2. 检查应用日志: pnpm start"
echo "3. 验证 SSL 证书: curl -I https://mxd.hyhuman.xyz"
