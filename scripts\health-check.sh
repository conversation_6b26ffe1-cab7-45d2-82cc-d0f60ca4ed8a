#!/bin/bash

# 冒险岛情报站 - 健康检查脚本
# 用于监控应用和服务状态

set -e

# 配置
APP_URL="http://localhost:3000"
HEALTH_ENDPOINT="/health"
LOG_FILE="/home/<USER>/logs/health-check.log"
ALERT_EMAIL="<EMAIL>"
SLACK_WEBHOOK=""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a "$LOG_FILE"
}

# 发送告警
send_alert() {
    local message="$1"
    local severity="$2"
    
    # 邮件告警
    if [ -n "$ALERT_EMAIL" ] && command -v mail >/dev/null 2>&1; then
        echo "$message" | mail -s "[$severity] 冒险岛情报站告警" "$ALERT_EMAIL"
    fi
    
    # Slack 告警
    if [ -n "$SLACK_WEBHOOK" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"[$severity] 冒险岛情报站告警: $message\"}" \
            "$SLACK_WEBHOOK" >/dev/null 2>&1
    fi
}

# 检查应用健康状态
check_app_health() {
    local status_code
    local response_time
    
    # 检查HTTP响应
    status_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time 10 "$APP_URL$HEALTH_ENDPOINT" || echo "000")
    response_time=$(curl -s -o /dev/null -w "%{time_total}" --max-time 10 "$APP_URL$HEALTH_ENDPOINT" || echo "999")
    
    if [ "$status_code" = "200" ]; then
        log "应用健康检查通过 (响应时间: ${response_time}s)"
        
        # 检查响应时间
        if (( $(echo "$response_time > 5" | bc -l) )); then
            warning "应用响应时间过长: ${response_time}s"
            send_alert "应用响应时间过长: ${response_time}s" "WARNING"
        fi
        
        return 0
    else
        error "应用健康检查失败 (状态码: $status_code)"
        send_alert "应用健康检查失败，状态码: $status_code" "CRITICAL"
        return 1
    fi
}

# 检查PM2进程状态
check_pm2_status() {
    if ! command -v pm2 >/dev/null 2>&1; then
        warning "PM2 未安装"
        return 1
    fi
    
    local app_status
    app_status=$(pm2 jlist | jq -r '.[] | select(.name=="maplestory-info-station") | .pm2_env.status' 2>/dev/null || echo "not_found")
    
    if [ "$app_status" = "online" ]; then
        log "PM2 进程状态正常"
        return 0
    else
        error "PM2 进程状态异常: $app_status"
        
        # 尝试重启应用
        log "尝试重启应用..."
        if pm2 restart maplestory-info-station >/dev/null 2>&1; then
            log "应用重启成功"
            sleep 10
            # 重新检查
            check_app_health
        else
            error "应用重启失败"
            send_alert "PM2 进程异常且重启失败" "CRITICAL"
        fi
        
        return 1
    fi
}

# 检查数据库连接
check_database() {
    if command -v psql >/dev/null 2>&1; then
        if psql -h localhost -U maplestory -d mxd_info_db -c "SELECT 1;" >/dev/null 2>&1; then
            log "数据库连接正常"
            return 0
        else
            error "数据库连接失败"
            send_alert "PostgreSQL 数据库连接失败" "CRITICAL"
            return 1
        fi
    else
        warning "psql 命令未找到，跳过数据库检查"
        return 0
    fi
}

# 检查Redis连接
check_redis() {
    if command -v redis-cli >/dev/null 2>&1; then
        if redis-cli ping >/dev/null 2>&1; then
            log "Redis 连接正常"
            return 0
        else
            error "Redis 连接失败"
            send_alert "Redis 连接失败" "HIGH"
            return 1
        fi
    else
        warning "redis-cli 命令未找到，跳过Redis检查"
        return 0
    fi
}

# 检查磁盘空间
check_disk_space() {
    local disk_usage
    disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$disk_usage" -gt 90 ]; then
        error "磁盘空间严重不足: ${disk_usage}%"
        send_alert "磁盘空间严重不足: ${disk_usage}%" "CRITICAL"
        return 1
    elif [ "$disk_usage" -gt 80 ]; then
        warning "磁盘空间不足: ${disk_usage}%"
        send_alert "磁盘空间不足: ${disk_usage}%" "WARNING"
        return 1
    else
        log "磁盘空间正常: ${disk_usage}%"
        return 0
    fi
}

# 检查内存使用
check_memory() {
    local memory_usage
    memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [ "$memory_usage" -gt 90 ]; then
        error "内存使用过高: ${memory_usage}%"
        send_alert "内存使用过高: ${memory_usage}%" "HIGH"
        return 1
    elif [ "$memory_usage" -gt 80 ]; then
        warning "内存使用较高: ${memory_usage}%"
        return 1
    else
        log "内存使用正常: ${memory_usage}%"
        return 0
    fi
}

# 检查CPU使用率
check_cpu() {
    local cpu_usage
    cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
    
    if (( $(echo "$cpu_usage > 80" | bc -l) )); then
        warning "CPU 使用率较高: ${cpu_usage}%"
        return 1
    else
        log "CPU 使用率正常: ${cpu_usage}%"
        return 0
    fi
}

# 检查SSL证书
check_ssl_certificate() {
    local domain="$1"
    
    if [ -z "$domain" ]; then
        return 0
    fi
    
    local expiry_date
    expiry_date=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
    
    if [ -n "$expiry_date" ]; then
        local expiry_timestamp
        local current_timestamp
        local days_until_expiry
        
        expiry_timestamp=$(date -d "$expiry_date" +%s)
        current_timestamp=$(date +%s)
        days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
        
        if [ "$days_until_expiry" -lt 7 ]; then
            error "SSL证书即将过期: ${days_until_expiry}天"
            send_alert "SSL证书即将过期: ${days_until_expiry}天" "HIGH"
            return 1
        elif [ "$days_until_expiry" -lt 30 ]; then
            warning "SSL证书将在${days_until_expiry}天后过期"
            return 1
        else
            log "SSL证书有效期正常: ${days_until_expiry}天"
            return 0
        fi
    else
        warning "无法获取SSL证书信息"
        return 1
    fi
}

# 生成健康报告
generate_health_report() {
    local total_checks=0
    local failed_checks=0
    local report_file="/home/<USER>/logs/health-report-$(date +%Y%m%d).json"
    
    log "开始健康检查..."
    
    # 执行各项检查
    checks=(
        "check_app_health"
        "check_pm2_status"
        "check_database"
        "check_redis"
        "check_disk_space"
        "check_memory"
        "check_cpu"
    )
    
    for check in "${checks[@]}"; do
        total_checks=$((total_checks + 1))
        if ! $check; then
            failed_checks=$((failed_checks + 1))
        fi
    done
    
    # 生成JSON报告
    cat > "$report_file" << EOF
{
    "timestamp": "$(date -Iseconds)",
    "total_checks": $total_checks,
    "failed_checks": $failed_checks,
    "success_rate": $(echo "scale=2; ($total_checks - $failed_checks) * 100 / $total_checks" | bc),
    "status": "$([ $failed_checks -eq 0 ] && echo "healthy" || echo "unhealthy")"
}
EOF
    
    log "健康检查完成: $((total_checks - failed_checks))/$total_checks 项通过"
    
    if [ $failed_checks -gt 0 ]; then
        return 1
    else
        return 0
    fi
}

# 主函数
main() {
    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # 执行健康检查
    if generate_health_report; then
        log "系统状态良好"
        exit 0
    else
        error "系统存在问题"
        exit 1
    fi
}

# 执行主函数
main "$@"
