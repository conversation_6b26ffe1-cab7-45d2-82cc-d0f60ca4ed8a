#!/usr/bin/env node

/**
 * 基于 Git 的增量打包脚本
 * 根据 Git 提交差异创建增量更新包
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

function getGitChanges(fromCommit = 'HEAD~1', toCommit = 'HEAD') {
  try {
    // 获取变更的文件列表
    const changedFiles = execSync(`git diff --name-only ${fromCommit} ${toCommit}`, { encoding: 'utf8' })
      .trim()
      .split('\n')
      .filter(file => file && !file.startsWith('.git'))

    // 获取新增的文件
    const addedFiles = execSync(`git diff --name-status ${fromCommit} ${toCommit}`, { encoding: 'utf8' })
      .trim()
      .split('\n')
      .filter(line => line.startsWith('A\t'))
      .map(line => line.substring(2))

    // 获取删除的文件
    const deletedFiles = execSync(`git diff --name-status ${fromCommit} ${toCommit}`, { encoding: 'utf8' })
      .trim()
      .split('\n')
      .filter(line => line.startsWith('D\t'))
      .map(line => line.substring(2))

    // 获取修改的文件
    const modifiedFiles = changedFiles.filter(file => 
      !addedFiles.includes(file) && !deletedFiles.includes(file)
    )

    return {
      all: changedFiles,
      added: addedFiles,
      modified: modifiedFiles,
      deleted: deletedFiles
    }
  } catch (error) {
    console.error('❌ Git 操作失败:', error.message)
    return null
  }
}

function analyzeChanges(changes) {
  const analysis = {
    needsBuild: false,
    needsRestart: true,
    needsPrismaGenerate: false,
    needsInstall: false,
    categories: {
      config: [],
      components: [],
      api: [],
      pages: [],
      styles: [],
      prisma: [],
      other: []
    }
  }

  changes.all.forEach(file => {
    // 分类文件
    if (file.includes('package.json')) {
      analysis.needsInstall = true
      analysis.categories.config.push(file)
    } else if (file.includes('prisma/')) {
      analysis.needsPrismaGenerate = true
      analysis.categories.prisma.push(file)
    } else if (file.includes('components/')) {
      analysis.needsBuild = true
      analysis.categories.components.push(file)
    } else if (file.includes('app/api/')) {
      analysis.categories.api.push(file)
    } else if (file.includes('app/') && (file.endsWith('.tsx') || file.endsWith('.ts'))) {
      analysis.needsBuild = true
      analysis.categories.pages.push(file)
    } else if (file.endsWith('.css') || file.includes('tailwind')) {
      analysis.needsBuild = true
      analysis.categories.styles.push(file)
    } else if (file.includes('lib/') || file.includes('types/')) {
      analysis.categories.other.push(file)
    } else {
      analysis.categories.other.push(file)
    }
  })

  return analysis
}

function createGitIncrementalPackage(fromCommit = 'HEAD~1', toCommit = 'HEAD') {
  console.log(`🔄 创建基于 Git 的增量包 (${fromCommit}..${toCommit})...`)

  // 获取 Git 变更
  const changes = getGitChanges(fromCommit, toCommit)
  if (!changes || changes.all.length === 0) {
    console.log('✅ 没有文件变更')
    return null
  }

  // 分析变更
  const analysis = analyzeChanges(changes)

  console.log('📊 变更分析:')
  console.log(`   新增: ${changes.added.length} 个文件`)
  console.log(`   修改: ${changes.modified.length} 个文件`)
  console.log(`   删除: ${changes.deleted.length} 个文件`)
  console.log(`   需要构建: ${analysis.needsBuild ? '是' : '否'}`)
  console.log(`   需要安装依赖: ${analysis.needsInstall ? '是' : '否'}`)
  console.log(`   需要 Prisma 生成: ${analysis.needsPrismaGenerate ? '是' : '否'}`)

  // 创建输出目录
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19)
  const packageDir = `git-update-${timestamp}`
  fs.mkdirSync(packageDir, { recursive: true })

  // 复制变更的文件
  console.log('📁 复制变更文件...')
  const filesToCopy = [...changes.added, ...changes.modified]
  
  filesToCopy.forEach(file => {
    if (fs.existsSync(file)) {
      const destPath = path.join(packageDir, file)
      const destDir = path.dirname(destPath)
      
      if (!fs.existsSync(destDir)) {
        fs.mkdirSync(destDir, { recursive: true })
      }
      
      fs.copyFileSync(file, destPath)
      console.log(`   ✓ ${file}`)
    }
  })

  // 获取提交信息
  let commitInfo = {}
  try {
    const commitHash = execSync(`git rev-parse ${toCommit}`, { encoding: 'utf8' }).trim()
    const commitMessage = execSync(`git log -1 --pretty=format:"%s" ${toCommit}`, { encoding: 'utf8' }).trim()
    const commitAuthor = execSync(`git log -1 --pretty=format:"%an <%ae>" ${toCommit}`, { encoding: 'utf8' }).trim()
    const commitDate = execSync(`git log -1 --pretty=format:"%ci" ${toCommit}`, { encoding: 'utf8' }).trim()
    
    commitInfo = {
      hash: commitHash,
      message: commitMessage,
      author: commitAuthor,
      date: commitDate
    }
  } catch (error) {
    console.log('⚠️  无法获取提交信息')
  }

  // 创建更新清单
  const manifest = {
    type: 'git-incremental',
    timestamp,
    fromCommit,
    toCommit,
    commitInfo,
    changes,
    analysis,
    instructions: generateInstructions(analysis)
  }

  fs.writeFileSync(
    path.join(packageDir, 'git-update-manifest.json'),
    JSON.stringify(manifest, null, 2)
  )

  // 创建应用脚本
  createGitUpdateScript(packageDir, manifest)

  // 创建回滚脚本
  createRollbackScript(packageDir, manifest)

  console.log(`✅ Git 增量包已创建: ${packageDir}`)
  return packageDir
}

function generateInstructions(analysis) {
  const instructions = [
    '1. 备份当前版本',
    '2. 停止应用服务'
  ]

  if (analysis.needsInstall) {
    instructions.push('3. 安装新依赖: npm install --omit=dev')
  }

  instructions.push('4. 复制更新文件')

  if (analysis.needsPrismaGenerate) {
    instructions.push('5. 重新生成 Prisma: npx prisma generate')
  }

  if (analysis.needsBuild) {
    instructions.push('6. 重新构建: npm run build')
  }

  instructions.push('7. 重启应用服务')
  instructions.push('8. 验证应用功能')

  return instructions
}

function createGitUpdateScript(packageDir, manifest) {
  const script = `#!/bin/bash

# Git 增量更新脚本
echo "🔄 应用 Git 增量更新..."
echo "提交: ${manifest.fromCommit}..${manifest.toCommit}"
echo "消息: ${manifest.commitInfo.message || 'N/A'}"
echo "作者: ${manifest.commitInfo.author || 'N/A'}"
echo ""

# 检查环境
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 创建备份
BACKUP_DIR="backup-git-$(date +%Y%m%d_%H%M%S)"
echo "📁 创建备份: $BACKUP_DIR"
mkdir -p "$BACKUP_DIR"

# 备份要更新的文件
${manifest.changes.added.concat(manifest.changes.modified).map(file => 
  `[ -f "${file}" ] && cp -p "${file}" "$BACKUP_DIR/" || true`
).join('\n')}

echo "✅ 备份完成"

# 停止应用
echo "🛑 停止应用..."
pkill -f "next start" || true
sleep 2

# 安装依赖（如果需要）
${manifest.analysis.needsInstall ? `
echo "📦 安装依赖..."
npm install --omit=dev
` : ''}

# 复制新文件
echo "📋 复制更新文件..."
${manifest.changes.added.concat(manifest.changes.modified).map(file => 
  `cp "${file}" "${file}"`
).join('\n')}

# 删除文件
${manifest.changes.deleted.map(file => 
  `rm -f "${file}"`
).join('\n')}

echo "✅ 文件更新完成"

# Prisma 生成（如果需要）
${manifest.analysis.needsPrismaGenerate ? `
echo "🔧 重新生成 Prisma 客户端..."
npx prisma generate
` : ''}

# 重新构建（如果需要）
${manifest.analysis.needsBuild ? `
echo "🔨 重新构建应用..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ 构建失败"
    echo "使用备份恢复: cp $BACKUP_DIR/* ./"
    exit 1
fi
` : ''}

# 重启应用
echo "🚀 重启应用..."
npm start &

echo ""
echo "🎉 Git 增量更新完成!"
echo "备份位置: $BACKUP_DIR"
echo "如有问题，运行回滚脚本: ./rollback.sh"
`

  fs.writeFileSync(path.join(packageDir, 'apply-git-update.sh'), script)
  
  try {
    execSync(`chmod +x "${path.join(packageDir, 'apply-git-update.sh')}"`)
  } catch {}
}

function createRollbackScript(packageDir, manifest) {
  const script = `#!/bin/bash

# Git 更新回滚脚本
echo "🔄 回滚 Git 更新..."

# 查找最新的备份
LATEST_BACKUP=$(ls -1t backup-git-* 2>/dev/null | head -1)

if [ -z "$LATEST_BACKUP" ]; then
    echo "❌ 未找到备份目录"
    exit 1
fi

echo "📁 使用备份: $LATEST_BACKUP"

# 停止应用
echo "🛑 停止应用..."
pkill -f "next start" || true
sleep 2

# 恢复文件
echo "📋 恢复文件..."
cp -r "$LATEST_BACKUP"/* ./

# 重新构建（如果之前需要构建）
${manifest.analysis.needsBuild ? `
echo "🔨 重新构建..."
npm run build
` : ''}

# 重启应用
echo "🚀 重启应用..."
npm start &

echo "✅ 回滚完成"
`

  fs.writeFileSync(path.join(packageDir, 'rollback.sh'), script)
  
  try {
    execSync(`chmod +x "${path.join(packageDir, 'rollback.sh')}"`)
  } catch {}
}

// 命令行处理
if (require.main === module) {
  const args = process.argv.slice(2)
  const fromCommit = args[0] || 'HEAD~1'
  const toCommit = args[1] || 'HEAD'
  
  createGitIncrementalPackage(fromCommit, toCommit)
}

module.exports = { createGitIncrementalPackage }
