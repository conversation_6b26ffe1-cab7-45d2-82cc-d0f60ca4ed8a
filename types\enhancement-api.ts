// 装备强化API相关类型定义

import { EnhancementType, EnhancementResultType } from './enhancement'

/**
 * 装备强化API请求参数
 */
export interface EnhancementRequest {
  /** 装备ID */
  equipmentId: string
  /** 当前强化等级 */
  currentLevel: number
  /** 强化类型 */
  enhancementType: EnhancementType
  /** 是否启用解锁抓星星 */
  starcatchEnabled?: boolean
  /** 是否启用防止破坏 */
  preventEnabled?: boolean
  /** 迷你游戏加成 */
  minigameBonus?: number
}

/**
 * 装备强化API响应结果
 */
export interface EnhancementResponse {
  /** 请求是否成功 */
  success: boolean
  /** 强化结果 */
  result: {
    /** 结果类型 */
    type: EnhancementResultType
    /** 结果消息 */
    message: string
    /** 新的强化等级 */
    newLevel: number
    /** 之前的强化等级 */
    previousLevel: number
    /** 是否成功 */
    isSuccess: boolean
  }
  /** 消耗信息 */
  cost: {
    /** 金币消耗 */
    mesos: number
    /** 材料消耗 */
    materials?: {
      /** 材料名称 */
      name: string
      /** 消耗数量 */
      quantity: number
    }[]
  }
  /** 概率信息 */
  probability: {
    /** 成功概率 */
    success: number
    /** 失败概率 */
    failure: number
    /** 大失败概率 */
    majorFailure: number
    /** 等级下降概率 */
    failureDrop: number
  }
  /** 错误信息（如果有） */
  error?: string
  /** 请求ID（用于追踪） */
  requestId: string
  /** 时间戳 */
  timestamp: string
}

/**
 * API错误响应
 */
export interface EnhancementErrorResponse {
  success: false
  error: string
  errorCode: string
  details?: Record<string, any>
  requestId: string
  timestamp: string
}

/**
 * API密钥验证请求
 */
export interface ApiKeyRequest {
  /** API密钥 */
  apiKey: string
  /** 请求来源 */
  origin?: string
}

/**
 * API密钥验证响应
 */
export interface ApiKeyResponse {
  /** 验证是否成功 */
  valid: boolean
  /** 用户ID */
  userId?: string
  /** 权限级别 */
  permissions?: string[]
  /** 速率限制信息 */
  rateLimit?: {
    /** 每分钟请求限制 */
    requestsPerMinute: number
    /** 当前已使用次数 */
    currentUsage: number
    /** 重置时间 */
    resetTime: string
  }
}

/**
 * 强化统计信息
 */
export interface EnhancementStats {
  /** 总强化次数 */
  totalAttempts: number
  /** 成功次数 */
  successCount: number
  /** 失败次数 */
  failureCount: number
  /** 大失败次数 */
  majorFailureCount: number
  /** 成功率 */
  successRate: number
  /** 总消耗金币 */
  totalMesosSpent: number
}

/**
 * 装备信息
 */
export interface EquipmentInfo {
  /** 装备ID */
  id: string
  /** 装备名称 */
  name: string
  /** 装备类型 */
  type: string
  /** 装备分类 */
  category: string
  /** 基础等级要求 */
  baseLevel: number
  /** 最大强化等级 */
  maxEnhancementLevel: number
  /** 是否支持星力强化 */
  supportsStarforce: boolean
  /** 是否支持潜能 */
  supportsPotential: boolean
  /** 是否支持额外属性 */
  supportsBonusStat: boolean
}

/**
 * 强化历史记录
 */
export interface EnhancementHistory {
  /** 记录ID */
  id: string
  /** 装备ID */
  equipmentId: string
  /** 强化类型 */
  enhancementType: EnhancementType
  /** 之前等级 */
  previousLevel: number
  /** 新等级 */
  newLevel: number
  /** 结果类型 */
  resultType: EnhancementResultType
  /** 消耗金币 */
  mesosSpent: number
  /** 是否使用了特殊选项 */
  options: {
    starcatchEnabled: boolean
    preventEnabled: boolean
    minigameBonus: number
  }
  /** 创建时间 */
  createdAt: string
  /** 用户ID（如果已登录） */
  userId?: string
}

/**
 * 批量强化请求
 */
export interface BatchEnhancementRequest {
  /** 强化请求列表 */
  requests: EnhancementRequest[]
  /** 是否在失败时停止 */
  stopOnFailure?: boolean
  /** 最大尝试次数 */
  maxAttempts?: number
}

/**
 * 批量强化响应
 */
export interface BatchEnhancementResponse {
  /** 请求是否成功 */
  success: boolean
  /** 强化结果列表 */
  results: EnhancementResponse[]
  /** 统计信息 */
  stats: EnhancementStats
  /** 总耗时（毫秒） */
  totalDuration: number
  /** 请求ID */
  requestId: string
  /** 时间戳 */
  timestamp: string
}

/**
 * API配置
 */
export interface ApiConfig {
  /** API版本 */
  version: string
  /** 基础URL */
  baseUrl: string
  /** 默认超时时间（毫秒） */
  timeout: number
  /** 重试次数 */
  retryAttempts: number
  /** 速率限制 */
  rateLimit: {
    /** 每分钟请求数 */
    requestsPerMinute: number
    /** 突发请求数 */
    burstLimit: number
  }
}

/**
 * 错误代码枚举
 */
export enum EnhancementErrorCode {
  INVALID_EQUIPMENT_ID = 'INVALID_EQUIPMENT_ID',
  INVALID_LEVEL = 'INVALID_LEVEL',
  INVALID_ENHANCEMENT_TYPE = 'INVALID_ENHANCEMENT_TYPE',
  MAX_LEVEL_REACHED = 'MAX_LEVEL_REACHED',
  INSUFFICIENT_FUNDS = 'INSUFFICIENT_FUNDS',
  EQUIPMENT_NOT_FOUND = 'EQUIPMENT_NOT_FOUND',
  ENHANCEMENT_NOT_SUPPORTED = 'ENHANCEMENT_NOT_SUPPORTED',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  INVALID_API_KEY = 'INVALID_API_KEY',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR'
}

/**
 * 强化选项
 */
export interface EnhancementOptions {
  /** 是否启用解锁抓星星 */
  starcatchEnabled: boolean
  /** 是否启用防止破坏 */
  preventEnabled: boolean
  /** MVP折扣 */
  mvpDiscount: boolean
  /** 活动折扣 */
  eventDiscount: boolean
  /** 迷你游戏加成 */
  minigameBonus: number
}

/**
 * 强化材料信息
 */
export interface EnhancementMaterial {
  /** 材料ID */
  id: string
  /** 材料名称 */
  name: string
  /** 材料描述 */
  description: string
  /** 材料图标URL */
  iconUrl?: string
  /** 获取方式 */
  obtainMethod: string[]
}
