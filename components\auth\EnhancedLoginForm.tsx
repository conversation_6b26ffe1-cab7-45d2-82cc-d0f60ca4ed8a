'use client'

import { useState, useEffect } from 'react'
import { signIn } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Loader2, Eye, EyeOff, Mail } from 'lucide-react'

// 客户端安全的认证配置接口
interface ClientAuthConfig {
  showEmailField: boolean
  showUsernameField: boolean
  showGoogleButton: boolean
  emailPlaceholder: string
  loginFieldLabel: string
  credentialsEnabled: boolean
  googleEnabled: boolean
}

// 动态创建登录表单验证模式
function createLoginSchema(config: ClientAuthConfig) {
  return z.object({
    identifier: config.showEmailField || config.showUsernameField
      ? z.string().min(1, '请输入登录信息')
      : z.string().email('请输入有效的邮箱地址'),
    password: z.string().min(1, '请输入密码'),
  })
}

// 获取客户端认证配置的 hook
function useAuthConfig(): ClientAuthConfig | null {
  const [config, setConfig] = useState<ClientAuthConfig | null>(null)

  useEffect(() => {
    // 从服务端获取认证配置
    fetch('/api/config/auth')
      .then(res => res.json())
      .then(data => setConfig(data))
      .catch(err => {
        console.error('Failed to fetch auth config:', err)
        // 使用默认配置
        setConfig({
          showEmailField: true,
          showUsernameField: false,
          showGoogleButton: false,
          emailPlaceholder: '邮箱地址',
          loginFieldLabel: '邮箱地址',
          credentialsEnabled: true,
          googleEnabled: false
        })
      })
  }, [])

  return config
}

interface EnhancedLoginFormProps {
  callbackUrl?: string
}

export function EnhancedLoginForm({ callbackUrl }: EnhancedLoginFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()

  // 获取登录配置
  const config = useAuthConfig()

  // 如果配置还没加载完成，显示加载状态
  if (!config) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">加载中...</span>
      </div>
    )
  }

  const loginSchema = createLoginSchema(config)
  type LoginFormData = z.infer<typeof loginSchema>

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  })

  const onSubmit = async (data: LoginFormData) => {
    console.log('登录表单提交:', { ...data, password: '[HIDDEN]' })
    setIsLoading(true)
    setError('')

    try {
      console.log('调用 signIn...')
      const result = await signIn('credentials', {
        identifier: data.identifier,
        password: data.password,
        redirect: false,
        callbackUrl: callbackUrl || '/dashboard'
      })

      console.log('signIn 结果:', result)

      if (result?.error) {
        console.error('登录错误:', result.error)
        setError(result.error)
      } else if (result?.ok) {
        console.log('登录成功，跳转到:', callbackUrl || '/dashboard')
        router.push(callbackUrl || '/dashboard')
        router.refresh()
      } else {
        console.warn('未知的登录结果:', result)
        setError('登录状态未知，请重试')
      }
    } catch (error) {
      console.error('登录异常:', error)
      setError('登录失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  const handleGoogleSignIn = async () => {
    setIsLoading(true)
    setError('')

    try {
      console.log('开始 Google 登录...')
      await signIn('google', {
        callbackUrl: callbackUrl || '/dashboard'
      })
    } catch (error) {
      console.error('Google 登录异常:', error)
      setError('Google 登录失败，请稍后重试')
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 账号密码登录 */}
      {config.credentialsEnabled && (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="identifier">{config.loginFieldLabel}</Label>
            <Input
              id="identifier"
              type="text"
              placeholder={config.emailPlaceholder}
              {...register('identifier')}
              disabled={isLoading}
              autoComplete="username"
            />
            {errors.identifier && (
              <p className="text-sm text-red-600">{errors.identifier.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">密码</Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="请输入密码"
                {...register('password')}
                disabled={isLoading}
                autoComplete="current-password"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
                disabled={isLoading}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {errors.password && (
              <p className="text-sm text-red-600">{errors.password.message}</p>
            )}
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={isLoading}
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            登录
          </Button>
        </form>
      )}

      {/* 分隔线 */}
      {config.credentialsEnabled && config.googleEnabled && (
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <Separator className="w-full" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-white px-2 text-muted-foreground">或</span>
          </div>
        </div>
      )}

      {/* Google 登录 */}
      {config.googleEnabled && (
        <Button
          type="button"
          variant="outline"
          className="w-full"
          onClick={handleGoogleSignIn}
          disabled={isLoading}
        >
          {isLoading ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="currentColor"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="currentColor"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="currentColor"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
          )}
          使用 Google 登录
        </Button>
      )}

      {/* 登录提示信息 */}
      <div className="text-center text-sm text-muted-foreground">
        <p>
          {config.showEmailField && config.showUsernameField
            ? '您可以使用邮箱地址或用户名登录'
            : config.showEmailField
            ? '请使用邮箱地址登录'
            : '请使用用户名登录'}
        </p>
      </div>
    </div>
  )
}
