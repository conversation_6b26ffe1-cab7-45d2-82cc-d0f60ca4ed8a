我正在开发一个 React + Next.js + Prisma 全栈应用，请生成 TypeScript 代码。

要求：
- 所有 try/catch 块中不要直接访问 unknown 类型的 error.message
- 使用 instanceof Error 或其他类型守卫来安全地访问错误信息
- 不要使用类型断言（如 as any、as Error）
- 保持代码符合 strict 模式规范


我正在构建一个 React + Next.js + Prisma 应用，包含客户端组件、服务端组件、Server Actions 和数据库操作。

请为我生成代码时注意以下事项：
- 在任何 try/catch 中都不要直接访问 error.message（避免 TS18046）
- 使用类型守卫（如 instanceof Error 或 PrismaClientKnownRequestError）来判断错误类型
- 提供友好且类型安全的错误处理方式
- 如果涉及 Prisma 查询，请识别并处理其特定错误类型
- 返回清晰的错误响应给前端
- 不要使用 any 类型或类型断言



我正在开发一个 React + Next.js + Prisma 全栈应用，请生成 TypeScript 代码。

要求：
- 所有 try/catch 块中不要直接访问 unknown 类型的 error.message
- 使用 instanceof Error 或其他类型守卫来安全地访问错误信息
- 不要使用类型断言（如 as any、as Error）
- 保持代码符合 strict 模式规范