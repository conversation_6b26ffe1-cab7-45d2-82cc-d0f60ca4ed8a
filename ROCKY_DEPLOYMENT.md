# 🚀 冒险岛情报站 - Rocky Linux 9.5 生产环境部署指南

## 📋 概述

本指南专门针对 Rocky Linux 9.5 系统，专注于冒险岛情报站应用的部署。假设您的服务器已经配置好以下基础服务：
- PostgreSQL 数据库
- Redis 缓存服务
- 网络和防火墙配置

## 🛠️ 系统要求

### 最低配置
- **操作系统**: Rocky Linux 9.5
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB 可用空间
- **Node.js**: 18.17.0 或更高版本

### 推荐配置
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 40GB SSD

## 📦 环境准备

### 1. 更新系统包
```bash
# 更新系统
sudo dnf update -y

# 安装基础工具
sudo dnf install -y curl wget git unzip tar gzip
```

### 2. 安装 Node.js 18.x
```bash
# 添加 NodeSource 仓库
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -

# 安装 Node.js
sudo dnf install -y nodejs

# 验证安装
node --version  # 应显示 v18.x.x
npm --version   # 应显示 9.x.x 或更高
```

### 3. 安装 PM2 进程管理器
```bash
# 全局安装 PM2
sudo npm install -g pm2

# 验证安装
pm2 --version
```

### 4. 创建应用用户
```bash
# 创建专用用户
sudo useradd -m -s /bin/bash maplestory

# 创建必要目录
sudo -u maplestory mkdir -p /home/<USER>/{app,logs,backups}

# 设置目录权限
sudo chown -R maplestory:maplestory /home/<USER>/
```

## 🔧 防火墙配置

### 配置 firewalld（如果使用 Nginx）
```bash
# 启用防火墙
sudo systemctl enable --now firewalld

# 允许 HTTP 和 HTTPS
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https

# 重新加载防火墙规则
sudo firewall-cmd --reload
```

### 直接暴露应用端口（无 Nginx）
```bash
# 允许应用端口 3000
sudo firewall-cmd --permanent --add-port=3000/tcp

# 重新加载防火墙规则
sudo firewall-cmd --reload

# 查看开放端口
sudo firewall-cmd --list-ports
```

## 📁 应用部署

### 1. 切换到应用用户
```bash
sudo su - maplestory
```

### 2. 克隆应用代码
```bash
# 克隆代码到应用目录
git clone <your-repository-url> app
cd app

# 检查代码版本
git log --oneline -5
```

### 3. 安装应用依赖
```bash
# 安装生产依赖
npm ci --only=production

# 验证依赖安装
npm list --depth=0
```

### 4. 配置环境变量
```bash
# 创建生产环境变量文件
cp .env.example .env.production

# 编辑环境变量
nano .env.production
```

生产环境变量配置示例：
```env
# 数据库配置（使用现有的数据库服务）
DATABASE_URL="postgresql://username:password@localhost:5432/mxd_info_db"
REDIS_URL="redis://localhost:6379"

# NextAuth配置
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="production-secret-key-at-least-32-characters-long"

# 邮件服务
RESEND_API_KEY="re_your-production-resend-api-key"
EMAIL_FROM="<EMAIL>"

# 第三方服务
NEXT_PUBLIC_FINGERPRINT_JS_API_KEY="your-production-fingerprint-js-key"
STRIPE_SECRET_KEY="sk_live_your-production-stripe-key"
STRIPE_WEBHOOK_SECRET="whsec_your-production-webhook-secret"

# 安全配置
JWT_SECRET="production-jwt-secret-at-least-32-characters-long"
ENCRYPTION_KEY="production-encryption-key-at-least-32-characters-long"

# 应用配置
NEXT_PUBLIC_APP_URL="https://your-domain.com"
NEXT_PUBLIC_APP_NAME="冒险岛情报站"

# 生产配置
NODE_ENV="production"
LOG_LEVEL="info"
PORT="3000"

# 虚拟货币配置
CURRENCY_NAME="欢乐豆"
DEFAULT_CURRENCY_AMOUNT="100"

# 功能开关
ENABLE_REGISTRATION="true"
ENABLE_EMAIL_VERIFICATION="true"
ENABLE_FINGERPRINT_TRACKING="true"
```

### 5. 数据库初始化
```bash
# 生成 Prisma 客户端
npx prisma generate

# 推送数据库模式（确保数据库已创建）
npx prisma db push

# 运行种子数据
npx prisma db seed
```

### 6. 构建应用
```bash
# 构建生产版本
npm run build

# 验证构建结果
ls -la .next/
```

## 🚀 应用启动配置

### 1. 创建 PM2 配置文件
```bash
# 创建 PM2 配置
nano ecosystem.config.js
```

PM2 配置内容：
```javascript
module.exports = {
  apps: [{
    name: 'maplestory-info-station',
    script: 'npm',
    args: 'start',
    cwd: '/home/<USER>/app',
    
    // 集群配置
    instances: 'max',
    exec_mode: 'cluster',
    
    // 环境变量
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    
    // 环境变量文件
    env_file: '.env.production',
    
    // 日志配置
    log_file: '/home/<USER>/logs/app.log',
    out_file: '/home/<USER>/logs/out.log',
    error_file: '/home/<USER>/logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    
    // 性能配置
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',
    
    // 监控配置
    min_uptime: '10s',
    max_restarts: 10,
    
    // 健康检查
    health_check_grace_period: 3000,
    health_check_fatal_exceptions: true,
    
    // 自动重启配置
    autorestart: true,
    watch: false,
    
    // 进程配置
    kill_timeout: 5000,
    listen_timeout: 3000,
    
    // 时间配置
    time: true
  }]
}
```

### 2. 启动应用
```bash
# 启动应用
pm2 start ecosystem.config.js --env production

# 保存 PM2 配置
pm2 save

# 查看应用状态
pm2 status
pm2 logs
```

### 3. 设置开机自启
```bash
# 生成启动脚本
pm2 startup

# 执行生成的命令（通常需要 sudo 权限）
# 示例：sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u maplestory --hp /home/<USER>

# 保存当前 PM2 进程列表
pm2 save
```

## 📊 应用监控

### 1. 创建健康检查脚本
```bash
# 创建监控脚本
nano /home/<USER>/monitor.sh
chmod +x /home/<USER>/monitor.sh
```

监控脚本内容：
```bash
#!/bin/bash

LOG_FILE="/home/<USER>/logs/monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# 检查应用状态
if ! pm2 list | grep -q "online"; then
    echo "[$DATE] 应用离线，尝试重启..." >> $LOG_FILE
    pm2 restart maplestory-info-station
fi

# 检查应用响应
if ! curl -f http://localhost:3000/health >/dev/null 2>&1; then
    echo "[$DATE] 应用健康检查失败" >> $LOG_FILE
    pm2 restart maplestory-info-station
fi

# 检查磁盘空间
DISK_USAGE=$(df /home | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "[$DATE] 磁盘空间不足: ${DISK_USAGE}%" >> $LOG_FILE
fi

# 检查内存使用
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.2f", $3*100/$2}')
if (( $(echo "$MEMORY_USAGE > 90" | bc -l) )); then
    echo "[$DATE] 内存使用过高: ${MEMORY_USAGE}%" >> $LOG_FILE
fi
```

### 2. 设置定时监控
```bash
# 编辑 crontab
crontab -e

# 添加监控任务（每5分钟检查一次）
*/5 * * * * /home/<USER>/monitor.sh
```

## 🔄 应用更新部署

### 1. 创建部署脚本
```bash
# 创建部署脚本
nano /home/<USER>/deploy.sh
chmod +x /home/<USER>/deploy.sh
```

部署脚本内容：
```bash
#!/bin/bash

set -e

APP_DIR="/home/<USER>/app"
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)

echo "🚀 开始部署 - $DATE"

# 创建备份
echo "📦 创建备份..."
mkdir -p $BACKUP_DIR
tar -czf "$BACKUP_DIR/backup_$DATE.tar.gz" -C "$APP_DIR" .

# 进入应用目录
cd $APP_DIR

# 拉取最新代码
echo "📥 拉取最新代码..."
git fetch origin
git reset --hard origin/main

# 安装依赖
echo "📦 安装依赖..."
npm ci --only=production

# 构建应用
echo "🔨 构建应用..."
npm run build

# 数据库迁移
echo "🗄️ 数据库迁移..."
npx prisma generate
npx prisma db push

# 重启应用
echo "🔄 重启应用..."
pm2 restart maplestory-info-station

# 等待应用启动
sleep 10

# 健康检查
echo "🏥 健康检查..."
if curl -f http://localhost:3000/health; then
    echo "✅ 部署成功！"
else
    echo "❌ 健康检查失败"
    exit 1
fi

echo "🎉 部署完成 - $DATE"
```

### 2. 执行部署
```bash
# 运行部署脚本
./deploy.sh
```

## 📝 日志管理

### 1. 配置日志轮转
```bash
# 创建 logrotate 配置
sudo nano /etc/logrotate.d/maplestory
```

logrotate 配置内容：
```
/home/<USER>/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 maplestory maplestory
    postrotate
        sudo -u maplestory pm2 reloadLogs
    endscript
}
```

### 2. 查看日志
```bash
# 查看应用日志
pm2 logs maplestory-info-station

# 查看实时日志
pm2 logs --lines 100

# 查看错误日志
pm2 logs --err

# 查看特定时间的日志
tail -f /home/<USER>/logs/app.log
```

## 🔧 故障排查

### 常见问题解决

#### 1. 应用启动失败
```bash
# 检查 PM2 状态
pm2 status

# 查看详细错误
pm2 logs maplestory-info-station --err

# 检查环境变量
cat .env.production

# 手动启动测试
npm start
```

#### 2. 端口被占用
```bash
# 查看端口占用
sudo ss -tlnp | grep :3000

# 杀死占用进程
sudo kill -9 <PID>
```

#### 3. 内存不足
```bash
# 查看内存使用
free -h
pm2 monit

# 重启应用释放内存
pm2 restart maplestory-info-station
```

#### 4. 磁盘空间不足
```bash
# 查看磁盘使用
df -h

# 清理日志文件
sudo logrotate -f /etc/logrotate.d/maplestory

# 清理旧备份
find /home/<USER>/backups -name "backup_*" -mtime +30 -delete
```

## 🔒 安全配置

### 1. 文件权限设置
```bash
# 设置环境变量文件权限
chmod 600 /home/<USER>/app/.env.production

# 设置应用目录权限
chown -R maplestory:maplestory /home/<USER>/app
```

### 2. SELinux 配置（如果启用）
```bash
# 检查 SELinux 状态
getenforce

# 如果需要，设置 SELinux 上下文
sudo setsebool -P httpd_can_network_connect 1
```

## 📈 性能优化

### 1. 系统优化
```bash
# 增加文件描述符限制
echo "maplestory soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "maplestory hard nofile 65536" | sudo tee -a /etc/security/limits.conf
```

### 2. Node.js 优化
```bash
# 在 PM2 配置中添加 Node.js 优化参数
node_args: '--max-old-space-size=1024 --optimize-for-size'
```

## 🎯 验证部署

### 1. 功能测试
```bash
# 健康检查
curl http://localhost:3000/health

# API 测试
curl http://localhost:3000/api/auth/csrf

# 页面访问测试
curl -I http://localhost:3000
```

### 2. 性能测试
```bash
# 安装 Apache Bench（可选）
sudo dnf install httpd-tools

# 简单压力测试
ab -n 100 -c 10 http://localhost:3000/
```

---

## 📞 技术支持

如果在部署过程中遇到问题，请检查：
1. 系统日志：`sudo journalctl -xe`
2. 应用日志：`pm2 logs`
3. 网络连接：`curl http://localhost:3000/health`
4. 进程状态：`pm2 status`

部署完成后，您的冒险岛情报站应用将在 Rocky Linux 9.5 上稳定运行！
