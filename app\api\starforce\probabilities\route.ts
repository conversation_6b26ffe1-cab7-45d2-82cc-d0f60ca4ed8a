import { NextResponse } from 'next/server'
import { exportProbabilityDataAsJSON } from '@/lib/starforce-data-loader'

/**
 * 获取星力强化概率数据API
 * GET /api/starforce/probabilities
 * 
 * @description 提供星力强化概率数据给客户端使用
 * @returns {Record<number, StarforceProbability>} 概率数据对象
 */
export async function GET() {
  try {
    const probabilityData = exportProbabilityDataAsJSON()
    
    return NextResponse.json({
      success: true,
      data: probabilityData,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('获取星力概率数据失败:', error)
    
    return NextResponse.json({
      success: false,
      error: '无法加载星力概率数据',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
