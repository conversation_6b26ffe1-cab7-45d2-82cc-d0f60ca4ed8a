#!/usr/bin/env node

/**
 * 增量打包脚本
 * 只打包修改过的文件，生成增量更新包
 */

const fs = require('fs')
const path = require('path')
const crypto = require('crypto')
const { execSync } = require('child_process')

console.log('🔄 创建增量更新包...')

// 配置
const config = {
  // 需要检查的目录和文件
  watchPaths: [
    'lib',
    'components', 
    'app',
    'types',
    'prisma/schema.prisma',
    'next.config.mjs',
    'package.json'
  ],
  
  // 输出目录
  outputDir: 'incremental-updates',
  
  // 哈希文件存储位置
  hashFile: 'file-hashes.json',
  
  // 排除的文件模式
  excludePatterns: [
    /node_modules/,
    /\.git/,
    /\.next/,
    /\.env/,
    /\.log$/,
    /\.tmp$/
  ]
}

// 计算文件哈希
function calculateFileHash(filePath) {
  try {
    const content = fs.readFileSync(filePath)
    return crypto.createHash('md5').update(content).digest('hex')
  } catch (error) {
    return null
  }
}

// 获取目录下所有文件
function getAllFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath)

  files.forEach(file => {
    const fullPath = path.join(dirPath, file)
    
    // 检查是否应该排除
    const shouldExclude = config.excludePatterns.some(pattern => 
      pattern.test(fullPath)
    )
    
    if (shouldExclude) return

    if (fs.statSync(fullPath).isDirectory()) {
      arrayOfFiles = getAllFiles(fullPath, arrayOfFiles)
    } else {
      arrayOfFiles.push(fullPath)
    }
  })

  return arrayOfFiles
}

// 加载之前的哈希记录
function loadPreviousHashes() {
  try {
    if (fs.existsSync(config.hashFile)) {
      const content = fs.readFileSync(config.hashFile, 'utf8')
      return JSON.parse(content)
    }
  } catch (error) {
    console.log('⚠️  无法加载之前的哈希记录，将创建完整包')
  }
  return {}
}

// 保存当前哈希记录
function saveCurrentHashes(hashes) {
  fs.writeFileSync(config.hashFile, JSON.stringify(hashes, null, 2))
}

// 复制文件到目标目录
function copyFile(src, dest) {
  const destDir = path.dirname(dest)
  if (!fs.existsSync(destDir)) {
    fs.mkdirSync(destDir, { recursive: true })
  }
  fs.copyFileSync(src, dest)
}

// 主函数
function createIncrementalPackage() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19)
  const packageDir = path.join(config.outputDir, `update-${timestamp}`)
  
  // 创建输出目录
  if (!fs.existsSync(packageDir)) {
    fs.mkdirSync(packageDir, { recursive: true })
  }

  // 加载之前的哈希
  const previousHashes = loadPreviousHashes()
  const currentHashes = {}
  const changedFiles = []
  const newFiles = []
  const deletedFiles = []

  console.log('📁 扫描文件变化...')

  // 检查所有监控路径
  config.watchPaths.forEach(watchPath => {
    if (!fs.existsSync(watchPath)) {
      console.log(`⚠️  路径不存在: ${watchPath}`)
      return
    }

    const isFile = fs.statSync(watchPath).isFile()
    const files = isFile ? [watchPath] : getAllFiles(watchPath)

    files.forEach(filePath => {
      const currentHash = calculateFileHash(filePath)
      if (!currentHash) return

      currentHashes[filePath] = currentHash
      const previousHash = previousHashes[filePath]

      if (!previousHash) {
        // 新文件
        newFiles.push(filePath)
        changedFiles.push(filePath)
      } else if (previousHash !== currentHash) {
        // 修改的文件
        changedFiles.push(filePath)
      }
    })
  })

  // 检查删除的文件
  Object.keys(previousHashes).forEach(filePath => {
    if (!currentHashes[filePath]) {
      deletedFiles.push(filePath)
    }
  })

  console.log(`📊 变化统计:`)
  console.log(`   新增文件: ${newFiles.length}`)
  console.log(`   修改文件: ${changedFiles.length - newFiles.length}`)
  console.log(`   删除文件: ${deletedFiles.length}`)

  if (changedFiles.length === 0 && deletedFiles.length === 0) {
    console.log('✅ 没有文件变化，无需创建更新包')
    return null
  }

  // 复制变化的文件
  console.log('📦 复制变化的文件...')
  changedFiles.forEach(filePath => {
    const destPath = path.join(packageDir, filePath)
    copyFile(filePath, destPath)
    console.log(`   ✓ ${filePath}`)
  })

  // 创建更新清单
  const manifest = {
    timestamp,
    version: getProjectVersion(),
    changes: {
      added: newFiles,
      modified: changedFiles.filter(f => !newFiles.includes(f)),
      deleted: deletedFiles
    },
    totalFiles: changedFiles.length,
    instructions: [
      '1. 停止应用服务',
      '2. 备份当前版本',
      '3. 复制文件到对应位置',
      '4. 如有 package.json 变化，运行 npm install',
      '5. 如有 schema.prisma 变化，运行 npx prisma generate',
      '6. 重新构建: npm run build',
      '7. 重启应用服务'
    ]
  }

  // 保存清单文件
  fs.writeFileSync(
    path.join(packageDir, 'update-manifest.json'),
    JSON.stringify(manifest, null, 2)
  )

  // 创建应用脚本
  createUpdateScript(packageDir, manifest)

  // 保存当前哈希
  saveCurrentHashes(currentHashes)

  // 创建压缩包
  const zipFile = `${packageDir}.zip`
  try {
    execSync(`cd "${config.outputDir}" && zip -r "${path.basename(zipFile)}" "${path.basename(packageDir)}"`)
    console.log(`📦 压缩包已创建: ${zipFile}`)
  } catch (error) {
    console.log('⚠️  创建压缩包失败，请手动压缩目录')
  }

  console.log(`✅ 增量更新包已创建: ${packageDir}`)
  return packageDir
}

// 获取项目版本
function getProjectVersion() {
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
    return packageJson.version || '1.0.0'
  } catch {
    return '1.0.0'
  }
}

// 创建更新应用脚本
function createUpdateScript(packageDir, manifest) {
  // Linux/Mac 脚本
  const bashScript = `#!/bin/bash

# 增量更新应用脚本
echo "🔄 应用增量更新..."
echo "更新时间: ${manifest.timestamp}"
echo "版本: ${manifest.version}"
echo ""

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 备份当前版本
BACKUP_DIR="backup-$(date +%Y%m%d_%H%M%S)"
echo "📁 创建备份: $BACKUP_DIR"
mkdir -p "$BACKUP_DIR"

# 备份要更新的文件
${manifest.changes.modified.concat(manifest.changes.added).map(file => 
  `[ -f "${file}" ] && cp -p "${file}" "$BACKUP_DIR/${file}.bak" || true`
).join('\n')}

echo "✅ 备份完成"

# 停止应用
echo "🛑 停止应用..."
pkill -f "next start" || true
sleep 2

# 复制新文件
echo "📋 更新文件..."
${manifest.changes.modified.concat(manifest.changes.added).map(file => 
  `cp "${file}" "${file}"`
).join('\n')}

# 删除文件
${manifest.changes.deleted.map(file => 
  `rm -f "${file}"`
).join('\n')}

echo "✅ 文件更新完成"

# 检查是否需要安装依赖
if [ -f "package.json.new" ]; then
    echo "📦 检测到 package.json 变化，更新依赖..."
    npm install --omit=dev
fi

# 检查是否需要更新 Prisma
if [ -f "prisma/schema.prisma.new" ]; then
    echo "🔧 检测到 schema.prisma 变化，更新 Prisma..."
    npx prisma generate
fi

# 重新构建
echo "🔨 重新构建应用..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ 构建成功"
    
    # 重启应用
    echo "🚀 重启应用..."
    npm start &
    
    echo ""
    echo "🎉 更新完成!"
    echo "备份位置: $BACKUP_DIR"
    echo "如有问题，可使用备份文件恢复"
else
    echo "❌ 构建失败，请检查错误信息"
    echo "可使用备份文件恢复: $BACKUP_DIR"
    exit 1
fi
`

  // Windows 脚本
  const batScript = `@echo off
echo 🔄 应用增量更新...
echo 更新时间: ${manifest.timestamp}
echo 版本: ${manifest.version}
echo.

REM 检查是否在正确的目录
if not exist "package.json" (
    echo ❌ 错误: 请在项目根目录运行此脚本
    pause
    exit /b 1
)

REM 创建备份
set BACKUP_DIR=backup-%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
echo 📁 创建备份: %BACKUP_DIR%
mkdir "%BACKUP_DIR%" 2>nul

echo ✅ 备份完成

REM 停止应用
echo 🛑 停止应用...
taskkill /f /im node.exe 2>nul

REM 复制新文件
echo 📋 更新文件...
${manifest.changes.modified.concat(manifest.changes.added).map(file => 
  `copy "${file}" "${file}" >nul`
).join('\n')}

echo ✅ 文件更新完成

REM 重新构建
echo 🔨 重新构建应用...
npm run build

if %errorlevel% equ 0 (
    echo ✅ 构建成功
    echo 🚀 重启应用...
    start npm start
    echo.
    echo 🎉 更新完成!
) else (
    echo ❌ 构建失败，请检查错误信息
    pause
)
`

  fs.writeFileSync(path.join(packageDir, 'apply-update.sh'), bashScript)
  fs.writeFileSync(path.join(packageDir, 'apply-update.bat'), batScript)
  
  // 设置执行权限
  try {
    execSync(`chmod +x "${path.join(packageDir, 'apply-update.sh')}"`)
  } catch {}
}

// 运行
if (require.main === module) {
  createIncrementalPackage()
}

module.exports = { createIncrementalPackage }
