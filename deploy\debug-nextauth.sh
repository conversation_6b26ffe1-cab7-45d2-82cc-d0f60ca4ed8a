#!/bin/bash

# NextAuth.js 调试脚本 - 诊断反向代理问题

echo "🔍 NextAuth.js 调试诊断..."
echo ""

# 1. 检查环境变量
echo "1. 检查环境变量配置..."
if [ -f ".env.local" ]; then
    echo "✅ .env.local 文件存在"
    
    if grep -q "NEXTAUTH_URL.*https://mxd.hyhuman.xyz" .env.local; then
        echo "✅ NEXTAUTH_URL 配置正确"
    else
        echo "❌ NEXTAUTH_URL 配置错误或缺失"
    fi
    
    if grep -q "AUTH_TRUST_HOST.*true" .env.local; then
        echo "✅ AUTH_TRUST_HOST 已启用"
    else
        echo "⚠️  建议添加 AUTH_TRUST_HOST=true"
    fi
    
    if grep -q "NEXTAUTH_SECRET" .env.local; then
        echo "✅ NEXTAUTH_SECRET 已配置"
    else
        echo "❌ NEXTAUTH_SECRET 缺失"
    fi
else
    echo "❌ .env.local 文件不存在"
fi

# 2. 检查应用是否运行
echo ""
echo "2. 检查应用状态..."
if pgrep -f "next start" > /dev/null; then
    echo "✅ Next.js 应用正在运行"
    
    # 检查端口
    if ss -tlnp | grep ":3000" > /dev/null; then
        echo "✅ 端口 3000 正在监听"
    else
        echo "❌ 端口 3000 未监听"
    fi
else
    echo "❌ Next.js 应用未运行"
fi

# 3. 测试本地连接
echo ""
echo "3. 测试本地连接..."
if curl -s http://localhost:3000/api/auth/signin > /dev/null; then
    echo "✅ 本地 NextAuth 端点可访问"
else
    echo "❌ 本地 NextAuth 端点不可访问"
fi

# 4. 测试 HTTPS 连接
echo ""
echo "4. 测试 HTTPS 连接..."
RESPONSE=$(curl -I -s https://mxd.hyhuman.xyz/api/auth/signin)
if echo "$RESPONSE" | grep -q "200\|302"; then
    echo "✅ HTTPS NextAuth 端点可访问"
else
    echo "❌ HTTPS NextAuth 端点不可访问"
    echo "响应: $RESPONSE"
fi

# 5. 检查 cookies
echo ""
echo "5. 测试 cookies 设置..."
COOKIE_TEST=$(curl -I -s https://mxd.hyhuman.xyz/api/auth/signin | grep -i "set-cookie")
if [ -n "$COOKIE_TEST" ]; then
    echo "✅ Cookies 正在设置"
    echo "Cookies: $COOKIE_TEST"
else
    echo "⚠️  未检测到 cookies 设置"
fi

# 6. 检查重定向
echo ""
echo "6. 测试重定向逻辑..."
REDIRECT_TEST=$(curl -I -s https://mxd.hyhuman.xyz/api/auth/signout | grep -i "location")
if [ -n "$REDIRECT_TEST" ]; then
    echo "✅ 重定向头部存在"
    echo "重定向: $REDIRECT_TEST"
    
    # 检查是否重定向到 localhost
    if echo "$REDIRECT_TEST" | grep -q "localhost"; then
        echo "❌ 重定向到 localhost，这是问题所在！"
    else
        echo "✅ 重定向目标正确"
    fi
else
    echo "⚠️  未检测到重定向头部"
fi

# 7. 创建测试页面
echo ""
echo "7. 创建调试测试页面..."
cat > debug-auth.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>NextAuth 调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .test { margin: 20px 0; padding: 10px; border: 1px solid #ddd; }
        .success { background: #d4edda; }
        .error { background: #f8d7da; }
    </style>
</head>
<body>
    <h1>NextAuth.js 调试测试</h1>
    
    <div class="test">
        <h3>测试链接</h3>
        <p><a href="https://mxd.hyhuman.xyz/api/auth/signin" target="_blank">登录页面</a></p>
        <p><a href="https://mxd.hyhuman.xyz/api/auth/signout" target="_blank">登出页面</a></p>
        <p><a href="https://mxd.hyhuman.xyz/api/auth/session" target="_blank">会话信息</a></p>
        <p><a href="https://mxd.hyhuman.xyz/login" target="_blank">应用登录页</a></p>
    </div>
    
    <div class="test">
        <h3>JavaScript 测试</h3>
        <button onclick="testSession()">测试会话</button>
        <button onclick="testSignOut()">测试登出</button>
        <div id="result"></div>
    </div>
    
    <script>
        async function testSession() {
            try {
                const response = await fetch('/api/auth/session');
                const data = await response.json();
                document.getElementById('result').innerHTML = 
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<div class="error">错误: ' + error.message + '</div>';
            }
        }
        
        async function testSignOut() {
            try {
                const response = await fetch('/api/auth/signout', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                if (response.redirected) {
                    document.getElementById('result').innerHTML = 
                        '<div>重定向到: ' + response.url + '</div>';
                } else {
                    document.getElementById('result').innerHTML = 
                        '<div>状态: ' + response.status + '</div>';
                }
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<div class="error">错误: ' + error.message + '</div>';
            }
        }
    </script>
</body>
</html>
EOF

echo "✅ 已创建 debug-auth.html 测试页面"

# 8. 生成修复建议
echo ""
echo "🔧 修复建议:"
echo ""

if ! grep -q "AUTH_TRUST_HOST.*true" .env.local 2>/dev/null; then
    echo "1. 在 .env.local 中添加: AUTH_TRUST_HOST=true"
fi

if ! grep -q "NEXTAUTH_URL.*https://mxd.hyhuman.xyz" .env.local 2>/dev/null; then
    echo "2. 确保 NEXTAUTH_URL=https://mxd.hyhuman.xyz"
fi

echo "3. 检查 Nginx 配置包含所有必要的代理头部"
echo "4. 重启应用和 Nginx 服务"

# 9. 创建快速修复脚本
cat > quick-fix.sh << 'EOF'
#!/bin/bash
echo "🚀 快速修复 NextAuth 问题..."

# 更新环境变量
./fix-nextauth-proxy.sh

# 重启应用
echo "重启应用..."
pkill -f "next start" || true
sleep 2
pnpm start &

echo "✅ 修复完成，请测试登录功能"
EOF

chmod +x quick-fix.sh
echo "✅ 已创建 quick-fix.sh 快速修复脚本"

echo ""
echo "📋 下一步操作:"
echo "1. 运行快速修复: ./quick-fix.sh"
echo "2. 检查 Nginx 配置: ./check-nginx-config.sh"
echo "3. 在浏览器中打开 debug-auth.html 进行测试"
echo "4. 查看应用日志检查错误信息"
