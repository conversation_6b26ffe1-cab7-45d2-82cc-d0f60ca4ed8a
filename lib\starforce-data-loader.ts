/**
 * 星力强化数据加载器（服务端专用）
 * 只在服务端环境中使用，负责从CSV文件加载概率数据
 */

import fs from 'fs'
import path from 'path'

/**
 * CSV数据行结构
 */
export interface StarforceProbabilityRow {
  currentLevel: number
  nextLevel: number
  successRate: number
  failHoldRate: number
  failDropRate: number
  boomRate: number
}

/**
 * 星力强化概率数据
 */
export interface StarforceProbability {
  /** 成功概率 (0-1) */
  success: number
  /** 失败保级概率 (0-1) */
  failHold: number
  /** 失败降级概率 (0-1) */
  failDrop: number
  /** 失败损坏概率 (0-1) */
  boom: number
}

/**
 * 星力概率数据缓存
 */
let probabilityCache: Map<number, StarforceProbability> | null = null

/**
 * 读取并解析CSV文件中的星力强化概率数据（服务端专用）
 */
export function loadStarforceProbabilitiesServer(): Map<number, StarforceProbability> {
  // 如果已缓存，直接返回
  if (probabilityCache) {
    return probabilityCache
  }

  try {
    const csvPath = path.join(process.cwd(), 'docs', 'starforcePB.csv')
    const csvContent = fs.readFileSync(csvPath, 'utf-8')
    
    const lines = csvContent.trim().split('\n')
    const headers = lines[0].split(',')
    
    // 验证CSV格式
    const expectedHeaders = ['当前级', '下一级', '成功率', '失败保级', '失败降级', '失败损坏']
    if (!expectedHeaders.every((header, index) => headers[index]?.trim() === header)) {
      throw new Error(`CSV格式不正确。期望的列: ${expectedHeaders.join(', ')}`)
    }

    const probabilityMap = new Map<number, StarforceProbability>()

    // 解析数据行
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim()
      if (!line) continue // 跳过空行

      const values = line.split(',').map(v => v.trim())
      if (values.length !== 6) {
        console.warn(`跳过格式不正确的行 ${i + 1}: ${line}`)
        continue
      }

      const currentLevel = parseInt(values[0])
      const nextLevel = parseInt(values[1])
      const successRate = parseFloat(values[2])
      const failHoldRate = parseFloat(values[3])
      const failDropRate = parseFloat(values[4])
      const boomRate = parseFloat(values[5])

      // 验证数据有效性
      if (isNaN(currentLevel) || isNaN(nextLevel) || 
          isNaN(successRate) || isNaN(failHoldRate) || 
          isNaN(failDropRate) || isNaN(boomRate)) {
        console.warn(`跳过包含无效数值的行 ${i + 1}: ${line}`)
        continue
      }

      // 验证概率总和
      const totalProbability = successRate + failHoldRate + failDropRate + boomRate
      if (Math.abs(totalProbability - 1.0) > 0.001) {
        console.warn(`警告：第 ${i + 1} 行概率总和不等于1.0: ${totalProbability}`)
      }

      // 验证星级连续性
      if (nextLevel !== currentLevel + 1) {
        console.warn(`警告：第 ${i + 1} 行星级不连续: ${currentLevel} -> ${nextLevel}`)
      }

      probabilityMap.set(currentLevel, {
        success: successRate,
        failHold: failHoldRate,
        failDrop: failDropRate,
        boom: boomRate
      })
    }

    console.log(`✅ 成功加载 ${probabilityMap.size} 个星级的概率数据`)
    
    // 缓存结果
    probabilityCache = probabilityMap
    return probabilityMap

  } catch (error) {
    console.error('❌ 加载星力概率数据失败:', error)
    throw new Error(`无法加载星力概率数据: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

/**
 * 获取指定星级的基础概率（服务端专用）
 */
export function getBaseProbabilityServer(level: number): StarforceProbability {
  const probabilityMap = loadStarforceProbabilitiesServer()
  const probability = probabilityMap.get(level)
  
  if (!probability) {
    throw new Error(`未找到星级 ${level} 的概率数据`)
  }
  
  return { ...probability } // 返回副本避免修改原数据
}

/**
 * 验证概率数据的完整性（服务端专用）
 */
export function validateProbabilityDataServer(): {
  isValid: boolean
  errors: string[]
  warnings: string[]
} {
  const errors: string[] = []
  const warnings: string[] = []

  try {
    const probabilityMap = loadStarforceProbabilitiesServer()
    
    // 检查是否有0-24星的完整数据
    for (let level = 0; level <= 24; level++) {
      if (!probabilityMap.has(level)) {
        errors.push(`缺少星级 ${level} 的概率数据`)
      } else {
        const prob = probabilityMap.get(level)!
        
        // 检查概率值范围
        if (prob.success < 0 || prob.success > 1) {
          errors.push(`星级 ${level} 成功率超出范围: ${prob.success}`)
        }
        if (prob.failHold < 0 || prob.failHold > 1) {
          errors.push(`星级 ${level} 失败保级率超出范围: ${prob.failHold}`)
        }
        if (prob.failDrop < 0 || prob.failDrop > 1) {
          errors.push(`星级 ${level} 失败降级率超出范围: ${prob.failDrop}`)
        }
        if (prob.boom < 0 || prob.boom > 1) {
          errors.push(`星级 ${level} 损坏率超出范围: ${prob.boom}`)
        }
        
        // 检查概率总和
        const total = prob.success + prob.failHold + prob.failDrop + prob.boom
        if (Math.abs(total - 1.0) > 0.001) {
          warnings.push(`星级 ${level} 概率总和不等于1.0: ${total}`)
        }
      }
    }
    
  } catch (error) {
    errors.push(`加载概率数据失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 将概率数据转换为JSON格式（用于传递给客户端）
 */
export function exportProbabilityDataAsJSON(): Record<number, StarforceProbability> {
  const probabilityMap = loadStarforceProbabilitiesServer()
  const result: Record<number, StarforceProbability> = {}
  
  probabilityMap.forEach((probability, level) => {
    result[level] = probability
  })
  
  return result
}
