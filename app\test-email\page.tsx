'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Textarea } from '@/components/ui/textarea'

export default function TestEmailPage() {
  const [email, setEmail] = useState('<EMAIL>')
  const [token, setToken] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')

  const handleTestRegister = async () => {
    setIsLoading(true)
    setMessage('')
    setError('')

    try {
      console.log('🧪 开始测试注册流程')
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: '测试用户',
          email: email,
          password: 'Test123456'
        })
      })
      
      const result = await response.json()
      console.log('🧪 注册响应:', result)

      if (response.ok) {
        setMessage('注册成功！请查看控制台获取验证链接。')
      } else {
        setError(result.error || '注册失败')
      }
    } catch (error) {
      console.error('🧪 注册测试错误:', error)
      setError('注册测试失败')
    } finally {
      setIsLoading(false)
    }
  }

  const handleTestVerify = async () => {
    if (!token.trim()) {
      setError('请输入验证令牌')
      return
    }

    setIsLoading(true)
    setMessage('')
    setError('')

    try {
      console.log('🧪 开始测试邮箱验证')
      const response = await fetch('/api/auth/verify-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: token.trim() })
      })
      
      const result = await response.json()
      console.log('🧪 验证响应:', result)

      if (response.ok) {
        setMessage('邮箱验证成功！')
      } else {
        setError(result.error || '验证失败')
      }
    } catch (error) {
      console.error('🧪 验证测试错误:', error)
      setError('验证测试失败')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>邮箱验证测试工具</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {message && (
            <Alert>
              <AlertDescription>{message}</AlertDescription>
            </Alert>
          )}
          
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 注册测试 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">1. 测试注册流程</h3>
            
            <div className="space-y-2">
              <Label htmlFor="email">测试邮箱</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="输入测试邮箱"
              />
            </div>

            <Button 
              onClick={handleTestRegister}
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? '注册中...' : '测试注册'}
            </Button>
          </div>

          {/* 验证测试 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">2. 测试邮箱验证</h3>
            
            <div className="space-y-2">
              <Label htmlFor="token">验证令牌</Label>
              <Textarea
                id="token"
                value={token}
                onChange={(e) => setToken(e.target.value)}
                placeholder="从控制台复制验证令牌粘贴到这里"
                rows={3}
              />
              <p className="text-sm text-gray-500">
                注册成功后，验证令牌会在控制台输出，复制粘贴到这里进行测试
              </p>
            </div>

            <Button 
              onClick={handleTestVerify}
              disabled={isLoading || !token.trim()}
              variant="outline"
              className="w-full"
            >
              {isLoading ? '验证中...' : '测试验证'}
            </Button>
          </div>

          {/* 说明 */}
          <div className="p-4 bg-blue-50 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">使用说明</h3>
            <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
              <li>输入测试邮箱地址，点击"测试注册"</li>
              <li>打开浏览器开发者工具的 Console 标签页</li>
              <li>查找验证链接或令牌（以 🔗 开头的行）</li>
              <li>复制验证链接中的 token 参数值</li>
              <li>粘贴到"验证令牌"输入框中</li>
              <li>点击"测试验证"完成验证流程</li>
            </ol>
          </div>

          <div className="p-4 bg-yellow-50 rounded-lg">
            <h3 className="font-semibold text-yellow-900 mb-2">控制台日志说明</h3>
            <ul className="text-sm text-yellow-800 space-y-1">
              <li>• 📝 = 注册流程日志</li>
              <li>• 📧 = 邮件发送日志</li>
              <li>• 🔗 = 验证链接（开发模式）</li>
              <li>• ✅ = 成功操作</li>
              <li>• ❌ = 错误信息</li>
              <li>• ⚠️ = 警告信息</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
