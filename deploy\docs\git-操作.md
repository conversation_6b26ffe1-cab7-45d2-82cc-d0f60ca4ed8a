


# 1. 删除本地分支（如果需要）
git branch -d jules_wip_5047491809906471586

# 2. 删除远程分支
git push origin --delete jules_wip_5047491809906471586


✅ 方法 1：提交你的本地更改再拉取
适用于你本地改动是有用的，想要保留并合并远程修改的情况。
git add docs/游戏装备强化模拟器.md
git commit -m "本地修改：更新装备强化模拟器文档"
git pull

✅ 方法 2：暂存（stash）本地更改，完成拉取后再恢复
适用于你只是临时改了文件，还不想提交，想先拉取远程修改的情况。

git stash           # 保存当前修改
git pull            # 拉取远程代码
git stash pop       # 恢复你刚刚 stash 的修改
如果合并后出现冲突，Git 会提示你手动解决冲突。

✅ 方法 3：放弃本地修改（谨慎使用）
适用于你不再需要本地的更改，想直接用远程版本覆盖。
git checkout -- docs/游戏装备强化模拟器.md  # 丢弃本地更改
git pull



git push origin feature/api-integration
（可选）✅ 3. 如果你是第一次推送该分支，并想设置为默认上游：
git push --set-upstream origin feature/api-integration
git remote add origin https://github.com/你的用户名/你的仓库名.git

