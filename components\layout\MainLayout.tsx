'use client'

import { useSession } from 'next-auth/react'
import { usePathname } from 'next/navigation'
import { Sidebar } from '@/components/sidebar'

interface MainLayoutProps {
  children: React.ReactNode
}

export function MainLayout({ children }: MainLayoutProps) {
  const { data: session } = useSession()
  const pathname = usePathname()

  // 判断是否在特殊页面（不需要侧边栏）
  const isAuthPage = pathname?.startsWith('/login') || pathname?.startsWith('/register') || pathname?.startsWith('/verify-email') || pathname?.startsWith('/activation-status')
  const isDashboardPage = pathname?.startsWith('/dashboard')
  
  // 如果是认证页面或仪表板页面，不显示主页面的侧边栏
  if (isAuthPage || isDashboardPage) {
    return <div className="max-w-7xl mx-auto px-4 py-6">{children}</div>
  }

  // 主页面布局：已登录用户不显示侧边栏，未登录用户显示侧边栏
  if (session) {
    // 已登录用户：不显示侧边栏，全宽显示内容
    return (
      <div className="max-w-7xl mx-auto px-4 py-6">
        <main className="space-y-8">
          {children}
        </main>
      </div>
    )
  } else {
    // 未登录用户：显示侧边栏
    return (
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="flex gap-6">
          <Sidebar />
          <main className="flex-1 space-y-8">
            {children}
          </main>
        </div>
      </div>
    )
  }
}
