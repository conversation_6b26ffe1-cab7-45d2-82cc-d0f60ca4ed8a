# 环境变量启动显示使用指南

## 🎯 功能概述

在程序启动时自动显示已启用的环境变量，帮助开发者和运维人员快速了解应用的配置状态。

## 🛠️ 实现方案

### 方案一：PM2 启动脚本（推荐生产环境）

**配置文件**: `ecosystem.config.js`

```javascript
module.exports = {
  apps: [{
    name: 'maplestory-info-station',
    script: './scripts/start-with-env-info.js', // 使用自定义启动脚本
    cwd: '/home/<USER>/app',
    // ... 其他配置
  }]
}
```

**启动脚本**: `scripts/start-with-env-info.js`
- 显示详细的环境变量信息
- 按分类组织显示
- 自动隐藏敏感信息
- 显示配置完成度统计

### 方案二：Next.js 集成（推荐开发环境）

**配置文件**: `next.config.mjs`

```javascript
// 导入启动日志
import './lib/startup-logger.js'

const nextConfig = {
  // ... 其他配置
}
```

**启动日志**: `lib/startup-logger.ts`
- TypeScript 类型安全
- 自动在服务器启动时执行
- 彩色输出支持
- 功能状态显示

### 方案三：独立检查脚本

**快速检查**: `scripts/show-env.js`

```bash
# 随时检查环境变量状态
node scripts/show-env.js
```

## 📋 显示内容

### 1. 基本信息
- 启动时间
- 运行环境 (development/production)
- Node.js 版本
- 进程 ID
- 平台信息

### 2. 环境变量分类

#### 🔧 应用配置
- `NODE_ENV` - 运行环境 (必需)
- `PORT` - 应用端口
- `NEXT_PUBLIC_APP_URL` - 应用URL
- `NEXT_PUBLIC_APP_NAME` - 应用名称

#### 🔐 认证配置
- `NEXTAUTH_URL` - NextAuth URL (必需)
- `NEXTAUTH_SECRET` - NextAuth 密钥 (必需，敏感)
- `AUTH_TRUST_HOST` - 信任主机
- `NEXTAUTH_URL_INTERNAL` - 内部URL

#### 🗄️ 数据库配置
- `DATABASE_URL` - 数据库连接 (必需，敏感)
- `REDIS_URL` - Redis连接 (敏感)

#### 📧 邮件配置
- `RESEND_API_KEY` - Resend API密钥 (敏感)
- `EMAIL_FROM` - 发件人邮箱

#### 🔑 第三方服务
- `GOOGLE_CLIENT_ID` - Google客户端ID
- `GOOGLE_CLIENT_SECRET` - Google客户端密钥 (敏感)
- `NEXT_PUBLIC_FINGERPRINT_JS_API_KEY` - 指纹识别API (敏感)

#### 🛡️ 安全配置
- `JWT_SECRET` - JWT密钥 (敏感)
- `ENCRYPTION_KEY` - 加密密钥 (敏感)

#### ⚙️ 功能开关
- `ENABLE_REGISTRATION` - 启用注册
- `ENABLE_EMAIL_VERIFICATION` - 启用邮箱验证
- `ALLOW_EMAIL_LOGIN` - 允许邮箱登录
- `ALLOW_USERNAME_LOGIN` - 允许用户名登录

### 3. 统计信息
- 必需配置完成度
- 缺失的关键环境变量
- 启用的功能列表

## 🔒 安全特性

### 敏感信息处理
- 自动识别敏感环境变量 (包含 SECRET, PASSWORD, KEY, TOKEN 等)
- 短值显示为 `***已设置***`
- 长值显示为 `前4位...后4位` 格式

### 示例输出
```
🔐 认证配置:
   NEXTAUTH_URL: ✅ https://mxd.hyhuman.xyz
   NEXTAUTH_SECRET: ✅ abcd...xyz9
   AUTH_TRUST_HOST: ✅ true
```

## 🚀 使用方法

### 开发环境

```bash
# 直接启动 (会自动显示环境变量)
npm run dev

# 或单独检查
node scripts/show-env.js
```

### 生产环境

```bash
# 使用 PM2 启动 (推荐)
pm2 start ecosystem.config.js

# 或直接使用启动脚本
node scripts/start-with-env-info.js

# 或传统方式
npm start
```

### 手动检查

```bash
# 快速检查当前环境变量状态
node scripts/show-env.js

# 详细启动信息 (不启动应用)
node -e "require('./lib/startup-logger.ts').logStartupInfo()"
```

## 📊 输出示例

```
🚀 MapleStory 信息站启动中...

📋 启动信息:
   时间: 2025/6/24 09:05:47
   环境: production
   进程: 12345
   Node.js: v18.17.0
   平台: linux x64

🔧 环境变量配置:

📂 应用:
   NODE_ENV *: ✅ production - 运行环境
   PORT: ✅ 3000 - 应用端口
   NEXT_PUBLIC_APP_URL: ✅ https://mxd.hyhuman.xyz - 应用URL

📂 认证:
   NEXTAUTH_URL *: ✅ https://mxd.hyhuman.xyz - NextAuth URL
   NEXTAUTH_SECRET *: ✅ abcd...xyz9 - NextAuth 密钥
   AUTH_TRUST_HOST: ✅ true - 信任主机

📂 数据库:
   DATABASE_URL *: ✅ post...5432 - 数据库连接
   REDIS_URL: ✅ redi...6379 - Redis连接

📊 配置统计:
   必需配置: 4/4
   完成度: 100%

🌐 网络配置:
   本地端口: 3000
   外部URL: https://mxd.hyhuman.xyz

🎮 启用的功能:
   ✅ 用户注册
   ✅ 邮箱验证
   ✅ 邮箱登录
   ✅ 用户名登录

✅ 环境配置检查完成，应用启动中...
```

## ⚙️ 自定义配置

### 添加新的环境变量

在 `lib/startup-logger.ts` 中的 `ENV_CONFIGS` 数组添加：

```typescript
{
  key: 'YOUR_NEW_ENV',
  category: '自定义',
  required: false,
  sensitive: true, // 如果是敏感信息
  description: '您的环境变量描述'
}
```

### 修改显示格式

在 `scripts/start-with-env-info.js` 中修改 `displayEnvironmentInfo` 函数。

### 禁用彩色输出

设置环境变量：
```bash
export ENABLE_COLOR_LOGS=false
```

## 🔧 故障排除

### 问题 1: 脚本权限错误

```bash
chmod +x scripts/start-with-env-info.js
chmod +x scripts/show-env.js
```

### 问题 2: 模块导入错误

确保在项目根目录运行脚本：
```bash
cd /path/to/your/project
node scripts/show-env.js
```

### 问题 3: PM2 启动失败

检查 `ecosystem.config.js` 中的路径是否正确：
```javascript
script: './scripts/start-with-env-info.js', // 相对于 cwd 的路径
cwd: '/correct/path/to/your/app',
```

## 📝 最佳实践

1. **生产环境**: 使用 PM2 + 启动脚本
2. **开发环境**: 使用 Next.js 集成
3. **调试时**: 使用独立检查脚本
4. **CI/CD**: 在部署前运行环境检查
5. **监控**: 定期检查环境变量完整性

## 🎯 总结

通过这套环境变量显示系统，您可以：

- ✅ 快速了解应用配置状态
- ✅ 及时发现缺失的环境变量
- ✅ 安全地显示敏感信息
- ✅ 监控功能开关状态
- ✅ 简化故障排除过程

这大大提高了应用的可观测性和运维效率！
