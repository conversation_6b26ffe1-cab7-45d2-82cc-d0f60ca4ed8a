/**
 * 星力强化算法模块
 * 基于真实游戏数据的星力强化概率计算和结果生成
 */

import fs from 'fs'
import path from 'path'

/**
 * CSV数据行结构
 */
export interface StarforceProbabilityRow {
  currentLevel: number
  nextLevel: number
  successRate: number
  failHoldRate: number
  failDropRate: number
  boomRate: number
}

/**
 * 星力强化概率数据
 */
export interface StarforceProbability {
  /** 成功概率 (0-1) */
  success: number
  /** 失败保级概率 (0-1) */
  failHold: number
  /** 失败降级概率 (0-1) */
  failDrop: number
  /** 失败损坏概率 (0-1) */
  boom: number
}

/**
 * 星力强化结果枚举
 */
export enum StarforceResult {
  /** 成功，星级+1 */
  SUCCESS = 'SUCCESS',
  /** 失败保级，星级不变 */
  FAIL_HOLD = 'FAIL_HOLD',
  /** 失败降级，星级-1 */
  FAIL_DROP = 'FAIL_DROP',
  /** 失败损坏，星级重置 */
  BOOM = 'BOOM'
}

/**
 * 星力强化计算结果
 */
export interface StarforceCalculationResult {
  /** 结果类型 */
  result: StarforceResult
  /** 新的星级 */
  newLevel: number
  /** 之前的星级 */
  previousLevel: number
  /** 使用的概率 */
  probabilities: StarforceProbability
  /** 随机数值 */
  randomValue: number
}

/**
 * 模拟结果统计
 */
export interface SimulationResult {
  /** 总尝试次数 */
  totalAttempts: number
  /** 成功次数 */
  successCount: number
  /** 失败保级次数 */
  failHoldCount: number
  /** 失败降级次数 */
  failDropCount: number
  /** 失败损坏次数 */
  boomCount: number
  /** 平均尝试次数 */
  averageAttempts: number
  /** 成功率 */
  successRate: number
  /** 最终达到目标的次数 */
  reachedTargetCount: number
}

/**
 * 星力强化选项
 */
export interface StarforceOptions {
  /** 是否启用抓星星小游戏 */
  starcatchEnabled: boolean
  /** 是否启用防止破坏 */
  preventEnabled: boolean
}

/**
 * 星力概率数据缓存
 */
let probabilityCache: Map<number, StarforceProbability> | null = null

/**
 * 读取并解析CSV文件中的星力强化概率数据
 */
export function loadStarforceProbabilities(): Map<number, StarforceProbability> {
  // 如果已缓存，直接返回
  if (probabilityCache) {
    return probabilityCache
  }

  try {
    const csvPath = path.join(process.cwd(), 'docs', 'starforcePB.csv')
    const csvContent = fs.readFileSync(csvPath, 'utf-8')
    
    const lines = csvContent.trim().split('\n')
    const headers = lines[0].split(',')
    
    // 验证CSV格式
    const expectedHeaders = ['当前级', '下一级', '成功率', '失败保级', '失败降级', '失败损坏']
    if (!expectedHeaders.every((header, index) => headers[index]?.trim() === header)) {
      throw new Error(`CSV格式不正确。期望的列: ${expectedHeaders.join(', ')}`)
    }

    const probabilityMap = new Map<number, StarforceProbability>()

    // 解析数据行
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim()
      if (!line) continue // 跳过空行

      const values = line.split(',').map(v => v.trim())
      if (values.length !== 6) {
        console.warn(`跳过格式不正确的行 ${i + 1}: ${line}`)
        continue
      }

      const currentLevel = parseInt(values[0])
      const nextLevel = parseInt(values[1])
      const successRate = parseFloat(values[2])
      const failHoldRate = parseFloat(values[3])
      const failDropRate = parseFloat(values[4])
      const boomRate = parseFloat(values[5])

      // 验证数据有效性
      if (isNaN(currentLevel) || isNaN(nextLevel) || 
          isNaN(successRate) || isNaN(failHoldRate) || 
          isNaN(failDropRate) || isNaN(boomRate)) {
        console.warn(`跳过包含无效数值的行 ${i + 1}: ${line}`)
        continue
      }

      // 验证概率总和
      const totalProbability = successRate + failHoldRate + failDropRate + boomRate
      if (Math.abs(totalProbability - 1.0) > 0.001) {
        console.warn(`警告：第 ${i + 1} 行概率总和不等于1.0: ${totalProbability}`)
      }

      // 验证星级连续性
      if (nextLevel !== currentLevel + 1) {
        console.warn(`警告：第 ${i + 1} 行星级不连续: ${currentLevel} -> ${nextLevel}`)
      }

      probabilityMap.set(currentLevel, {
        success: successRate,
        failHold: failHoldRate,
        failDrop: failDropRate,
        boom: boomRate
      })
    }

    console.log(`✅ 成功加载 ${probabilityMap.size} 个星级的概率数据`)
    
    // 缓存结果
    probabilityCache = probabilityMap
    return probabilityMap

  } catch (error) {
    console.error('❌ 加载星力概率数据失败:', error)
    throw new Error(`无法加载星力概率数据: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

/**
 * 获取指定星级的基础概率
 */
export function getBaseProbability(level: number): StarforceProbability {
  const probabilityMap = loadStarforceProbabilities()
  const probability = probabilityMap.get(level)
  
  if (!probability) {
    throw new Error(`未找到星级 ${level} 的概率数据`)
  }
  
  return { ...probability } // 返回副本避免修改原数据
}

/**
 * 应用抓星星效果调整概率
 * 抓星星机制：成功率提升5%，计算方式为 当前成功率 × 1.05
 * 其余失败概率按比例重新分配以确保总和为1
 */
export function applyStarcatchBonus(baseProbability: StarforceProbability): StarforceProbability {
  const originalSuccess = baseProbability.success
  const newSuccess = Math.min(1.0, originalSuccess * 1.05) // 成功率不能超过100%
  
  const successIncrease = newSuccess - originalSuccess
  const remainingProbability = 1.0 - newSuccess
  const originalRemaining = 1.0 - originalSuccess
  
  // 如果原始成功率已经是100%，直接返回
  if (originalRemaining <= 0) {
    return {
      success: 1.0,
      failHold: 0,
      failDrop: 0,
      boom: 0
    }
  }
  
  // 按比例缩减其他概率
  const scaleFactor = remainingProbability / originalRemaining
  
  return {
    success: newSuccess,
    failHold: baseProbability.failHold * scaleFactor,
    failDrop: baseProbability.failDrop * scaleFactor,
    boom: baseProbability.boom * scaleFactor
  }
}

/**
 * 应用防止破坏效果调整概率
 * 防止破坏：失败损坏概率转移到失败保级
 */
export function applyPreventDestruction(baseProbability: StarforceProbability): StarforceProbability {
  return {
    success: baseProbability.success,
    failHold: baseProbability.failHold + baseProbability.boom, // 损坏概率转为保级
    failDrop: baseProbability.failDrop,
    boom: 0 // 损坏概率清零
  }
}

/**
 * 计算最终概率（应用所有效果）
 */
export function calculateFinalProbability(
  level: number, 
  options: StarforceOptions
): StarforceProbability {
  let probability = getBaseProbability(level)
  
  // 应用抓星星效果
  if (options.starcatchEnabled) {
    probability = applyStarcatchBonus(probability)
  }
  
  // 应用防止破坏效果
  if (options.preventEnabled) {
    probability = applyPreventDestruction(probability)
  }
  
  // 确保概率总和为1（处理浮点数精度问题）
  const total = probability.success + probability.failHold + probability.failDrop + probability.boom
  if (Math.abs(total - 1.0) > 0.001) {
    console.warn(`概率总和异常: ${total}，进行归一化处理`)
    const factor = 1.0 / total
    probability.success *= factor
    probability.failHold *= factor
    probability.failDrop *= factor
    probability.boom *= factor
  }
  
  return probability
}

/**
 * 计算星力强化结果
 */
export function calculateStarforceResult(
  currentLevel: number,
  starcatchEnabled: boolean = false,
  preventEnabled: boolean = false
): StarforceCalculationResult {
  // 验证输入参数
  if (currentLevel < 0 || currentLevel > 24) {
    throw new Error(`无效的星级: ${currentLevel}。星级必须在0-24之间`)
  }

  const options: StarforceOptions = { starcatchEnabled, preventEnabled }
  const probabilities = calculateFinalProbability(currentLevel, options)

  // 生成随机数
  const randomValue = Math.random()

  // 根据概率区间确定结果
  let result: StarforceResult
  let newLevel: number

  if (randomValue < probabilities.success) {
    // 成功
    result = StarforceResult.SUCCESS
    newLevel = currentLevel + 1
  } else if (randomValue < probabilities.success + probabilities.failHold) {
    // 失败保级
    result = StarforceResult.FAIL_HOLD
    newLevel = currentLevel
  } else if (randomValue < probabilities.success + probabilities.failHold + probabilities.failDrop) {
    // 失败降级
    result = StarforceResult.FAIL_DROP
    newLevel = Math.max(0, currentLevel - 1)
  } else {
    // 失败损坏
    result = StarforceResult.BOOM
    // 损坏规则：根据当前星级确定重置等级
    if (currentLevel >= 15) {
      newLevel = 12 // 15星以上损坏重置到12星
    } else if (currentLevel >= 10) {
      newLevel = currentLevel - 1 // 10-14星损坏降1级
    } else {
      newLevel = 0 // 10星以下损坏重置到0星
    }
  }

  return {
    result,
    newLevel,
    previousLevel: currentLevel,
    probabilities,
    randomValue
  }
}

/**
 * 模拟星力强化过程
 */
export function simulateStarforceEnhancement(
  startLevel: number,
  targetLevel: number,
  attempts: number,
  options: StarforceOptions
): SimulationResult {
  if (startLevel < 0 || startLevel > 25 || targetLevel < 0 || targetLevel > 25) {
    throw new Error(`无效的星级范围: ${startLevel} -> ${targetLevel}`)
  }

  if (startLevel >= targetLevel) {
    throw new Error(`起始星级 ${startLevel} 必须小于目标星级 ${targetLevel}`)
  }

  if (attempts <= 0) {
    throw new Error(`模拟次数必须大于0: ${attempts}`)
  }

  let totalAttempts = 0
  let successCount = 0
  let failHoldCount = 0
  let failDropCount = 0
  let boomCount = 0
  let reachedTargetCount = 0

  for (let simulation = 0; simulation < attempts; simulation++) {
    let currentLevel = startLevel
    let attemptCount = 0
    const maxAttempts = 10000 // 防止无限循环

    while (currentLevel < targetLevel && attemptCount < maxAttempts) {
      const result = calculateStarforceResult(
        currentLevel,
        options.starcatchEnabled,
        options.preventEnabled
      )

      attemptCount++
      totalAttempts++

      switch (result.result) {
        case StarforceResult.SUCCESS:
          successCount++
          currentLevel = result.newLevel
          break
        case StarforceResult.FAIL_HOLD:
          failHoldCount++
          break
        case StarforceResult.FAIL_DROP:
          failDropCount++
          currentLevel = result.newLevel
          break
        case StarforceResult.BOOM:
          boomCount++
          currentLevel = result.newLevel
          break
      }
    }

    if (currentLevel >= targetLevel) {
      reachedTargetCount++
    }
  }

  return {
    totalAttempts,
    successCount,
    failHoldCount,
    failDropCount,
    boomCount,
    averageAttempts: totalAttempts / attempts,
    successRate: (successCount / totalAttempts) * 100,
    reachedTargetCount
  }
}

/**
 * 获取星力强化的详细信息（用于调试和展示）
 */
export function getStarforceInfo(level: number, options: StarforceOptions): {
  level: number
  baseProbability: StarforceProbability
  finalProbability: StarforceProbability
  options: StarforceOptions
  effects: string[]
} {
  const baseProbability = getBaseProbability(level)
  const finalProbability = calculateFinalProbability(level, options)

  const effects: string[] = []
  if (options.starcatchEnabled) {
    effects.push('抓星星小游戏 (+5%成功率)')
  }
  if (options.preventEnabled) {
    effects.push('防止破坏 (损坏→保级)')
  }
  if (effects.length === 0) {
    effects.push('无特殊效果')
  }

  return {
    level,
    baseProbability,
    finalProbability,
    options,
    effects
  }
}

/**
 * 验证概率数据的完整性
 */
export function validateProbabilityData(): {
  isValid: boolean
  errors: string[]
  warnings: string[]
} {
  const errors: string[] = []
  const warnings: string[] = []

  try {
    const probabilityMap = loadStarforceProbabilities()

    // 检查是否有0-24星的完整数据
    for (let level = 0; level <= 24; level++) {
      if (!probabilityMap.has(level)) {
        errors.push(`缺少星级 ${level} 的概率数据`)
      } else {
        const prob = probabilityMap.get(level)!

        // 检查概率值范围
        if (prob.success < 0 || prob.success > 1) {
          errors.push(`星级 ${level} 成功率超出范围: ${prob.success}`)
        }
        if (prob.failHold < 0 || prob.failHold > 1) {
          errors.push(`星级 ${level} 失败保级率超出范围: ${prob.failHold}`)
        }
        if (prob.failDrop < 0 || prob.failDrop > 1) {
          errors.push(`星级 ${level} 失败降级率超出范围: ${prob.failDrop}`)
        }
        if (prob.boom < 0 || prob.boom > 1) {
          errors.push(`星级 ${level} 损坏率超出范围: ${prob.boom}`)
        }

        // 检查概率总和
        const total = prob.success + prob.failHold + prob.failDrop + prob.boom
        if (Math.abs(total - 1.0) > 0.001) {
          warnings.push(`星级 ${level} 概率总和不等于1.0: ${total}`)
        }
      }
    }

  } catch (error) {
    errors.push(`加载概率数据失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}
