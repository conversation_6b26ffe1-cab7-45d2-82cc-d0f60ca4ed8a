/**
 * 星力强化算法模块
 * 基于真实游戏数据的星力强化概率计算和结果生成
 * 客户端和服务端通用版本
 */

/**
 * 星力强化概率数据
 */
export interface StarforceProbability {
  /** 成功概率 (0-1) */
  success: number
  /** 失败保级概率 (0-1) */
  failHold: number
  /** 失败降级概率 (0-1) */
  failDrop: number
  /** 失败损坏概率 (0-1) */
  boom: number
}

/**
 * 星力强化结果枚举
 */
export enum StarforceResult {
  /** 成功，星级+1 */
  SUCCESS = 'SUCCESS',
  /** 失败保级，星级不变 */
  FAIL_HOLD = 'FAIL_HOLD',
  /** 失败降级，星级-1 */
  FAIL_DROP = 'FAIL_DROP',
  /** 失败损坏，星级重置 */
  BOOM = 'BOOM'
}

/**
 * 星力强化计算结果
 */
export interface StarforceCalculationResult {
  /** 结果类型 */
  result: StarforceResult
  /** 新的星级 */
  newLevel: number
  /** 之前的星级 */
  previousLevel: number
  /** 使用的概率 */
  probabilities: StarforceProbability
  /** 随机数值 */
  randomValue: number
}

/**
 * 模拟结果统计
 */
export interface SimulationResult {
  /** 总尝试次数 */
  totalAttempts: number
  /** 成功次数 */
  successCount: number
  /** 失败保级次数 */
  failHoldCount: number
  /** 失败降级次数 */
  failDropCount: number
  /** 失败损坏次数 */
  boomCount: number
  /** 平均尝试次数 */
  averageAttempts: number
  /** 成功率 */
  successRate: number
  /** 最终达到目标的次数 */
  reachedTargetCount: number
}

/**
 * 星力强化选项
 */
export interface StarforceOptions {
  /** 是否启用抓星星小游戏 */
  starcatchEnabled: boolean
  /** 是否启用防止破坏 */
  preventEnabled: boolean
}

/**
 * 获取指定星级的基础概率
 * 现在从传入的概率数据中获取，而不是直接读取CSV
 */
export function getBaseProbability(level: number, probabilityData: Record<number, StarforceProbability>): StarforceProbability {
  const probability = probabilityData[level]

  if (!probability) {
    throw new Error(`未找到星级 ${level} 的概率数据`)
  }

  return { ...probability } // 返回副本避免修改原数据
}

/**
 * 应用抓星星效果调整概率
 * 抓星星机制：成功率提升5%，计算方式为 当前成功率 × 1.05
 * 其余失败概率按比例重新分配以确保总和为1
 */
export function applyStarcatchBonus(baseProbability: StarforceProbability): StarforceProbability {
  const originalSuccess = baseProbability.success
  const newSuccess = Math.min(1.0, originalSuccess * 1.05) // 成功率不能超过100%
  
  const successIncrease = newSuccess - originalSuccess
  const remainingProbability = 1.0 - newSuccess
  const originalRemaining = 1.0 - originalSuccess
  
  // 如果原始成功率已经是100%，直接返回
  if (originalRemaining <= 0) {
    return {
      success: 1.0,
      failHold: 0,
      failDrop: 0,
      boom: 0
    }
  }
  
  // 按比例缩减其他概率
  const scaleFactor = remainingProbability / originalRemaining
  
  return {
    success: newSuccess,
    failHold: baseProbability.failHold * scaleFactor,
    failDrop: baseProbability.failDrop * scaleFactor,
    boom: baseProbability.boom * scaleFactor
  }
}

/**
 * 应用防止破坏效果调整概率
 * 防止破坏：失败损坏概率转移到失败保级
 */
export function applyPreventDestruction(baseProbability: StarforceProbability): StarforceProbability {
  return {
    success: baseProbability.success,
    failHold: baseProbability.failHold + baseProbability.boom, // 损坏概率转为保级
    failDrop: baseProbability.failDrop,
    boom: 0 // 损坏概率清零
  }
}

/**
 * 计算最终概率（应用所有效果）
 */
export function calculateFinalProbability(
  level: number,
  options: StarforceOptions,
  probabilityData: Record<number, StarforceProbability>
): StarforceProbability {
  let probability = getBaseProbability(level, probabilityData)

  // 应用抓星星效果
  if (options.starcatchEnabled) {
    probability = applyStarcatchBonus(probability)
  }

  // 应用防止破坏效果
  if (options.preventEnabled) {
    probability = applyPreventDestruction(probability)
  }

  // 确保概率总和为1（处理浮点数精度问题）
  const total = probability.success + probability.failHold + probability.failDrop + probability.boom
  if (Math.abs(total - 1.0) > 0.001) {
    console.warn(`概率总和异常: ${total}，进行归一化处理`)
    const factor = 1.0 / total
    probability.success *= factor
    probability.failHold *= factor
    probability.failDrop *= factor
    probability.boom *= factor
  }

  return probability
}

/**
 * 计算星力强化结果
 */
export function calculateStarforceResult(
  currentLevel: number,
  starcatchEnabled: boolean = false,
  preventEnabled: boolean = false,
  probabilityData: Record<number, StarforceProbability>
): StarforceCalculationResult {
  // 验证输入参数
  if (currentLevel < 0 || currentLevel > 24) {
    throw new Error(`无效的星级: ${currentLevel}。星级必须在0-24之间`)
  }

  const options: StarforceOptions = { starcatchEnabled, preventEnabled }
  const probabilities = calculateFinalProbability(currentLevel, options, probabilityData)

  // 生成随机数
  const randomValue = Math.random()

  // 根据概率区间确定结果
  let result: StarforceResult
  let newLevel: number

  if (randomValue < probabilities.success) {
    // 成功
    result = StarforceResult.SUCCESS
    newLevel = currentLevel + 1
  } else if (randomValue < probabilities.success + probabilities.failHold) {
    // 失败保级
    result = StarforceResult.FAIL_HOLD
    newLevel = currentLevel
  } else if (randomValue < probabilities.success + probabilities.failHold + probabilities.failDrop) {
    // 失败降级
    result = StarforceResult.FAIL_DROP
    newLevel = Math.max(0, currentLevel - 1)
  } else {
    // 失败损坏
    result = StarforceResult.BOOM
    // 损坏规则：根据当前星级确定重置等级
    if (currentLevel >= 15) {
      newLevel = 12 // 15星以上损坏重置到12星
    } else if (currentLevel >= 10) {
      newLevel = currentLevel - 1 // 10-14星损坏降1级
    } else {
      newLevel = 0 // 10星以下损坏重置到0星
    }
  }

  return {
    result,
    newLevel,
    previousLevel: currentLevel,
    probabilities,
    randomValue
  }
}

/**
 * 获取星力强化的详细信息（用于调试和展示）
 */
export function getStarforceInfo(
  level: number,
  options: StarforceOptions,
  probabilityData: Record<number, StarforceProbability>
): {
  level: number
  baseProbability: StarforceProbability
  finalProbability: StarforceProbability
  options: StarforceOptions
  effects: string[]
} {
  const baseProbability = getBaseProbability(level, probabilityData)
  const finalProbability = calculateFinalProbability(level, options, probabilityData)

  const effects: string[] = []
  if (options.starcatchEnabled) {
    effects.push('抓星星小游戏 (+5%成功率)')
  }
  if (options.preventEnabled) {
    effects.push('防止破坏 (损坏→保级)')
  }
  if (effects.length === 0) {
    effects.push('无特殊效果')
  }

  return {
    level,
    baseProbability,
    finalProbability,
    options,
    effects
  }
}
