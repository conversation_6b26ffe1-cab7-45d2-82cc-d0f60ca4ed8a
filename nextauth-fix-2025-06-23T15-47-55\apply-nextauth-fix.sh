#!/bin/bash

# NextAuth 反向代理修复脚本
echo "🔧 应用 NextAuth 反向代理修复..."
echo ""

# 检查环境
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 备份现有文件
echo "📁 备份现有文件..."
BACKUP_DIR="backup-nextauth-$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

[ -f "lib/auth.ts" ] && cp "lib/auth.ts" "$BACKUP_DIR/"
[ -f ".env.local" ] && cp ".env.local" "$BACKUP_DIR/"

echo "✅ 备份完成: $BACKUP_DIR"

# 停止应用
echo "🛑 停止应用..."
pkill -f "next start" || true
sleep 2

# 更新文件
echo "📋 更新配置文件..."
cp "lib/auth.ts" "lib/auth.ts"

# 处理环境变量
if [ -f ".env.local" ]; then
    echo "⚠️  .env.local 已存在，请手动合并配置"
    echo "参考模板: .env.local.template"
else
    echo "📝 创建 .env.local 文件..."
    cp ".env.local.template" ".env.local"
    echo "⚠️  请编辑 .env.local 文件，配置正确的数据库连接等信息"
fi

echo "✅ 文件更新完成"

# 重启应用
echo "🚀 重启应用..."
npm start &

echo ""
echo "🎉 NextAuth 修复完成!"
echo ""
echo "📋 验证步骤:"
echo "1. 访问 https://mxd.hyhuman.xyz/login"
echo "2. 尝试登录"
echo "3. 测试登出功能"
echo ""
echo "如有问题:"
echo "- 检查 Nginx 配置"
echo "- 查看应用日志"
echo "- 使用备份恢复: cp $BACKUP_DIR/* ./"
