import type React from "react"
import type { Metada<PERSON> } from "next"
import { Poppins } from "next/font/google"
import "./globals.css"
import { UnifiedHeader } from "@/components/layout/UnifiedHeader"
import { MainLayout } from "@/components/layout/MainLayout"
import { SessionProvider } from "@/components/providers/SessionProvider"
import { auth } from "@/lib/auth"
import "./startup-init"

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-poppins"
})

export const metadata: Metadata = {
  title: "冒险岛情报站 - MapleStory Information Hub",
  description: "专业的冒险岛游戏数据库，提供装备、怪物、技能、地图等全面信息",
    generator: 'v0.dev'
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const session = await auth()

  return (
    <html lang="zh-CN">
      <body className={poppins.className}>
        <SessionProvider session={session}>
          <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
            <UnifiedHeader />
            <MainLayout>
              {children}
            </MainLayout>
          </div>
        </SessionProvider>
      </body>
    </html>
  )
}
