-- 添加用户名支持的数据库迁移
-- 这个文件用于手动执行数据库迁移，或者作为 Prisma 迁移的参考

-- 1. 添加 username 字段到 users 表
ALTER TABLE "users" ADD COLUMN "username" TEXT;

-- 2. 添加 lastLogoutAt 字段到 users 表
ALTER TABLE "users" ADD COLUMN "lastLogoutAt" TIMESTAMP(3);

-- 3. 为 username 字段创建唯一索引
CREATE UNIQUE INDEX "users_username_key" ON "users"("username");

-- 4. 为现有用户生成唯一用户名（可选，用于数据迁移）
-- 注意：这个步骤需要根据实际情况调整
-- UPDATE "users" SET "username" = CONCAT('user_', "id") WHERE "username" IS NULL;

-- 5. 验证迁移结果
-- SELECT COUNT(*) FROM "users" WHERE "username" IS NOT NULL;

-- 迁移说明：
-- - username 字段允许为 NULL，这样现有用户不会受到影响
-- - 新注册的用户可以选择设置用户名
-- - 如果需要为所有用户强制设置用户名，可以取消注释第4步的 UPDATE 语句
-- - lastLogoutAt 字段用于记录用户最后登出时间，有助于会话管理和安全审计
