/**
 * 认证配置测试
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals'
import { 
  getAuthConfig, 
  isAuthProviderEnabled, 
  getEnabledAuthProviders,
  validateAuthConfig,
  getLoginFieldConfig 
} from '../lib/auth-config'

// 保存原始环境变量
const originalEnv = process.env

describe('Auth Config', () => {
  beforeEach(() => {
    // 重置环境变量
    process.env = { ...originalEnv }
  })

  afterEach(() => {
    // 恢复原始环境变量
    process.env = originalEnv
  })

  describe('getAuthConfig', () => {
    it('should return default config when no env vars are set', () => {
      delete process.env.ENABLED_AUTH_PROVIDERS
      delete process.env.ALLOW_EMAIL_LOGIN
      delete process.env.ALLOW_USERNAME_LOGIN
      delete process.env.GOOGLE_CLIENT_ID
      delete process.env.GOOGLE_CLIENT_SECRET

      const config = getAuthConfig()

      expect(config.enabledProviders).toEqual(['credentials', 'email'])
      expect(config.allowEmailLogin).toBe(true)
      expect(config.allowUsernameLogin).toBe(false)
      expect(config.googleOAuth.enabled).toBe(false)
    })

    it('should parse ENABLED_AUTH_PROVIDERS from env', () => {
      process.env.ENABLED_AUTH_PROVIDERS = 'credentials,google,username'

      const config = getAuthConfig()

      expect(config.enabledProviders).toEqual(['credentials', 'google', 'username'])
    })

    it('should enable Google OAuth when credentials are provided', () => {
      process.env.GOOGLE_CLIENT_ID = 'test-client-id'
      process.env.GOOGLE_CLIENT_SECRET = 'test-client-secret'

      const config = getAuthConfig()

      expect(config.googleOAuth.enabled).toBe(true)
      expect(config.googleOAuth.clientId).toBe('test-client-id')
      expect(config.googleOAuth.clientSecret).toBe('test-client-secret')
    })

    it('should auto-add google to enabled providers when OAuth is configured', () => {
      process.env.ENABLED_AUTH_PROVIDERS = 'credentials'
      process.env.GOOGLE_CLIENT_ID = 'test-client-id'
      process.env.GOOGLE_CLIENT_SECRET = 'test-client-secret'

      const config = getAuthConfig()

      expect(config.enabledProviders).toContain('google')
    })

    it('should auto-add username to enabled providers when username login is enabled', () => {
      process.env.ENABLED_AUTH_PROVIDERS = 'credentials'
      process.env.ALLOW_USERNAME_LOGIN = 'true'

      const config = getAuthConfig()

      expect(config.enabledProviders).toContain('username')
    })
  })

  describe('isAuthProviderEnabled', () => {
    it('should return true for enabled providers', () => {
      process.env.ENABLED_AUTH_PROVIDERS = 'credentials,google'

      expect(isAuthProviderEnabled('credentials')).toBe(true)
      expect(isAuthProviderEnabled('google')).toBe(true)
      expect(isAuthProviderEnabled('username')).toBe(false)
    })
  })

  describe('getEnabledAuthProviders', () => {
    it('should return list of enabled providers', () => {
      process.env.ENABLED_AUTH_PROVIDERS = 'credentials,username'

      const providers = getEnabledAuthProviders()

      expect(providers).toEqual(['credentials', 'username'])
    })
  })

  describe('validateAuthConfig', () => {
    it('should return valid for default config', () => {
      const validation = validateAuthConfig()

      expect(validation.valid).toBe(true)
      expect(validation.errors).toHaveLength(0)
    })

    it('should return invalid when no providers are enabled', () => {
      process.env.ENABLED_AUTH_PROVIDERS = ''

      const validation = validateAuthConfig()

      expect(validation.valid).toBe(false)
      expect(validation.errors).toContain('至少需要启用一种认证方式')
    })

    it('should return invalid when Google OAuth is enabled but missing credentials', () => {
      process.env.ENABLED_AUTH_PROVIDERS = 'google'
      delete process.env.GOOGLE_CLIENT_ID
      delete process.env.GOOGLE_CLIENT_SECRET

      const validation = validateAuthConfig()

      expect(validation.valid).toBe(false)
      expect(validation.errors).toContain('启用 Google OAuth 需要设置 GOOGLE_CLIENT_ID 环境变量')
      expect(validation.errors).toContain('启用 Google OAuth 需要设置 GOOGLE_CLIENT_SECRET 环境变量')
    })

    it('should return invalid when email/username login is enabled but credentials is not', () => {
      process.env.ENABLED_AUTH_PROVIDERS = 'google'
      process.env.ALLOW_EMAIL_LOGIN = 'true'

      const validation = validateAuthConfig()

      expect(validation.valid).toBe(false)
      expect(validation.errors).toContain('启用邮箱或用户名登录需要同时启用 credentials 认证方式')
    })
  })

  describe('getLoginFieldConfig', () => {
    it('should return email config when only email login is enabled', () => {
      process.env.ALLOW_EMAIL_LOGIN = 'true'
      process.env.ALLOW_USERNAME_LOGIN = 'false'

      const config = getLoginFieldConfig()

      expect(config.showEmailField).toBe(true)
      expect(config.showUsernameField).toBe(false)
      expect(config.loginFieldLabel).toBe('邮箱地址')
      expect(config.emailPlaceholder).toBe('邮箱地址')
    })

    it('should return username config when only username login is enabled', () => {
      process.env.ALLOW_EMAIL_LOGIN = 'false'
      process.env.ALLOW_USERNAME_LOGIN = 'true'

      const config = getLoginFieldConfig()

      expect(config.showEmailField).toBe(false)
      expect(config.showUsernameField).toBe(true)
      expect(config.loginFieldLabel).toBe('用户名')
      expect(config.emailPlaceholder).toBe('用户名')
    })

    it('should return combined config when both email and username login are enabled', () => {
      process.env.ALLOW_EMAIL_LOGIN = 'true'
      process.env.ALLOW_USERNAME_LOGIN = 'true'

      const config = getLoginFieldConfig()

      expect(config.showEmailField).toBe(true)
      expect(config.showUsernameField).toBe(true)
      expect(config.loginFieldLabel).toBe('邮箱/用户名')
      expect(config.emailPlaceholder).toBe('邮箱地址或用户名')
    })

    it('should show Google button when Google OAuth is enabled', () => {
      process.env.GOOGLE_CLIENT_ID = 'test-client-id'
      process.env.GOOGLE_CLIENT_SECRET = 'test-client-secret'

      const config = getLoginFieldConfig()

      expect(config.showGoogleButton).toBe(true)
    })
  })
})
