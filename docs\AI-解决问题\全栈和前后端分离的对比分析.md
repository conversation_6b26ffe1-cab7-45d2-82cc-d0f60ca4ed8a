# 全栈和前后端分离的对比分析

## 📋 项目背景

基于冒险岛情报站项目（Next.js 14 + TypeScript + SSG配置），分析添加复杂后端API功能的技术方案，重点关注用户认证和权限管理系统的实现。

## 🎯 核心需求分析

### 功能要求
1. **用户认证系统**
   - 用户注册、登录、密码重置
   - JWT token 管理和刷新
   - 会话管理和安全控制

2. **权限管理系统**
   - 多级用户权限（普通用户、VIP用户、管理员）
   - 基于角色的访问控制（RBAC）
   - 细粒度权限控制

3. **安全性要求**
   - 密码加密存储
   - 防暴力破解机制
   - CSRF 和 XSS 防护
   - API 安全认证

## 🏗️ 架构方案对比

### 方案A：全栈 Next.js 架构

```
┌─────────────────────────────────────┐
│           Next.js 14 应用            │
├─────────────────────────────────────┤
│  前端页面 (SSG/SSR)                  │
│  ├── app/                           │
│  ├── components/                    │
│  └── public/                       │
├─────────────────────────────────────┤
│  API Routes (后端逻辑)               │
│  ├── app/api/auth/                  │
│  ├── app/api/users/                 │
│  └── app/api/admin/                 │
├─────────────────────────────────────┤
│  数据库层                            │
│  ├── PostgreSQL                    │
│  └── Redis (缓存)                   │
└─────────────────────────────────────┘
```

### 方案B：前后端分离架构 (FastAPI)

```
┌─────────────────┐    ┌─────────────────┐
│   Next.js 前端   │    │  FastAPI 后端   │
│                │    │                │
│  ├── app/       │    │  ├── app/       │
│  ├── components/│◄──►│  ├── models/    │
│  └── public/    │    │  ├── routes/    │
│                │    │  └── services/  │
└─────────────────┘    └─────────────────┘
                              │
                       ┌─────────────────┐
                       │   数据库层       │
                       │  ├── PostgreSQL │
                       │  └── Redis      │
                       └─────────────────┘
```

## 1. 🔧 后端技术栈选择分析

### FastAPI (Python) 技术评估

#### 用户认证系统实现复杂度

**优势分析**：
- ✅ **FastAPI-Users**: 开箱即用的完整用户管理系统
- ✅ **Passlib**: 强大的密码哈希和验证库
- ✅ **Python-Jose**: 成熟的 JWT 处理库
- ✅ **OAuth2 支持**: 原生支持 OAuth2 和 OpenID Connect

**实现评估**：
```python
# 认证系统复杂度示例
from fastapi_users import FastAPIUsers
from fastapi_users.authentication import JWTAuthentication

# 配置简单，功能完整
jwt_authentication = JWTAuthentication(
    secret=SECRET,
    lifetime_seconds=3600,
    tokenUrl="auth/jwt/login",
)

fastapi_users = FastAPIUsers(
    user_manager,
    [jwt_authentication],
)
```

**复杂度评分**: 🟢 低 (8/10)
- 代码量: ~300-500 行
- 开发时间: 2-3 天
- 学习曲线: 平缓

#### 权限管理和角色控制灵活性

**RBAC 实现能力**：
```python
# 权限系统设计示例
from enum import Enum
from fastapi import Depends, HTTPException

class UserRole(str, Enum):
    GUEST = "guest"
    VIP = "vip"
    ADMIN = "admin"

class Permission(str, Enum):
    READ_BASIC = "read:basic"
    READ_ADVANCED = "read:advanced"
    WRITE_DATA = "write:data"
    ADMIN_ACCESS = "admin:access"

# 装饰器权限检查
def require_permission(permission: Permission):
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 权限验证逻辑
            return await func(*args, **kwargs)
        return wrapper
    return decorator
```

**灵活性评分**: 🟢 优秀 (9/10)
- 支持复杂权限模型
- 装饰器模式简洁
- 动态权限分配
- 细粒度控制

#### 与 Next.js 前端集成难度

**集成挑战**：
- 🔴 **跨语言通信**: 需要 API 契约管理
- 🔴 **类型同步**: TypeScript 类型需要手动维护
- 🔴 **开发环境**: 需要双语言开发环境
- 🟡 **调试复杂度**: 跨服务调试

**集成方案**：
```typescript
// 前端 API 客户端
interface User {
  id: string
  email: string
  role: 'guest' | 'vip' | 'admin'
}

// 需要手动维护类型一致性
const authApi = {
  login: async (credentials: LoginCredentials): Promise<User> => {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials)
    })
    return response.json()
  }
}
```

**集成难度**: 🔴 高
- 需要 API 文档生成工具
- 类型定义同步机制
- 跨域配置管理

#### 开发和维护成本

**开发成本分析**：
```
初期开发成本: 🔴 高
├── 技能要求: Python + TypeScript
├── 环境配置: 双语言开发环境
├── 工具链: 两套构建和部署流程
└── 学习成本: FastAPI + Next.js 双重学习

长期维护成本: 🔴 高
├── 依赖管理: Python 和 Node.js 双重依赖
├── 安全更新: 两套技术栈的安全补丁
├── 性能优化: 跨服务性能调优
└── 团队协作: 需要全栈或专业分工
```

**成本评估**：
- 开发团队规模: 2-3 人（全栈）或 4-5 人（分工）
- 年度维护工作量: 50-80 人天
- 技术债务风险: 中等

#### 性能和扩展性考虑

**性能优势**：
```python
# FastAPI 性能特点
性能指标:
├── 并发处理: 异步支持，高并发能力
├── 响应速度: 接近 NodeJS 和 Go 的性能
├── 内存使用: 相对较低的内存占用
└── CPU 效率: Python 3.7+ 异步优化

扩展性:
├── 水平扩展: 支持多实例部署
├── 微服务: 易于拆分为微服务架构
├── 负载均衡: 无状态设计，易于负载均衡
└── 缓存集成: Redis 集成简单
```

**性能评分**: 🟢 优秀 (8/10)

## 2. 🏛️ 项目架构设计方案

### 在当前项目添加 backend/ 目录的可行性

#### 目录结构设计

```
maplestory-info-station/
├── 📁 frontend/              # Next.js 前端 (重命名现有内容)
│   ├── app/
│   ├── components/
│   ├── lib/
│   ├── types/
│   ├── package.json
│   └── next.config.mjs
├── 📁 backend/               # FastAPI 后端 (新增)
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py          # FastAPI 应用入口
│   │   ├── core/            # 核心配置
│   │   │   ├── config.py
│   │   │   ├── security.py
│   │   │   └── database.py
│   │   ├── models/          # 数据模型
│   │   │   ├── user.py
│   │   │   ├── role.py
│   │   │   └── permission.py
│   │   ├── schemas/         # Pydantic 模式
│   │   │   ├── user.py
│   │   │   └── auth.py
│   │   ├── api/             # API 路由
│   │   │   ├── v1/
│   │   │   │   ├── auth.py
│   │   │   │   ├── users.py
│   │   │   │   └── admin.py
│   │   │   └── deps.py      # 依赖注入
│   │   ├── services/        # 业务逻辑
│   │   │   ├── auth_service.py
│   │   │   └── user_service.py
│   │   └── utils/           # 工具函数
│   │       ├── email.py
│   │       └── validators.py
│   ├── requirements.txt
│   ├── alembic/             # 数据库迁移
│   └── tests/               # 测试文件
├── 📁 shared/                # 共享资源 (新增)
│   ├── types/               # 共享类型定义
│   ├── constants/           # 共享常量
│   └── schemas/             # API Schema 定义
├── 📁 docs/                 # 文档目录 (现有)
├── 📁 scripts/              # 构建脚本 (现有)
│   ├── build-frontend.sh
│   ├── build-backend.sh
│   └── deploy.sh
├── docker-compose.yml       # 开发环境 (新增)
├── docker-compose.prod.yml  # 生产环境 (新增)
├── .github/workflows/       # CI/CD (调整)
│   ├── frontend.yml
│   └── backend.yml
└── README.md               # 更新文档
```

#### 可行性评估

**✅ 优势**：
- 代码组织清晰，职责分离明确
- 独立的依赖管理，避免冲突
- 支持独立部署和扩展
- 便于团队分工协作

**⚠️ 挑战**：
- 项目复杂度显著增加
- 需要重新组织现有代码结构
- 开发环境配置复杂化
- 构建和部署流程需要重新设计

**可行性结论**: 🟡 中等 - 需要谨慎规划和分阶段实施

### 前后端分离架构详细设计

#### 通信架构

```mermaid
graph TB
    A[用户浏览器] --> B[Next.js 前端 SSG]
    B --> C[API Gateway/Proxy]
    C --> D[FastAPI 后端]
    D --> E[PostgreSQL 数据库]
    D --> F[Redis 缓存]
    
    subgraph "认证流程"
        G[JWT Token] --> H[权限验证]
        H --> I[资源访问]
    end
    
    subgraph "部署环境"
        J[Vercel/Netlify] --> B
        K[云服务器/容器] --> D
        L[云数据库] --> E
    end
```

#### API 设计规范

**RESTful API 设计**：
```python
# API 路由设计示例
from fastapi import APIRouter

router = APIRouter(prefix="/api/v1", tags=["auth"])

@router.post("/auth/register")
async def register_user(user_data: UserCreate):
    """用户注册"""
    pass

@router.post("/auth/login")
async def login(credentials: UserLogin):
    """用户登录"""
    pass

@router.post("/auth/refresh")
async def refresh_token(refresh_token: str):
    """刷新访问令牌"""
    pass

@router.post("/auth/logout")
async def logout(current_user: User = Depends(get_current_user)):
    """用户登出"""
    pass

@router.post("/auth/reset-password")
async def reset_password(email: str):
    """密码重置"""
    pass
```

**API 版本管理策略**：
```
版本控制方案:
├── URL 版本控制: /api/v1/, /api/v2/
├── 向后兼容: 保持旧版本 API 可用
├── 废弃策略: 逐步废弃旧版本
└── 文档管理: 自动生成 API 文档
```

### 数据库选择建议

#### 主数据库：PostgreSQL

**选择理由**：
```sql
-- 用户认证相关表结构设计
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role_name VARCHAR(50) NOT NULL,
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    granted_by UUID REFERENCES users(id)
);

CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    resource VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL
);

CREATE TABLE role_permissions (
    role_name VARCHAR(50),
    permission_id UUID REFERENCES permissions(id),
    PRIMARY KEY (role_name, permission_id)
);
```

**优势**：
- ✅ ACID 事务支持，数据一致性强
- ✅ 复杂查询能力，支持 JSON 字段
- ✅ 丰富的数据类型和扩展
- ✅ 成熟的 ORM 支持（SQLAlchemy）

#### 缓存层：Redis

**使用场景**：
```python
# Redis 使用示例
import redis
from typing import Optional

class CacheService:
    def __init__(self):
        self.redis = redis.Redis(host='localhost', port=6379, db=0)
    
    async def cache_user_session(self, user_id: str, session_data: dict):
        """缓存用户会话"""
        key = f"session:{user_id}"
        self.redis.setex(key, 3600, json.dumps(session_data))
    
    async def cache_user_permissions(self, user_id: str, permissions: list):
        """缓存用户权限"""
        key = f"permissions:{user_id}"
        self.redis.setex(key, 1800, json.dumps(permissions))
    
    async def get_cached_data(self, key: str) -> Optional[dict]:
        """获取缓存数据"""
        data = self.redis.get(key)
        return json.loads(data) if data else None
```

#### 游戏数据存储策略

**混合存储方案**：
```
数据分层存储:
├── 静态游戏数据 (继续使用文件系统)
│   ├── 装备数据: JSON/JS 文件
│   ├── 技能数据: 静态文件
│   └── 构建时预处理
├── 动态用户数据 (PostgreSQL)
│   ├── 用户配置
│   ├── 收藏记录
│   └── 使用历史
└── 缓存数据 (Redis)
    ├── 热点查询结果
    ├── 会话数据
    └── 临时计算结果
```

### 跨域处理和安全性考虑

#### CORS 配置

```python
# FastAPI CORS 配置
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # 开发环境
        "https://your-domain.com",  # 生产环境
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)
```

#### 安全性措施

**多层安全防护**：
```python
# 安全中间件配置
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.middleware.httpsredirect import HTTPSRedirectMiddleware

# HTTPS 重定向
app.add_middleware(HTTPSRedirectMiddleware)

# 可信主机
app.add_middleware(
    TrustedHostMiddleware, 
    allowed_hosts=["your-domain.com", "*.your-domain.com"]
)

# 速率限制
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

@app.post("/auth/login")
@limiter.limit("5/minute")
async def login(request: Request, credentials: UserLogin):
    """登录接口 - 限制每分钟5次尝试"""
    pass
```

## 3. 🔐 用户认证系统设计

### JWT 认证方案设计

#### Token 策略

**双 Token 机制**：
```python
# JWT 配置
from datetime import datetime, timedelta
from jose import JWTError, jwt

class JWTManager:
    def __init__(self):
        self.secret_key = "your-secret-key"
        self.algorithm = "HS256"
        self.access_token_expire = timedelta(minutes=15)
        self.refresh_token_expire = timedelta(days=7)
    
    def create_access_token(self, data: dict) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + self.access_token_expire
        to_encode.update({"exp": expire, "type": "access"})
        return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
    
    def create_refresh_token(self, data: dict) -> str:
        """创建刷新令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + self.refresh_token_expire
        to_encode.update({"exp": expire, "type": "refresh"})
        return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
```

**Token 安全策略**：
- 🔒 Access Token: 短期有效（15分钟）
- 🔄 Refresh Token: 长期有效（7天）
- 🚫 Token 黑名单机制
- 🔐 Token 轮换策略

### 用户注册、登录、密码重置流程设计

#### 用户注册流程

```python
# 用户注册实现
from fastapi import HTTPException, status
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

@router.post("/auth/register")
async def register_user(user_data: UserCreate, db: Session = Depends(get_db)):
    """用户注册流程"""

    # 1. 验证用户输入
    if await get_user_by_email(db, user_data.email):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已被注册"
        )

    # 2. 密码强度验证
    if not validate_password_strength(user_data.password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="密码强度不足"
        )

    # 3. 创建用户
    hashed_password = pwd_context.hash(user_data.password)
    user = User(
        email=user_data.email,
        hashed_password=hashed_password,
        is_active=False,  # 需要邮箱验证
        is_verified=False
    )

    # 4. 发送验证邮件
    verification_token = create_verification_token(user.email)
    await send_verification_email(user.email, verification_token)

    # 5. 保存用户
    db.add(user)
    db.commit()

    return {"message": "注册成功，请检查邮箱进行验证"}
```

#### 登录流程设计

```python
@router.post("/auth/login")
@limiter.limit("5/minute")
async def login(
    request: Request,
    credentials: UserLogin,
    db: Session = Depends(get_db)
):
    """用户登录流程"""

    # 1. 用户验证
    user = await authenticate_user(db, credentials.email, credentials.password)
    if not user:
        # 记录失败尝试
        await log_failed_login_attempt(credentials.email, request.client.host)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="邮箱或密码错误"
        )

    # 2. 账户状态检查
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="账户已被禁用"
        )

    if not user.is_verified:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="请先验证邮箱"
        )

    # 3. 生成 Token
    access_token = create_access_token({"sub": str(user.id)})
    refresh_token = create_refresh_token({"sub": str(user.id)})

    # 4. 更新登录信息
    user.last_login = datetime.utcnow()
    db.commit()

    # 5. 缓存用户权限
    permissions = await get_user_permissions(db, user.id)
    await cache_user_permissions(str(user.id), permissions)

    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "user": UserResponse.from_orm(user)
    }
```

#### 密码重置流程

```python
@router.post("/auth/reset-password")
async def reset_password(email: str, db: Session = Depends(get_db)):
    """密码重置流程"""

    # 1. 验证用户存在
    user = await get_user_by_email(db, email)
    if not user:
        # 安全考虑：不暴露用户是否存在
        return {"message": "如果邮箱存在，重置链接已发送"}

    # 2. 生成重置令牌
    reset_token = create_password_reset_token(email)

    # 3. 存储重置令牌（设置过期时间）
    await store_reset_token(email, reset_token, expire_minutes=30)

    # 4. 发送重置邮件
    await send_password_reset_email(email, reset_token)

    return {"message": "如果邮箱存在，重置链接已发送"}

@router.post("/auth/confirm-reset")
async def confirm_password_reset(
    reset_data: PasswordReset,
    db: Session = Depends(get_db)
):
    """确认密码重置"""

    # 1. 验证重置令牌
    email = await verify_reset_token(reset_data.token)
    if not email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="重置令牌无效或已过期"
        )

    # 2. 更新密码
    user = await get_user_by_email(db, email)
    user.hashed_password = pwd_context.hash(reset_data.new_password)

    # 3. 清除重置令牌
    await clear_reset_token(email)

    # 4. 撤销所有现有 Token
    await revoke_all_user_tokens(str(user.id))

    db.commit()

    return {"message": "密码重置成功"}
```

### 多级权限系统设计

#### 权限模型设计

```python
# 权限系统数据模型
from enum import Enum
from sqlalchemy import Column, String, Boolean, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

class UserRole(str, Enum):
    """用户角色枚举"""
    GUEST = "guest"          # 游客用户
    REGISTERED = "registered" # 注册用户
    VIP = "vip"             # VIP用户
    ADMIN = "admin"         # 管理员
    SUPER_ADMIN = "super_admin"  # 超级管理员

class Permission(str, Enum):
    """权限枚举"""
    # 基础权限
    READ_PUBLIC = "read:public"
    READ_BASIC = "read:basic"

    # 高级功能权限
    USE_SIMULATOR = "use:simulator"
    EXPORT_DATA = "export:data"
    SAVE_CONFIG = "save:config"

    # VIP 权限
    USE_ADVANCED_FEATURES = "use:advanced_features"
    UNLIMITED_ACCESS = "unlimited:access"
    PRIORITY_SUPPORT = "priority:support"

    # 管理权限
    MANAGE_USERS = "manage:users"
    MANAGE_CONTENT = "manage:content"
    VIEW_ANALYTICS = "view:analytics"
    SYSTEM_CONFIG = "system:config"

class User(Base):
    __tablename__ = "users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), unique=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)

    # 关联角色
    roles = relationship("UserRoleAssignment", back_populates="user")

class Role(Base):
    __tablename__ = "roles"

    name = Column(String(50), primary_key=True)
    description = Column(String(255))
    is_active = Column(Boolean, default=True)

    # 关联权限
    permissions = relationship("RolePermission", back_populates="role")

class UserRoleAssignment(Base):
    __tablename__ = "user_role_assignments"

    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), primary_key=True)
    role_name = Column(String(50), ForeignKey("roles.name"), primary_key=True)
    granted_at = Column(DateTime, default=datetime.utcnow)
    granted_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    expires_at = Column(DateTime, nullable=True)  # VIP 过期时间

    user = relationship("User", back_populates="roles")
    role = relationship("Role")
```

#### 权限检查装饰器

```python
# 权限检查实现
from functools import wraps
from fastapi import Depends, HTTPException, status

def require_permission(permission: Permission):
    """权限检查装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从依赖注入获取当前用户
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="未认证用户"
                )

            # 检查用户权限
            user_permissions = await get_user_permissions(current_user.id)
            if permission not in user_permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="权限不足"
                )

            return await func(*args, **kwargs)
        return wrapper
    return decorator

# 使用示例
@router.get("/admin/users")
@require_permission(Permission.MANAGE_USERS)
async def get_all_users(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取所有用户 - 需要管理用户权限"""
    return await get_users_list(db)
```

#### 角色权限配置

```python
# 默认角色权限配置
ROLE_PERMISSIONS = {
    UserRole.GUEST: [
        Permission.READ_PUBLIC,
    ],

    UserRole.REGISTERED: [
        Permission.READ_PUBLIC,
        Permission.READ_BASIC,
        Permission.USE_SIMULATOR,
        Permission.SAVE_CONFIG,
    ],

    UserRole.VIP: [
        Permission.READ_PUBLIC,
        Permission.READ_BASIC,
        Permission.USE_SIMULATOR,
        Permission.SAVE_CONFIG,
        Permission.EXPORT_DATA,
        Permission.USE_ADVANCED_FEATURES,
        Permission.UNLIMITED_ACCESS,
        Permission.PRIORITY_SUPPORT,
    ],

    UserRole.ADMIN: [
        # 包含所有 VIP 权限
        *ROLE_PERMISSIONS[UserRole.VIP],
        Permission.MANAGE_USERS,
        Permission.MANAGE_CONTENT,
        Permission.VIEW_ANALYTICS,
    ],

    UserRole.SUPER_ADMIN: [
        # 包含所有权限
        *[p for p in Permission],
    ]
}

async def initialize_roles_and_permissions(db: Session):
    """初始化角色和权限"""
    for role_name, permissions in ROLE_PERMISSIONS.items():
        # 创建角色
        role = Role(name=role_name.value, description=f"{role_name.value} 角色")
        db.merge(role)

        # 分配权限
        for permission in permissions:
            role_permission = RolePermission(
                role_name=role_name.value,
                permission_name=permission.value
            )
            db.merge(role_permission)

    db.commit()
```

### 第三方登录集成可能性

#### OAuth2 集成架构

```python
# 第三方登录配置
from authlib.integrations.fastapi_oauth2 import OAuth2AuthorizeCallback

class OAuth2Config:
    """OAuth2 配置"""

    # QQ 登录配置
    QQ_CLIENT_ID = "your_qq_client_id"
    QQ_CLIENT_SECRET = "your_qq_client_secret"
    QQ_REDIRECT_URI = "https://your-domain.com/auth/qq/callback"

    # 微信登录配置
    WECHAT_APP_ID = "your_wechat_app_id"
    WECHAT_APP_SECRET = "your_wechat_app_secret"
    WECHAT_REDIRECT_URI = "https://your-domain.com/auth/wechat/callback"

@router.get("/auth/qq/login")
async def qq_login():
    """QQ 登录重定向"""
    qq_auth_url = (
        f"https://graph.qq.com/oauth2.0/authorize"
        f"?client_id={OAuth2Config.QQ_CLIENT_ID}"
        f"&redirect_uri={OAuth2Config.QQ_REDIRECT_URI}"
        f"&response_type=code"
        f"&scope=get_user_info"
    )
    return RedirectResponse(qq_auth_url)

@router.get("/auth/qq/callback")
async def qq_callback(code: str, db: Session = Depends(get_db)):
    """QQ 登录回调处理"""

    # 1. 获取访问令牌
    token_response = await get_qq_access_token(code)
    access_token = token_response["access_token"]

    # 2. 获取用户信息
    user_info = await get_qq_user_info(access_token)

    # 3. 查找或创建用户
    user = await get_user_by_qq_openid(db, user_info["openid"])
    if not user:
        user = await create_user_from_qq(db, user_info)

    # 4. 生成 JWT Token
    access_token = create_access_token({"sub": str(user.id)})
    refresh_token = create_refresh_token({"sub": str(user.id)})

    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "user": UserResponse.from_orm(user)
    }
```

### 安全性措施

#### 密码安全策略

```python
# 密码安全实现
import re
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class PasswordValidator:
    """密码验证器"""

    @staticmethod
    def validate_strength(password: str) -> tuple[bool, list[str]]:
        """验证密码强度"""
        errors = []

        if len(password) < 8:
            errors.append("密码长度至少8位")

        if not re.search(r"[A-Z]", password):
            errors.append("密码必须包含大写字母")

        if not re.search(r"[a-z]", password):
            errors.append("密码必须包含小写字母")

        if not re.search(r"\d", password):
            errors.append("密码必须包含数字")

        if not re.search(r"[!@#$%^&*(),.?\":{}|<>]", password):
            errors.append("密码必须包含特殊字符")

        return len(errors) == 0, errors

    @staticmethod
    def hash_password(password: str) -> str:
        """密码哈希"""
        return pwd_context.hash(password)

    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(plain_password, hashed_password)
```

#### 防暴力破解机制

```python
# 防暴力破解实现
from datetime import datetime, timedelta
import redis

class BruteForceProtection:
    """暴力破解防护"""

    def __init__(self):
        self.redis = redis.Redis(host='localhost', port=6379, db=1)
        self.max_attempts = 5
        self.lockout_duration = timedelta(minutes=15)

    async def check_login_attempts(self, identifier: str) -> bool:
        """检查登录尝试次数"""
        key = f"login_attempts:{identifier}"
        attempts = self.redis.get(key)

        if attempts and int(attempts) >= self.max_attempts:
            return False  # 账户被锁定

        return True

    async def record_failed_attempt(self, identifier: str):
        """记录失败尝试"""
        key = f"login_attempts:{identifier}"
        current_attempts = self.redis.get(key) or 0
        new_attempts = int(current_attempts) + 1

        # 设置过期时间
        self.redis.setex(key, int(self.lockout_duration.total_seconds()), new_attempts)

        # 记录日志
        await self.log_security_event(
            event_type="failed_login",
            identifier=identifier,
            attempts=new_attempts
        )

    async def clear_attempts(self, identifier: str):
        """清除失败尝试记录"""
        key = f"login_attempts:{identifier}"
        self.redis.delete(key)
```

#### CSRF 和 XSS 防护

```python
# 安全中间件
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from starlette.middleware.sessions import SessionMiddleware

# CSRF 防护
class CSRFProtection:
    """CSRF 防护中间件"""

    def __init__(self):
        self.secret_key = "your-csrf-secret"

    def generate_csrf_token(self, session_id: str) -> str:
        """生成 CSRF 令牌"""
        timestamp = str(int(datetime.utcnow().timestamp()))
        message = f"{session_id}:{timestamp}"
        signature = hmac.new(
            self.secret_key.encode(),
            message.encode(),
            hashlib.sha256
        ).hexdigest()
        return f"{timestamp}:{signature}"

    def verify_csrf_token(self, token: str, session_id: str) -> bool:
        """验证 CSRF 令牌"""
        try:
            timestamp, signature = token.split(":", 1)
            message = f"{session_id}:{timestamp}"
            expected_signature = hmac.new(
                self.secret_key.encode(),
                message.encode(),
                hashlib.sha256
            ).hexdigest()

            # 验证签名和时间戳
            return (
                hmac.compare_digest(signature, expected_signature) and
                datetime.utcnow().timestamp() - int(timestamp) < 3600  # 1小时有效
            )
        except ValueError:
            return False

# XSS 防护
from markupsafe import escape

def sanitize_input(data: str) -> str:
    """输入数据清理"""
    return escape(data)

# 响应头安全配置
@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    response = await call_next(request)

    # 安全响应头
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    response.headers["Content-Security-Policy"] = (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline'; "
        "style-src 'self' 'unsafe-inline'; "
        "img-src 'self' data: https:;"
    )

    return response
```

## 📊 架构方案总结对比

### 全栈 Next.js vs 前后端分离 (FastAPI)

| 评估维度 | 全栈 Next.js | 前后端分离 (FastAPI) | 推荐 |
|---------|-------------|-------------------|------|
| **开发复杂度** | 🟢 简单 | 🔴 复杂 | Next.js |
| **技术栈一致性** | 🟢 统一 | 🔴 分离 | Next.js |
| **性能扩展性** | 🟡 良好 | 🟢 优秀 | FastAPI |
| **功能完整性** | 🟡 受限 | 🟢 完整 | FastAPI |
| **部署运维** | 🟢 简单 | 🔴 复杂 | Next.js |
| **开发成本** | 🟢 低 | 🔴 高 | Next.js |
| **维护成本** | 🟢 低 | 🔴 高 | Next.js |
| **团队技能要求** | 🟢 单一 | 🔴 多样 | Next.js |

### 最终建议

#### 推荐方案：阶段性架构演进

**第一阶段（当前）**：保持 Next.js SSG + 简单 API Routes
- 实现基础用户认证
- 简单的权限控制
- 最小化架构变更

**第二阶段（3-6个月后）**：评估业务需求
- 如果用户量和功能复杂度增长
- 考虑迁移到 FastAPI 后端
- 渐进式架构升级

**第三阶段（长期）**：微服务架构
- 根据业务发展需要
- 拆分为多个专业服务
- 完整的 DevOps 体系

#### 决策建议

**选择全栈 Next.js 如果**：
- ✅ 团队规模较小（1-3人）
- ✅ 功能相对简单
- ✅ 快速上线需求
- ✅ 维护成本敏感

**选择前后端分离 (FastAPI) 如果**：
- ✅ 团队有 Python 技能
- ✅ 复杂业务逻辑需求
- ✅ 高性能和扩展性要求
- ✅ 长期项目规划

对于冒险岛情报站项目，建议先从全栈 Next.js 开始，根据实际发展需要再考虑架构升级。
