#!/bin/bash

# 冒险岛情报站 - Rocky Linux 9.5 自动化部署脚本
# 专注于应用部署，假设基础服务（PostgreSQL、Redis）已配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
APP_NAME="maplestory-info-station"
APP_USER="maplestory"
APP_DIR="/home/<USER>/app"
BACKUP_DIR="/home/<USER>/backups"
LOG_DIR="/home/<USER>/logs"
NODE_VERSION="18"

# 参数解析
SKIP_BUILD=false
SKIP_DB=false
ROLLBACK=false
ENVIRONMENT="production"

for arg in "$@"; do
    case $arg in
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --skip-db)
            SKIP_DB=true
            shift
            ;;
        --rollback)
            ROLLBACK=true
            shift
            ;;
        --env=*)
            ENVIRONMENT="${arg#*=}"
            shift
            ;;
    esac
done

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
    exit 1
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO:${NC} $1"
}

# 检查是否为 root 用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "请不要使用 root 用户运行此脚本"
    fi
}

# 检查系统版本
check_system() {
    if ! grep -q "Rocky Linux release 9" /etc/redhat-release 2>/dev/null; then
        warning "此脚本专为 Rocky Linux 9.5 设计，当前系统可能不兼容"
    fi
    log "系统检查完成"
}

# 检查依赖
check_dependencies() {
    log "检查系统依赖..."
    
    # 检查 Node.js
    if ! command -v node >/dev/null 2>&1; then
        error "Node.js 未安装，请先安装 Node.js $NODE_VERSION"
    fi
    
    local node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt "$NODE_VERSION" ]; then
        error "Node.js 版本过低，需要 $NODE_VERSION 或更高版本"
    fi
    
    # 检查 npm
    if ! command -v npm >/dev/null 2>&1; then
        error "npm 未安装"
    fi
    
    # 检查 PM2
    if ! command -v pm2 >/dev/null 2>&1; then
        error "PM2 未安装，请运行: sudo npm install -g pm2"
    fi
    
    # 检查 Git
    if ! command -v git >/dev/null 2>&1; then
        error "Git 未安装，请运行: sudo dnf install -y git"
    fi
    
    log "依赖检查完成"
}

# 创建必要目录
create_directories() {
    log "创建必要目录..."
    
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$LOG_DIR"
    
    log "目录创建完成"
}

# 创建备份
create_backup() {
    log "创建应用备份..."
    
    local backup_name="backup_$(date +%Y%m%d_%H%M%S)"
    
    if [ -d "$APP_DIR" ]; then
        tar -czf "$BACKUP_DIR/${backup_name}.tar.gz" -C "$APP_DIR" . || error "备份失败"
        log "备份创建成功: ${backup_name}.tar.gz"
        
        # 清理旧备份（保留最近10个）
        ls -t "$BACKUP_DIR"/backup_*.tar.gz 2>/dev/null | tail -n +11 | xargs rm -f
    else
        info "应用目录不存在，跳过备份"
    fi
}

# 回滚功能
rollback() {
    log "开始回滚..."
    
    local latest_backup=$(ls -t "$BACKUP_DIR"/backup_*.tar.gz 2>/dev/null | head -n1)
    
    if [ -z "$latest_backup" ]; then
        error "未找到备份文件"
    fi
    
    log "使用备份文件: $latest_backup"
    
    # 停止应用
    if pm2 list | grep -q "$APP_NAME"; then
        pm2 stop "$APP_NAME" || true
    fi
    
    # 恢复文件
    rm -rf "$APP_DIR"
    mkdir -p "$APP_DIR"
    tar -xzf "$latest_backup" -C "$APP_DIR" || error "恢复失败"
    
    # 重启应用
    pm2 start "$APP_NAME" || error "应用启动失败"
    
    log "回滚完成"
    exit 0
}

# 部署代码
deploy_code() {
    log "部署应用代码..."
    
    if [ ! -d "$APP_DIR" ]; then
        error "应用目录不存在: $APP_DIR"
    fi
    
    cd "$APP_DIR" || error "无法进入应用目录"
    
    # 拉取最新代码
    git fetch origin || error "拉取代码失败"
    git reset --hard origin/main || error "重置代码失败"
    
    # 显示当前版本
    local commit_hash=$(git rev-parse --short HEAD)
    local commit_msg=$(git log -1 --pretty=format:"%s")
    info "当前版本: $commit_hash - $commit_msg"
    
    log "代码部署完成"
}

# 安装依赖
install_dependencies() {
    log "安装应用依赖..."
    
    cd "$APP_DIR" || error "无法进入应用目录"
    
    # 清理 node_modules 和 package-lock.json
    rm -rf node_modules package-lock.json
    
    # 安装生产依赖
    npm ci --only=production || error "依赖安装失败"
    
    log "依赖安装完成"
}

# 构建应用
build_app() {
    if [ "$SKIP_BUILD" = true ]; then
        info "跳过构建步骤"
        return
    fi
    
    log "构建应用..."
    
    cd "$APP_DIR" || error "无法进入应用目录"
    
    # 清理之前的构建
    rm -rf .next
    
    # 构建应用
    npm run build || error "构建失败"
    
    # 验证构建结果
    if [ ! -d ".next" ]; then
        error "构建产物不存在"
    fi
    
    log "应用构建完成"
}

# 数据库迁移
migrate_database() {
    if [ "$SKIP_DB" = true ]; then
        info "跳过数据库迁移"
        return
    fi
    
    log "执行数据库迁移..."
    
    cd "$APP_DIR" || error "无法进入应用目录"
    
    # 生成 Prisma 客户端
    npx prisma generate || error "Prisma 客户端生成失败"
    
    # 推送数据库模式
    npx prisma db push || error "数据库迁移失败"
    
    log "数据库迁移完成"
}

# 配置 PM2
setup_pm2() {
    log "配置 PM2..."
    
    cd "$APP_DIR" || error "无法进入应用目录"
    
    # 检查 PM2 配置文件
    if [ ! -f "ecosystem.config.js" ]; then
        log "创建 PM2 配置文件..."
        cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: '$APP_NAME',
    script: 'npm',
    args: 'start',
    cwd: '$APP_DIR',
    instances: 'max',
    exec_mode: 'cluster',
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    env_file: '.env.production',
    log_file: '$LOG_DIR/app.log',
    out_file: '$LOG_DIR/out.log',
    error_file: '$LOG_DIR/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',
    min_uptime: '10s',
    max_restarts: 10,
    autorestart: true,
    watch: false,
    kill_timeout: 5000,
    listen_timeout: 3000,
    time: true
  }]
}
EOF
    fi
    
    log "PM2 配置完成"
}

# 重启应用
restart_app() {
    log "重启应用..."
    
    cd "$APP_DIR" || error "无法进入应用目录"
    
    # 检查应用是否已存在
    if pm2 list | grep -q "$APP_NAME"; then
        pm2 restart "$APP_NAME" || error "应用重启失败"
    else
        pm2 start ecosystem.config.js --env production || error "应用启动失败"
    fi
    
    # 保存 PM2 配置
    pm2 save
    
    # 等待应用启动
    sleep 15
    
    log "应用重启完成"
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:3000/health >/dev/null 2>&1; then
            log "健康检查通过"
            return 0
        fi
        
        info "健康检查失败，重试 $attempt/$max_attempts"
        sleep 2
        ((attempt++))
    done
    
    error "健康检查失败，部署可能有问题"
}

# 显示部署信息
show_deployment_info() {
    log "部署完成！"
    echo ""
    echo "=== 部署信息 ==="
    echo "应用名称: $APP_NAME"
    echo "应用目录: $APP_DIR"
    echo "运行环境: $ENVIRONMENT"
    echo "访问地址: http://localhost:3000"
    echo ""
    echo "=== 管理命令 ==="
    echo "查看状态: pm2 status"
    echo "查看日志: pm2 logs $APP_NAME"
    echo "重启应用: pm2 restart $APP_NAME"
    echo "停止应用: pm2 stop $APP_NAME"
    echo ""
    echo "=== 日志文件 ==="
    echo "应用日志: $LOG_DIR/app.log"
    echo "输出日志: $LOG_DIR/out.log"
    echo "错误日志: $LOG_DIR/error.log"
}

# 主函数
main() {
    log "开始 Rocky Linux 9.5 应用部署"
    
    # 处理回滚
    if [ "$ROLLBACK" = true ]; then
        rollback
    fi
    
    # 检查环境
    check_root
    check_system
    check_dependencies
    
    # 部署流程
    create_directories
    create_backup
    deploy_code
    install_dependencies
    build_app
    migrate_database
    setup_pm2
    restart_app
    health_check
    show_deployment_info
    
    log "部署流程完成！"
}

# 显示帮助信息
show_help() {
    echo "Rocky Linux 9.5 冒险岛情报站部署脚本"
    echo ""
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --skip-build     跳过构建步骤"
    echo "  --skip-db        跳过数据库迁移"
    echo "  --rollback       回滚到上一版本"
    echo "  --env=ENV        指定环境 (默认: production)"
    echo "  --help           显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 完整部署"
    echo "  $0 --skip-build       # 跳过构建"
    echo "  $0 --rollback         # 回滚"
}

# 参数处理
case "$1" in
    --help|-h)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
