#!/usr/bin/env node

/**
 * 创建部署压缩包脚本
 * 将 deploy 目录打包成 zip 文件，方便上传到服务器
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

const sourceDir = process.cwd()
const deployDir = path.join(sourceDir, 'deploy')
const packageName = `maplestory-info-station-${new Date().toISOString().slice(0, 10)}.zip`
const packagePath = path.join(sourceDir, packageName)

function createZipPackage() {
  console.log('📦 创建部署压缩包...')
  
  try {
    // 检查 deploy 目录是否存在
    if (!fs.existsSync(deployDir)) {
      console.error('❌ deploy 目录不存在，请先运行 prepare-deploy.js')
      process.exit(1)
    }

    // 删除现有的压缩包
    if (fs.existsSync(packagePath)) {
      fs.unlinkSync(packagePath)
    }

    // 创建压缩包 (使用 PowerShell 在 Windows 上)
    if (process.platform === 'win32') {
      const command = `powershell -Command "Compress-Archive -Path '${deployDir}\\*' -DestinationPath '${packagePath}' -Force"`
      execSync(command, { stdio: 'inherit' })
    } else {
      // Linux/Mac 使用 zip 命令
      const command = `cd "${deployDir}" && zip -r "${packagePath}" .`
      execSync(command, { stdio: 'inherit' })
    }

    console.log('✅ 压缩包创建成功!')
    console.log(`📁 文件位置: ${packagePath}`)
    
    // 显示文件大小
    const stats = fs.statSync(packagePath)
    const fileSizeInMB = (stats.size / (1024 * 1024)).toFixed(2)
    console.log(`📊 文件大小: ${fileSizeInMB} MB`)
    
  } catch (error) {
    console.error('❌ 创建压缩包失败:', error.message)
    process.exit(1)
  }
}

function showDeployInstructions() {
  console.log('\n🚀 部署说明:')
  console.log('1. 将压缩包上传到服务器')
  console.log('2. 解压缩到目标目录')
  console.log('3. 配置环境变量 (.env.local)')
  console.log('4. 安装依赖: npm install --production')
  console.log('5. 设置数据库: npx prisma migrate deploy')
  console.log('6. 启动应用: npm start 或 ./start.sh')
  console.log('\n📖 详细说明请查看解压后的 README-DEPLOY.md 文件')
}

// 运行脚本
if (require.main === module) {
  createZipPackage()
  showDeployInstructions()
}

module.exports = { createZipPackage }
