import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/db'

const verifyEmailSchema = z.object({
  token: z.string()
})

export async function POST(request: NextRequest) {
  console.log('📧 开始处理邮箱验证请求')

  try {
    const body = await request.json()
    console.log('📧 接收到验证数据:', { tokenPreview: body.token?.substring(0, 8) + '...' })

    const { token } = verifyEmailSchema.parse(body)
    console.log('✅ 验证数据格式正确')

    // 查找验证令牌
    console.log('🔍 查找验证令牌:', token.substring(0, 8) + '...')
    const verificationToken = await prisma.verificationToken.findUnique({
      where: { token }
    })

    if (!verificationToken) {
      console.log('❌ 验证令牌无效或不存在')
      return NextResponse.json(
        { error: '验证令牌无效' },
        { status: 400 }
      )
    }

    console.log('✅ 验证令牌找到:', {
      identifier: verificationToken.identifier,
      expires: verificationToken.expires
    })

    // 检查令牌是否过期
    const now = new Date()
    if (verificationToken.expires < now) {
      console.log('❌ 验证令牌已过期')

      // 计算过期时间
      const expiredHours = Math.floor((now.getTime() - verificationToken.expires.getTime()) / (1000 * 60 * 60))

      // 删除过期令牌
      await prisma.verificationToken.delete({
        where: { token }
      })

      return NextResponse.json(
        {
          error: '验证令牌已过期',
          message: `验证链接已过期${expiredHours > 0 ? ` (过期 ${expiredHours} 小时)` : ''}，请重新发送激活邮件`,
          expired: true,
          email: verificationToken.identifier
        },
        { status: 410 } // 410 Gone 表示资源已过期
      )
    }

    console.log('✅ 验证令牌有效，未过期')

    // 查找用户
    console.log('👤 查找用户:', verificationToken.identifier)
    const user = await prisma.user.findUnique({
      where: { email: verificationToken.identifier }
    })

    if (!user) {
      console.log('❌ 用户不存在:', verificationToken.identifier)
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 400 }
      )
    }

    console.log('✅ 用户找到:', { id: user.id, email: user.email, emailVerified: user.emailVerified })

    // 更新用户邮箱验证状态
    console.log('📧 开始更新用户邮箱验证状态')
    await prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerified: new Date(),
        isActive: true
      }
    })
    console.log('✅ 用户邮箱验证状态更新成功')

    // 删除已使用的验证令牌
    console.log('🗑️ 删除已使用的验证令牌')
    await prisma.verificationToken.delete({
      where: { token }
    })
    console.log('✅ 验证令牌删除成功')

    // 记录验证成功的交易（奖励）
    console.log('🎁 开始发放邮箱验证奖励')
    await prisma.transaction.create({
      data: {
        userId: user.id,
        type: 'REWARD',
        amount: 50, // 邮箱验证奖励50欢乐豆
        balanceBefore: 100,
        balanceAfter: 150,
        description: '邮箱验证奖励',
        status: 'COMPLETED',
        metadata: {
          reason: 'email_verification_reward'
        }
      }
    })
    console.log('✅ 邮箱验证奖励交易记录创建成功')

    // 更新用户余额
    console.log('💰 开始更新用户余额')
    await prisma.currencyBalance.update({
      where: { userId: user.id },
      data: {
        balance: { increment: 50 },
        totalEarned: { increment: 50 }
      }
    })
    console.log('✅ 用户余额更新成功，增加50欢乐豆')

    console.log('🎉 邮箱验证流程完成')
    return NextResponse.json({
      success: true,
      message: '邮箱验证成功'
    })

  } catch (error) {
    console.error('❌ 邮箱验证过程发生错误:', error)

    if (error instanceof z.ZodError) {
      console.error('❌ 请求数据格式错误:', error.errors)
      return NextResponse.json(
        { error: '请求数据格式错误' },
        { status: 400 }
      )
    }

    console.error('❌ 邮箱验证失败，未知错误:', error)
    return NextResponse.json(
      { error: '邮箱验证失败，请稍后重试' },
      { status: 500 }
    )
  }
}
