{"server": {"host": "your-server-ip", "user": "root", "port": 22, "remotePath": "/root/gits/maplestory-info-station", "backupPath": "/root/backups"}, "deployment": {"buildCommand": "pnpm run build", "installCommand": "npm ci --only=production", "startCommand": "pm2 start ecosystem.config.js", "restartCommand": "pm2 restart maplestory-info-station"}, "options": {"createBackup": true, "verifyAfterDeploy": true, "uploadTimeout": 300, "startTimeout": 30}}