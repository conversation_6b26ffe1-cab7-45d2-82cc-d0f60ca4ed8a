#!/bin/bash

# 修复 Prisma 查询引擎错误
echo "🔧 修复 Prisma 查询引擎错误..."
echo ""

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 未找到 package.json 文件"
    echo "请确保在正确的项目目录中运行此脚本"
    exit 1
fi

# 检查 Prisma 目录
if [ ! -d "prisma" ]; then
    echo "❌ 错误: 未找到 prisma 目录"
    exit 1
fi

echo "1. 清理 Prisma 相关文件..."
rm -rf node_modules/.prisma
rm -rf node_modules/@prisma
rm -f prisma/dev.db*

echo "2. 重新安装 Prisma 依赖..."
npm install @prisma/client prisma --save

echo "3. 生成 Prisma 客户端..."
npx prisma generate --force

if [ $? -ne 0 ]; then
    echo "❌ Prisma 客户端生成失败"
    echo "尝试使用详细输出..."
    npx prisma generate --force --verbose
    exit 1
fi

echo "4. 检查数据库连接..."
if [ -f ".env.local" ]; then
    echo "✅ 找到 .env.local 文件"
    
    # 检查 DATABASE_URL
    if grep -q "DATABASE_URL" .env.local; then
        echo "✅ 找到 DATABASE_URL 配置"
    else
        echo "⚠️  警告: 未找到 DATABASE_URL 配置"
        echo "请在 .env.local 中添加数据库连接字符串"
    fi
else
    echo "⚠️  警告: 未找到 .env.local 文件"
    echo "创建示例配置文件..."
    
    cat > .env.local << EOF
# 数据库配置
DATABASE_URL="postgresql://postgres:password@localhost:5432/mxd_info_db"

# NextAuth 配置
NEXTAUTH_URL="https://mxd.hyhuman.xyz"
NEXTAUTH_SECRET="$(openssl rand -base64 32)"

# 应用配置
NODE_ENV="production"
PORT="3000"

# Prisma 配置
PRISMA_QUERY_ENGINE_LIBRARY="/root/svc/mxd/mxd-tool/node_modules/.prisma/client/libquery_engine-rhel-openssl-1.0.x.so.node"
PRISMA_QUERY_ENGINE_BINARY="/root/svc/mxd/mxd-tool/node_modules/.prisma/client/query-engine-rhel-openssl-1.0.x"
EOF
    
    echo "✅ 已创建 .env.local 文件"
fi

echo "5. 验证 Prisma 安装..."
npx prisma --version

echo "6. 测试数据库连接..."
npx prisma db pull --force || echo "⚠️  数据库连接测试失败，请检查 DATABASE_URL"

echo ""
echo "🎉 Prisma 修复完成!"
echo ""
echo "📋 下一步:"
echo "1. 检查 .env.local 中的 DATABASE_URL 配置"
echo "2. 确保数据库服务正在运行"
echo "3. 运行数据库迁移: npx prisma migrate deploy"
echo "4. 重新启动应用: pnpm start"
echo ""
echo "🔍 如果问题仍然存在，请运行:"
echo "RUST_BACKTRACE=1 pnpm start"
