
# 冒险岛情报站装备强化API文档

## 概述

装备强化API提供了完整的装备强化功能，支持星力强化、潜能重设、额外属性强化等功能。API采用RESTful设计，支持JSON格式的请求和响应。

## 基础信息

- **基础URL**: `https://your-domain.com/api/enhancement`
- **API版本**: `v1.0.0`
- **内容类型**: `application/json`
- **认证方式**: API密钥（可选）

## 认证

API支持可选的密钥认证。如需使用认证，请在请求头中包含API密钥：

```http
X-API-Key: your-api-key-here
```

## 端点

### 1. 装备强化

执行装备强化操作。

**端点**: `POST /api/enhancement/enhance`

#### 请求参数

| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| `equipmentId` | string | 是 | 装备唯一标识符 |
| `currentLevel` | number | 是 | 当前强化等级 (0-25) |
| `enhancementType` | string | 是 | 强化类型: `starforce`, `potential`, `bonusstat` |
| `starcatchEnabled` | boolean | 否 | 是否启用解锁抓星星 (默认: false) |
| `preventEnabled` | boolean | 否 | 是否启用防止破坏 (默认: false) |
| `minigameBonus` | number | 否 | 迷你游戏加成 0-20 (默认: 0) |

#### 请求示例

```json
{
  "equipmentId": "weapon_001",
  "currentLevel": 10,
  "enhancementType": "starforce",
  "starcatchEnabled": false,
  "preventEnabled": true,
  "minigameBonus": 5
}
```

#### 响应格式

**成功响应** (HTTP 200):

```json
{
  "success": true,
  "result": {
    "type": "success",
    "message": "强化成功！星力等级提升至 11",
    "newLevel": 11,
    "previousLevel": 10,
    "isSuccess": true
  },
  "cost": {
    "mesos": 1500000,
    "materials": [
      {
        "name": "星之力",
        "quantity": 1
      }
    ]
  },
  "probability": {
    "success": 85,
    "failure": 10,
    "majorFailure": 5,
    "failureDrop": 0
  },
  "requestId": "uuid-string",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

**错误响应** (HTTP 4xx/5xx):

```json
{
  "success": false,
  "error": "装备已达到最大强化等级",
  "errorCode": "MAX_LEVEL_REACHED",
  "details": {
    "currentLevel": 25,
    "maxLevel": 25
  },
  "requestId": "uuid-string",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 2. API信息

获取API基本信息和文档。

**端点**: `GET /api/enhancement/enhance`

#### 响应示例

```json
{
  "name": "Enhancement API",
  "version": "1.0.0",
  "description": "冒险岛装备强化API接口",
  "endpoints": {
    "enhance": {
      "method": "POST",
      "path": "/api/enhancement/enhance",
      "description": "执行装备强化"
    }
  }
}
```

## 强化类型说明

### 星力强化 (starforce)

- **等级范围**: 0-25
- **成功率**: 根据当前等级递减
- **失败效果**: 可能保级、降级或装备损坏
- **特殊选项**: 
  - 解锁抓星星: 提高成功率
  - 防止破坏: 避免装备损坏

### 潜能重设 (potential)

- **成功率**: 100%
- **效果**: 重新随机装备潜能属性
- **费用**: 固定 500,000 金币

### 额外属性强化 (bonusstat)

- **成功率**: 100%
- **效果**: 重新随机额外属性
- **费用**: 固定 300,000 金币

## 错误代码

| 错误代码 | 描述 |
|----------|------|
| `INVALID_EQUIPMENT_ID` | 无效的装备ID |
| `INVALID_LEVEL` | 无效的强化等级 |
| `INVALID_ENHANCEMENT_TYPE` | 无效的强化类型 |
| `MAX_LEVEL_REACHED` | 已达到最大强化等级 |
| `INSUFFICIENT_FUNDS` | 金币不足 |
| `EQUIPMENT_NOT_FOUND` | 装备不存在 |
| `ENHANCEMENT_NOT_SUPPORTED` | 装备不支持此强化类型 |
| `RATE_LIMIT_EXCEEDED` | 请求频率超限 |
| `INVALID_API_KEY` | 无效的API密钥 |
| `INTERNAL_ERROR` | 服务器内部错误 |
| `VALIDATION_ERROR` | 参数验证失败 |

## 速率限制

- **未认证用户**: 每分钟 30 次请求
- **认证用户**: 每分钟 60 次请求
- **突发限制**: 10 次请求

超出限制时将返回 HTTP 429 状态码。

## 使用示例

### JavaScript/TypeScript

```typescript
import { enhanceEquipment } from '@/lib/enhancement-api'

try {
  const result = await enhanceEquipment(
    'weapon_001',
    10,
    'starforce',
    {
      starcatchEnabled: false,
      preventEnabled: true,
      minigameBonus: 5,
      apiKey: 'your-api-key'
    }
  )
  
  console.log('强化结果:', result.result.message)
  console.log('新等级:', result.result.newLevel)
} catch (error) {
  console.error('强化失败:', error.message)
}
```

### Python

```python
from enhancement_api_client import EnhancementApiClient, EnhancementRequest, EnhancementType

client = EnhancementApiClient(
    base_url="https://your-domain.com",
    api_key="your-api-key"
)

request = EnhancementRequest(
    equipment_id="weapon_001",
    current_level=10,
    enhancement_type=EnhancementType.STARFORCE,
    prevent_enabled=True,
    minigame_bonus=5.0
)

try:
    result = client.enhance_equipment(request)
    print(f"强化结果: {result.message}")
    print(f"新等级: {result.new_level}")
except Exception as e:
    print(f"强化失败: {e}")
```

### cURL

```bash
curl -X POST https://your-domain.com/api/enhancement/enhance \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{
    "equipmentId": "weapon_001",
    "currentLevel": 10,
    "enhancementType": "starforce",
    "preventEnabled": true,
    "minigameBonus": 5
  }'
```

## 最佳实践

### 1. 错误处理

始终检查响应的 `success` 字段，并适当处理错误：

```typescript
if (!response.success) {
  switch (response.errorCode) {
    case 'MAX_LEVEL_REACHED':
      console.log('装备已达到最大等级')
      break
    case 'INSUFFICIENT_FUNDS':
      console.log('金币不足')
      break
    default:
      console.log('未知错误:', response.error)
  }
}
```

### 2. 批量操作

对于批量强化，建议添加适当的延迟避免触发速率限制：

```typescript
for (const request of requests) {
  const result = await enhanceEquipment(...)
  await new Promise(resolve => setTimeout(resolve, 100)) // 100ms延迟
}
```

### 3. 重试机制

网络错误时实现指数退避重试：

```typescript
async function enhanceWithRetry(request, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await enhanceEquipment(request)
    } catch (error) {
      if (i === maxRetries - 1) throw error
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000))
    }
  }
}
```

## 更新日志

### v1.0.0 (2024-01-01)

- 初始版本发布
- 支持星力强化、潜能重设、额外属性强化
- 实现API密钥认证
- 添加速率限制
- 提供完整的错误处理

## 支持

如有问题或建议，请联系：

- **邮箱**: <EMAIL>
- **GitHub**: https://github.com/your-repo/issues
- **文档**: https://docs.maplestory-info.com
