// 导入启动日志
// import './lib/startup-logger.js'

/** @type {import('next').NextConfig} */
const nextConfig = {
  // 移除 output: 'export' 以支持 API Routes
  trailingSlash: true,

  // 图片优化配置
  images: {
    domains: ['localhost', '127.0.0.1'],
    formats: ['image/webp', 'image/avif'],
    // 生产环境启用优化，开发环境保持简单
    unoptimized: process.env.NODE_ENV === 'development'
  },

  // 实验性功能
  experimental: {
    serverComponentsExternalPackages: ['@prisma/client'],
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons']
  },

  // 环境变量配置
  env: {
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
  },

  // 重定向配置
  async redirects() {
    return [
      {
        source: '/admin',
        destination: '/admin/dashboard',
        permanent: true,
      },
    ]
  },

  // 头部配置
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET,POST,PUT,DELETE,OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'Content-Type, Authorization' },
        ],
      },
    ]
  },

  // 开发环境配置
  eslint: {
    ignoreDuringBuilds: false, // 启用 ESLint 检查
  },
  typescript: {
    ignoreBuildErrors: false, // 启用 TypeScript 检查
  },

  // 排除增量包目录
  webpack: (config, { isServer }) => {
    // 排除增量更新包目录
    config.watchOptions = {
      ...config.watchOptions,
      ignored: [
        '**/node_modules/**',
        '**/incremental-updates/**',
        '**/nextauth-fix-*/**',
        '**/quick-update-*/**',
        '**/git-update-*/**',
        '**/backup-*/**'
      ]
    }
    return config
  },
}

export default nextConfig
