#!/usr/bin/env node

/**
 * 启动脚本 - 显示环境变量信息
 * 在应用启动前显示所有已启用的环境变量
 */

const { spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// 需要显示的环境变量分类
const envCategories = {
  '🔧 应用配置': [
    'NODE_ENV',
    'PORT',
    'NEXT_PUBLIC_APP_URL',
    'NEXT_PUBLIC_APP_NAME'
  ],
  '🔐 认证配置': [
    'NEXTAUTH_URL',
    'NEXTAUTH_SECRET',
    'AUTH_TRUST_HOST',
    'NEXTAUTH_URL_INTERNAL',
    'ENABLED_AUTH_PROVIDERS'
  ],
  '🗄️ 数据库配置': [
    'DATABASE_URL',
    'REDIS_URL'
  ],
  '📧 邮件配置': [
    'RESEND_API_KEY',
    'EMAIL_FROM',
    'SMTP_HOST',
    'SMTP_PORT',
    'SMTP_USER'
  ],
  '🔑 第三方服务': [
    'GOOGLE_CLIENT_ID',
    'NEXT_PUBLIC_FINGERPRINT_JS_API_KEY',
    'STRIPE_SECRET_KEY'
  ],
  '🛡️ 安全配置': [
    'JWT_SECRET',
    'ENCRYPTION_KEY'
  ],
  '⚙️ 功能开关': [
    'ENABLE_REGISTRATION',
    'ENABLE_EMAIL_VERIFICATION',
    'ENABLE_FINGERPRINT_TRACKING',
    'ALLOW_EMAIL_LOGIN',
    'ALLOW_USERNAME_LOGIN'
  ],
  '🎮 应用特定': [
    'CURRENCY_NAME',
    'DEFAULT_CURRENCY_AMOUNT'
  ]
}

// 敏感信息处理
const sensitiveKeys = [
  'SECRET', 'PASSWORD', 'KEY', 'TOKEN', 'API_KEY', 
  'CLIENT_SECRET', 'PRIVATE', 'CREDENTIAL'
]

function isSensitive(key) {
  return sensitiveKeys.some(sensitive => 
    key.toUpperCase().includes(sensitive)
  )
}

function maskSensitiveValue(key, value) {
  if (!value) return '未设置'
  
  if (isSensitive(key)) {
    if (value.length <= 8) {
      return '***已设置***'
    }
    return `${value.substring(0, 4)}...${value.substring(value.length - 4)}`
  }
  
  return value
}

// 显示环境变量信息
function displayEnvironmentInfo() {
  const startTime = new Date()
  
  console.log('')
  colorLog('cyan', '🚀 MapleStory 信息站启动中...')
  console.log('')
  
  // 显示基本信息
  colorLog('bright', '📋 启动信息:')
  console.log(`   时间: ${startTime.toLocaleString('zh-CN')}`)
  console.log(`   环境: ${process.env.NODE_ENV || 'development'}`)
  console.log(`   进程: ${process.pid}`)
  console.log(`   Node.js: ${process.version}`)
  console.log(`   平台: ${process.platform} ${process.arch}`)
  console.log('')
  
  // 显示环境变量
  colorLog('bright', '🔧 环境变量配置:')
  console.log('')
  
  let totalConfigured = 0
  let totalAvailable = 0
  
  Object.entries(envCategories).forEach(([category, keys]) => {
    colorLog('yellow', category)
    
    keys.forEach(key => {
      totalAvailable++
      const value = process.env[key]
      const displayValue = maskSensitiveValue(key, value)
      
      if (value) {
        totalConfigured++
        if (isSensitive(key)) {
          console.log(`   ${key}: ${colors.green}${displayValue}${colors.reset}`)
        } else {
          console.log(`   ${key}: ${colors.green}${displayValue}${colors.reset}`)
        }
      } else {
        console.log(`   ${key}: ${colors.red}未设置${colors.reset}`)
      }
    })
    console.log('')
  })
  
  // 显示统计信息
  colorLog('bright', '📊 配置统计:')
  console.log(`   已配置: ${colors.green}${totalConfigured}${colors.reset}/${totalAvailable}`)
  console.log(`   完成度: ${colors.cyan}${Math.round(totalConfigured / totalAvailable * 100)}%${colors.reset}`)
  console.log('')
  
  // 检查关键配置
  const criticalEnvs = [
    'NODE_ENV', 'DATABASE_URL', 'NEXTAUTH_URL', 'NEXTAUTH_SECRET'
  ]
  
  const missingCritical = criticalEnvs.filter(key => !process.env[key])
  
  if (missingCritical.length > 0) {
    colorLog('red', '⚠️  警告: 缺少关键环境变量:')
    missingCritical.forEach(key => {
      console.log(`   - ${key}`)
    })
    console.log('')
  }
  
  // 显示环境文件加载信息
  const envFiles = ['.env.local', '.env.production', '.env']
  const loadedEnvFiles = envFiles.filter(file => fs.existsSync(file))
  
  if (loadedEnvFiles.length > 0) {
    colorLog('bright', '📄 环境文件:')
    loadedEnvFiles.forEach(file => {
      const stats = fs.statSync(file)
      console.log(`   ${file} (${stats.size} bytes, ${stats.mtime.toLocaleString('zh-CN')})`)
    })
    console.log('')
  }
  
  // 显示网络配置
  if (process.env.PORT || process.env.NEXTAUTH_URL) {
    colorLog('bright', '🌐 网络配置:')
    if (process.env.PORT) {
      console.log(`   端口: ${colors.cyan}${process.env.PORT}${colors.reset}`)
    }
    if (process.env.NEXTAUTH_URL) {
      console.log(`   外部URL: ${colors.cyan}${process.env.NEXTAUTH_URL}${colors.reset}`)
    }
    console.log('')
  }
  
  colorLog('green', '✅ 环境变量检查完成，启动应用...')
  console.log('')
}

// 启动 Next.js 应用
function startApplication() {
  const child = spawn('npm', ['start'], {
    stdio: 'inherit',
    env: process.env
  })
  
  child.on('error', (error) => {
    colorLog('red', `❌ 启动失败: ${error.message}`)
    process.exit(1)
  })
  
  child.on('exit', (code) => {
    if (code !== 0) {
      colorLog('red', `❌ 应用异常退出，退出码: ${code}`)
    } else {
      colorLog('yellow', '📴 应用已停止')
    }
    process.exit(code)
  })
  
  // 处理进程信号
  process.on('SIGINT', () => {
    colorLog('yellow', '📴 收到停止信号，正在关闭应用...')
    child.kill('SIGINT')
  })
  
  process.on('SIGTERM', () => {
    colorLog('yellow', '📴 收到终止信号，正在关闭应用...')
    child.kill('SIGTERM')
  })
}

// 主函数
function main() {
  try {
    // 显示环境信息
    displayEnvironmentInfo()
    
    // 启动应用
    startApplication()
    
  } catch (error) {
    colorLog('red', `❌ 启动脚本错误: ${error.message}`)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

module.exports = { displayEnvironmentInfo, startApplication }
