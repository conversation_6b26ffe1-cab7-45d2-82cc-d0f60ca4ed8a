#!/bin/bash

# 完全重置 Prisma 配置，解决查询引擎错误

echo "🔄 完全重置 Prisma 配置..."
echo ""

# 设置错误时退出
set -e

# 1. 停止可能运行的应用
echo "1. 停止运行中的应用..."
pkill -f "next start" || true
pkill -f "node.*next" || true

# 2. 清理 Prisma 相关文件
echo "2. 清理 Prisma 相关文件..."
rm -rf node_modules/.prisma
rm -rf node_modules/@prisma
rm -rf .next/cache

# 3. 重新安装 Prisma
echo "3. 重新安装 Prisma..."
npm uninstall @prisma/client prisma
npm install @prisma/client@latest prisma@latest

# 4. 生成 Prisma 客户端（指定目标平台）
echo "4. 生成 Prisma 客户端..."
npx prisma generate --force

# 5. 验证 Prisma 安装
echo "5. 验证 Prisma 安装..."
npx prisma --version

# 6. 检查查询引擎文件
echo "6. 检查查询引擎文件..."
QUERY_ENGINE_PATH="node_modules/.prisma/client"

if [ -d "$QUERY_ENGINE_PATH" ]; then
    echo "✅ Prisma 客户端目录存在"
    ls -la "$QUERY_ENGINE_PATH" | grep -E "(query-engine|libquery_engine)" || echo "⚠️  查询引擎文件可能缺失"
else
    echo "❌ Prisma 客户端目录不存在"
    exit 1
fi

# 7. 测试 Prisma 连接
echo "7. 测试 Prisma 连接..."
if [ -f ".env.local" ]; then
    # 加载环境变量
    export $(grep -v '^#' .env.local | xargs)
    
    # 测试数据库连接
    npx prisma db pull --force || echo "⚠️  数据库连接测试失败"
else
    echo "⚠️  .env.local 文件不存在，跳过数据库连接测试"
fi

# 8. 创建测试脚本
echo "8. 创建 Prisma 测试脚本..."
cat > test-prisma.js << 'EOF'
const { PrismaClient } = require('@prisma/client')

async function testPrisma() {
  console.log('🔍 测试 Prisma 连接...')
  
  try {
    const prisma = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'],
    })
    
    console.log('✅ Prisma 客户端创建成功')
    
    // 测试数据库连接
    await prisma.$connect()
    console.log('✅ 数据库连接成功')
    
    // 测试简单查询
    const userCount = await prisma.user.count()
    console.log(`✅ 查询成功，用户数量: ${userCount}`)
    
    await prisma.$disconnect()
    console.log('✅ 数据库连接已关闭')
    
    console.log('🎉 Prisma 测试完成，一切正常!')
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Prisma 测试失败:')
    console.error(error)
    process.exit(1)
  }
}

testPrisma()
EOF

echo ""
echo "🎉 Prisma 重置完成!"
echo ""
echo "📋 验证步骤:"
echo "1. 运行 Prisma 测试: node test-prisma.js"
echo "2. 如果测试通过，启动应用: pnpm start"
echo "3. 如果仍有问题，检查环境变量配置"
echo ""
echo "🔧 故障排除:"
echo "- 检查 .env.local 中的 DATABASE_URL"
echo "- 确保 PostgreSQL 服务正在运行"
echo "- 运行: RUST_BACKTRACE=1 pnpm start 查看详细错误"
echo ""
echo "📞 如需帮助，请查看错误日志并检查:"
echo "- Node.js 版本 (需要 18.17+)"
echo "- Prisma 版本兼容性"
echo "- 数据库连接权限"
