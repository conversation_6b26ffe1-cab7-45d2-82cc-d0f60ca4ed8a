'use client'

import { useSession, signOut } from 'next-auth/react'
import { useState } from 'react'

export default function DebugAuthPage() {
  const { data: session, status } = useSession()
  const [debugInfo, setDebugInfo] = useState<any>(null)

  const testSignOut = async () => {
    console.log('Testing signOut...')
    
    // 测试不同的登出方式
    const tests = [
      {
        name: '默认登出',
        action: () => signOut()
      },
      {
        name: '指定回调 URL',
        action: () => signOut({ callbackUrl: 'https://mxd.hyhuman.xyz' })
      },
      {
        name: '相对路径回调',
        action: () => signOut({ callbackUrl: '/' })
      },
      {
        name: '不重定向',
        action: () => signOut({ redirect: false })
      }
    ]

    return tests
  }

  const fetchDebugInfo = async () => {
    try {
      // 获取环境信息
      const envInfo = {
        NEXTAUTH_URL: process.env.NEXTAUTH_URL,
        NODE_ENV: process.env.NODE_ENV,
        NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL
      }

      // 获取会话信息
      const sessionResponse = await fetch('/api/auth/session')
      const sessionData = await sessionResponse.json()

      // 获取提供商信息
      const providersResponse = await fetch('/api/auth/providers')
      const providersData = await providersResponse.json()

      setDebugInfo({
        env: envInfo,
        session: sessionData,
        providers: providersData,
        currentUrl: window.location.href,
        userAgent: navigator.userAgent
      })
    } catch (error) {
      console.error('获取调试信息失败:', error)
      setDebugInfo({ error: error.message })
    }
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">NextAuth 调试页面</h1>
      
      {/* 会话状态 */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">会话状态</h2>
        <div className="space-y-2">
          <p><strong>状态:</strong> {status}</p>
          {session && (
            <div>
              <p><strong>用户:</strong> {session.user?.email || session.user?.name}</p>
              <p><strong>ID:</strong> {session.user?.id}</p>
            </div>
          )}
        </div>
      </div>

      {/* 登出测试 */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">登出测试</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            onClick={() => signOut()}
            className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
          >
            默认登出
          </button>
          
          <button
            onClick={() => signOut({ callbackUrl: 'https://mxd.hyhuman.xyz' })}
            className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
          >
            指定 HTTPS 回调
          </button>
          
          <button
            onClick={() => signOut({ callbackUrl: '/' })}
            className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
          >
            相对路径回调
          </button>
          
          <button
            onClick={() => signOut({ redirect: false }).then(() => {
              window.location.href = 'https://mxd.hyhuman.xyz'
            })}
            className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
          >
            手动重定向
          </button>
        </div>
      </div>

      {/* 调试信息 */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">调试信息</h2>
          <button
            onClick={fetchDebugInfo}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            获取调试信息
          </button>
        </div>
        
        {debugInfo && (
          <pre className="bg-gray-100 p-4 rounded overflow-auto text-sm">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        )}
      </div>

      {/* API 测试 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">API 测试</h2>
        <div className="space-y-2">
          <div>
            <a 
              href="/api/auth/signin" 
              target="_blank"
              className="text-blue-500 hover:underline"
            >
              /api/auth/signin
            </a>
          </div>
          <div>
            <a 
              href="/api/auth/signout" 
              target="_blank"
              className="text-blue-500 hover:underline"
            >
              /api/auth/signout
            </a>
          </div>
          <div>
            <a 
              href="/api/auth/session" 
              target="_blank"
              className="text-blue-500 hover:underline"
            >
              /api/auth/session
            </a>
          </div>
          <div>
            <a 
              href="/api/auth/providers" 
              target="_blank"
              className="text-blue-500 hover:underline"
            >
              /api/auth/providers
            </a>
          </div>
        </div>
      </div>

      {/* 环境变量显示 */}
      <div className="bg-white rounded-lg shadow p-6 mt-6">
        <h2 className="text-xl font-semibold mb-4">环境变量 (客户端可见)</h2>
        <div className="space-y-2 text-sm">
          <p><strong>NEXT_PUBLIC_APP_URL:</strong> {process.env.NEXT_PUBLIC_APP_URL}</p>
          <p><strong>当前 URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'N/A'}</p>
          <p><strong>Origin:</strong> {typeof window !== 'undefined' ? window.location.origin : 'N/A'}</p>
        </div>
      </div>
    </div>
  )
}
