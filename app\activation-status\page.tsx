'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, CheckCircle, XCircle, Clock, Mail, RefreshCw } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface ActivationStatus {
  exists: boolean
  activated: boolean
  activatedAt?: string
  hasToken?: boolean
  tokenExpired?: boolean
  expiredHours?: number
  expiresAt?: string
  remainingHours?: number
  remainingMinutes?: number
  message: string
}

export default function ActivationStatusPage() {
  const [email, setEmail] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isResending, setIsResending] = useState(false)
  const [status, setStatus] = useState<ActivationStatus | null>(null)
  const [error, setError] = useState('')
  const router = useRouter()

  const checkStatus = async () => {
    if (!email) {
      setError('请输入邮箱地址')
      return
    }

    setIsLoading(true)
    setError('')
    setStatus(null)

    try {
      const response = await fetch('/api/auth/activation-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      const result = await response.json()

      if (response.ok) {
        setStatus(result)
      } else {
        setStatus(result)
      }
    } catch (error) {
      console.error('查询激活状态异常:', error)
      setError('查询失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  const resendEmail = async () => {
    if (!email) return

    setIsResending(true)
    setError('')

    try {
      const response = await fetch('/api/auth/resend-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      const result = await response.json()

      if (response.ok) {
        setError('')
        // 重新查询状态
        setTimeout(() => {
          checkStatus()
        }, 1000)
      } else {
        setError(result.error || '重发邮件失败，请稍后重试')
      }
    } catch (error) {
      console.error('重发邮件异常:', error)
      setError('重发邮件失败，请稍后重试')
    } finally {
      setIsResending(false)
    }
  }

  const getStatusIcon = () => {
    if (!status) return null

    if (status.activated) {
      return <CheckCircle className="h-8 w-8 text-green-500" />
    }

    if (status.exists && status.hasToken && !status.tokenExpired) {
      return <Clock className="h-8 w-8 text-yellow-500" />
    }

    return <XCircle className="h-8 w-8 text-red-500" />
  }

  const getStatusColor = () => {
    if (!status) return 'border-gray-200'

    if (status.activated) return 'border-green-200 bg-green-50'
    if (status.exists && status.hasToken && !status.tokenExpired) return 'border-yellow-200 bg-yellow-50'
    return 'border-red-200 bg-red-50'
  }

  const formatRemainingTime = () => {
    if (!status || !status.remainingHours || !status.remainingMinutes) return ''
    
    if (status.remainingHours > 0) {
      return `${status.remainingHours} 小时 ${status.remainingMinutes} 分钟`
    }
    return `${status.remainingMinutes} 分钟`
  }

  return (
    <div className="container mx-auto py-8 max-w-md">
      <Card>
        <CardHeader>
          <CardTitle>邮箱激活状态查询</CardTitle>
          <CardDescription>
            查询您的邮箱激活状态和验证链接有效期
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">邮箱地址</Label>
            <Input
              id="email"
              type="email"
              placeholder="请输入您的邮箱地址"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && checkStatus()}
            />
          </div>

          <Button
            onClick={checkStatus}
            disabled={isLoading || !email}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                查询中...
              </>
            ) : (
              '查询激活状态'
            )}
          </Button>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {status && (
            <Card className={`${getStatusColor()}`}>
              <CardContent className="pt-6">
                <div className="flex items-center space-x-3 mb-4">
                  {getStatusIcon()}
                  <div>
                    <h3 className="font-semibold">
                      {status.activated ? '邮箱已激活' : 
                       status.exists ? '邮箱未激活' : '邮箱未注册'}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {status.message}
                    </p>
                  </div>
                </div>

                {status.activated && status.activatedAt && (
                  <div className="text-sm text-muted-foreground">
                    激活时间: {new Date(status.activatedAt).toLocaleString('zh-CN')}
                  </div>
                )}

                {status.exists && !status.activated && status.hasToken && !status.tokenExpired && (
                  <div className="space-y-2">
                    <div className="text-sm">
                      <span className="font-medium">剩余有效时间: </span>
                      <span className="text-yellow-600">{formatRemainingTime()}</span>
                    </div>
                    {status.expiresAt && (
                      <div className="text-xs text-muted-foreground">
                        过期时间: {new Date(status.expiresAt).toLocaleString('zh-CN')}
                      </div>
                    )}
                  </div>
                )}

                {status.exists && !status.activated && (status.tokenExpired || !status.hasToken) && (
                  <div className="space-y-3">
                    {status.tokenExpired && status.expiredHours && (
                      <div className="text-sm text-red-600">
                        验证链接已过期 {status.expiredHours} 小时
                      </div>
                    )}
                    
                    <Button
                      onClick={resendEmail}
                      disabled={isResending}
                      variant="outline"
                      size="sm"
                      className="w-full"
                    >
                      {isResending ? (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                          发送中...
                        </>
                      ) : (
                        <>
                          <Mail className="mr-2 h-4 w-4" />
                          重新发送激活邮件
                        </>
                      )}
                    </Button>
                  </div>
                )}

                {status.activated && (
                  <Button
                    onClick={() => router.push('/login')}
                    className="w-full mt-4"
                  >
                    前往登录
                  </Button>
                )}
              </CardContent>
            </Card>
          )}

          <div className="text-center">
            <Button
              variant="link"
              onClick={() => router.push('/login')}
              className="text-sm"
            >
              返回登录页面
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
