#!/bin/bash

# 简化的构建上传脚本
# 用于快速上传本地构建文件到服务器

# 服务器配置 - 请根据您的实际情况修改
SERVER_HOST="your-server-ip"
SERVER_USER="root"
SERVER_PATH="/root/gits/maplestory-info-station"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}🚀 MapleStory 项目部署脚本${NC}"
echo "=================================="

# 检查是否提供了服务器信息
if [ "$SERVER_HOST" = "your-server-ip" ]; then
    echo -e "${RED}❌ 请先配置服务器信息！${NC}"
    echo "编辑此脚本，修改以下变量:"
    echo "  SERVER_HOST=\"您的服务器IP\""
    echo "  SERVER_USER=\"您的用户名\""
    echo "  SERVER_PATH=\"服务器上的项目路径\""
    exit 1
fi

# 1. 检查本地构建
echo -e "${BLUE}📋 检查本地构建...${NC}"
if [ ! -d ".next" ]; then
    echo -e "${YELLOW}⚠️  未找到构建文件，开始构建...${NC}"
    pnpm run build
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 构建失败${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ 找到构建文件${NC}"
fi

# 2. 准备部署文件
echo -e "${BLUE}📦 准备部署文件...${NC}"
if [ ! -d "deploy" ]; then
    node scripts/prepare-deploy.js
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 部署文件准备失败${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ 部署文件已准备${NC}"
fi

# 3. 测试 SSH 连接
echo -e "${BLUE}🔗 测试 SSH 连接...${NC}"
ssh -o ConnectTimeout=5 "$SERVER_USER@$SERVER_HOST" "echo 'SSH连接成功'" 2>/dev/null
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ SSH 连接失败，请检查:${NC}"
    echo "  1. 服务器IP和用户名是否正确"
    echo "  2. SSH密钥是否已配置"
    echo "  3. 服务器是否可访问"
    exit 1
fi
echo -e "${GREEN}✅ SSH 连接正常${NC}"

# 4. 创建备份
echo -e "${BLUE}💾 创建服务器备份...${NC}"
BACKUP_NAME="backup-$(date +%Y%m%d-%H%M%S)"
ssh "$SERVER_USER@$SERVER_HOST" "mkdir -p /root/backups && cp -r $SERVER_PATH /root/backups/$BACKUP_NAME" 2>/dev/null
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 备份创建成功: $BACKUP_NAME${NC}"
else
    echo -e "${YELLOW}⚠️  备份创建失败，继续部署...${NC}"
fi

# 5. 停止应用
echo -e "${BLUE}🛑 停止应用...${NC}"
ssh "$SERVER_USER@$SERVER_HOST" "cd $SERVER_PATH && pm2 stop maplestory-info-station 2>/dev/null || pkill -f 'next start' || true"

# 6. 上传文件
echo -e "${BLUE}📤 上传文件到服务器...${NC}"
rsync -avz --progress deploy/ "$SERVER_USER@$SERVER_HOST:$SERVER_PATH/"
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 文件上传失败${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 文件上传完成${NC}"

# 7. 安装依赖
echo -e "${BLUE}📦 安装生产依赖...${NC}"
ssh "$SERVER_USER@$SERVER_HOST" "cd $SERVER_PATH && npm ci --only=production"
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 依赖安装失败${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 依赖安装完成${NC}"

# 8. 启动应用
echo -e "${BLUE}🚀 启动应用...${NC}"
ssh "$SERVER_USER@$SERVER_HOST" "cd $SERVER_PATH && pm2 start ecosystem.config.js 2>/dev/null || pm2 restart maplestory-info-station 2>/dev/null || nohup npm start > app.log 2>&1 &"
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 应用启动失败${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 应用启动成功${NC}"

# 9. 验证部署
echo -e "${BLUE}🔍 验证部署...${NC}"
sleep 5
ssh "$SERVER_USER@$SERVER_HOST" "curl -f http://localhost:3000 > /dev/null 2>&1"
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 应用运行正常${NC}"
else
    echo -e "${YELLOW}⚠️  应用可能还在启动中${NC}"
fi

echo ""
echo -e "${GREEN}🎉 部署完成！${NC}"
echo ""
echo -e "${CYAN}📋 部署信息:${NC}"
echo "  服务器: $SERVER_HOST"
echo "  路径: $SERVER_PATH"
echo "  时间: $(date)"
echo ""
echo -e "${CYAN}🔗 有用的命令:${NC}"
echo "  查看日志: ssh $SERVER_USER@$SERVER_HOST \"pm2 logs maplestory-info-station\""
echo "  查看状态: ssh $SERVER_USER@$SERVER_HOST \"pm2 status\""
echo "  重启应用: ssh $SERVER_USER@$SERVER_HOST \"pm2 restart maplestory-info-station\""

if [ ! -z "$BACKUP_NAME" ]; then
    echo ""
    echo -e "${YELLOW}💾 回滚命令 (如需要):${NC}"
    echo "  ssh $SERVER_USER@$SERVER_HOST \"cp -r /root/backups/$BACKUP_NAME/* $SERVER_PATH/ && pm2 restart maplestory-info-station\""
fi
