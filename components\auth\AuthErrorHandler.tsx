'use client'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Mail, RefreshCw, AlertCircle } from 'lucide-react'

interface AuthErrorHandlerProps {
  error: string
  onResendEmail?: () => void
  isResending?: boolean
  email?: string
}

export function AuthErrorHandler({ 
  error, 
  onResendEmail, 
  isResending = false,
  email 
}: AuthErrorHandlerProps) {
  // 检查是否是邮箱验证相关错误
  const isEmailVerificationError = error.includes('验证邮箱') || 
                                   error.includes('邮箱未验证') ||
                                   error.includes('请先验证')

  // 检查是否是账户被禁用错误
  const isAccountDisabledError = error.includes('账户已被禁用') ||
                                error.includes('账户被禁用')

  // 检查是否是用户不存在或密码错误
  const isCredentialsError = error.includes('用户不存在') ||
                            error.includes('密码错误') ||
                            error.includes('登录信息')

  const getErrorIcon = () => {
    if (isEmailVerificationError) return <Mail className="h-4 w-4" />
    if (isAccountDisabledError) return <AlertCircle className="h-4 w-4" />
    return <AlertCircle className="h-4 w-4" />
  }

  const getErrorTitle = () => {
    if (isEmailVerificationError) return '邮箱验证'
    if (isAccountDisabledError) return '账户状态'
    if (isCredentialsError) return '登录失败'
    return '登录错误'
  }

  const getErrorMessage = () => {
    if (isEmailVerificationError) {
      return '您的邮箱尚未验证，请先验证邮箱后再登录。'
    }
    if (isAccountDisabledError) {
      return '您的账户已被禁用，如有疑问请联系客服。'
    }
    if (isCredentialsError) {
      return '邮箱或密码错误，请检查后重试。'
    }
    return error
  }

  const getErrorSuggestion = () => {
    if (isEmailVerificationError) {
      return '如果您没有收到验证邮件，可以点击下方按钮重新发送。'
    }
    if (isAccountDisabledError) {
      return '请联系客服了解账户状态详情。'
    }
    if (isCredentialsError) {
      return '请确认邮箱地址和密码是否正确，或尝试重置密码。'
    }
    return '请稍后重试，如问题持续存在请联系客服。'
  }

  return (
    <Alert variant="destructive" className="space-y-3">
      <div className="flex items-center space-x-2">
        {getErrorIcon()}
        <div className="font-medium">{getErrorTitle()}</div>
      </div>
      
      <AlertDescription className="space-y-2">
        <p>{getErrorMessage()}</p>
        <p className="text-sm text-muted-foreground">{getErrorSuggestion()}</p>
        
        {/* 邮箱验证错误时显示重发按钮 */}
        {isEmailVerificationError && onResendEmail && (
          <div className="pt-2">
            <Button
              onClick={onResendEmail}
              disabled={isResending}
              variant="outline"
              size="sm"
              className="w-full sm:w-auto"
            >
              {isResending ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  发送中...
                </>
              ) : (
                <>
                  <Mail className="mr-2 h-4 w-4" />
                  重新发送验证邮件
                </>
              )}
            </Button>
            {email && (
              <p className="text-xs text-muted-foreground mt-1">
                将发送到: {email}
              </p>
            )}
          </div>
        )}
      </AlertDescription>
    </Alert>
  )
}

// 错误类型枚举
export enum AuthErrorType {
  EMAIL_NOT_VERIFIED = 'EMAIL_NOT_VERIFIED',
  ACCOUNT_DISABLED = 'ACCOUNT_DISABLED',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  UNKNOWN = 'UNKNOWN'
}

// 错误分类函数
export function classifyAuthError(error: string): AuthErrorType {
  if (error.includes('验证邮箱') || error.includes('邮箱未验证') || error.includes('请先验证')) {
    return AuthErrorType.EMAIL_NOT_VERIFIED
  }
  if (error.includes('账户已被禁用') || error.includes('账户被禁用')) {
    return AuthErrorType.ACCOUNT_DISABLED
  }
  if (error.includes('用户不存在') || error.includes('密码错误') || error.includes('登录信息')) {
    return AuthErrorType.INVALID_CREDENTIALS
  }
  return AuthErrorType.UNKNOWN
}

// 获取用户友好的错误信息
export function getFriendlyErrorMessage(error: string): string {
  const errorType = classifyAuthError(error)
  
  switch (errorType) {
    case AuthErrorType.EMAIL_NOT_VERIFIED:
      return '请先验证邮箱后再登录'
    case AuthErrorType.ACCOUNT_DISABLED:
      return '账户已被禁用'
    case AuthErrorType.INVALID_CREDENTIALS:
      return '邮箱或密码错误'
    default:
      return error
  }
}
