# 本地打包上传部署指南

## 🎯 部署流程概述

本地构建 → 准备部署文件 → 上传到服务器 → 安装依赖 → 启动应用

## 🚀 快速部署（推荐）

### 方法一：使用自动化脚本

```bash
# 1. 配置服务器信息（首次使用）
cp deploy-config.example.json deploy-config.json
# 编辑 deploy-config.json 填入您的服务器信息

# 2. 一键部署
./scripts/upload-build.sh
```

### 方法二：使用 Node.js 脚本

```bash
# 1. 编辑服务器配置
# 修改 scripts/deploy-to-server.js 中的 SERVER_CONFIG

# 2. 执行部署
node scripts/deploy-to-server.js
```

## 📋 详细步骤

### 步骤 1: 本地构建

```bash
# 安装依赖（如果还没有）
pnpm install

# 构建项目
pnpm run build
```

**验证构建成功**：
- 检查 `.next` 目录是否存在
- 确认没有构建错误

### 步骤 2: 准备部署文件

```bash
# 自动准备部署文件
node scripts/prepare-deploy.js
```

**生成的部署文件**：
```
deploy/
├── .next/                 # Next.js 构建输出
├── public/               # 静态资源
├── package.json          # 项目配置
├── pnpm-lock.yaml       # 依赖锁定文件
├── next.config.mjs      # Next.js 配置
├── prisma/              # 数据库配置
├── ecosystem.config.js  # PM2 配置
└── README-DEPLOY.md     # 部署说明
```

### 步骤 3: 配置服务器信息

#### 方法 A: 修改 upload-build.sh

```bash
# 编辑脚本
nano scripts/upload-build.sh

# 修改以下变量
SERVER_HOST="您的服务器IP"
SERVER_USER="root"
SERVER_PATH="/root/gits/maplestory-info-station"
```

#### 方法 B: 修改 deploy-to-server.js

```bash
# 编辑脚本
nano scripts/deploy-to-server.js

# 修改 SERVER_CONFIG 对象
const SERVER_CONFIG = {
  host: '您的服务器IP',
  user: 'root',
  remotePath: '/root/gits/maplestory-info-station'
}
```

### 步骤 4: 配置 SSH 密钥（重要）

```bash
# 生成 SSH 密钥（如果还没有）
ssh-keygen -t rsa -b 4096

# 复制公钥到服务器
ssh-copy-id root@您的服务器IP

# 测试连接
ssh root@您的服务器IP "echo 'SSH连接成功'"
```

### 步骤 5: 执行部署

#### 使用 Shell 脚本（推荐）

```bash
# 执行部署
./scripts/upload-build.sh
```

#### 使用 Node.js 脚本

```bash
# 执行部署
node scripts/deploy-to-server.js
```

#### 手动上传（备选方案）

```bash
# 1. 上传部署文件
scp -r deploy/* root@您的服务器IP:/root/gits/maplestory-info-station/

# 2. 连接到服务器
ssh root@您的服务器IP

# 3. 在服务器上执行
cd /root/gits/maplestory-info-station
npm ci --only=production
pm2 restart maplestory-info-station
```

## 🔧 部署脚本功能

### upload-build.sh 功能

- ✅ 自动检查本地构建
- ✅ 准备部署文件
- ✅ 测试 SSH 连接
- ✅ 创建服务器备份
- ✅ 上传文件（使用 rsync）
- ✅ 安装生产依赖
- ✅ 重启应用
- ✅ 验证部署

### deploy-to-server.js 功能

- ✅ 完整的错误处理
- ✅ 彩色输出和进度显示
- ✅ 自动备份和回滚支持
- ✅ 多种启动方式尝试
- ✅ 部署后验证

## 📊 部署输出示例

```
🚀 MapleStory 项目部署脚本
==================================
📋 检查本地构建...
✅ 找到构建文件
📦 准备部署文件...
✅ 部署文件已准备
🔗 测试 SSH 连接...
✅ SSH 连接正常
💾 创建服务器备份...
✅ 备份创建成功: backup-20241224-143022
🛑 停止应用...
📤 上传文件到服务器...
✅ 文件上传完成
📦 安装生产依赖...
✅ 依赖安装完成
🚀 启动应用...
✅ 应用启动成功
🔍 验证部署...
✅ 应用运行正常

🎉 部署完成！

📋 部署信息:
  服务器: your-server-ip
  路径: /root/gits/maplestory-info-station
  时间: 2024年12月24日 14:30:22

🔗 有用的命令:
  查看日志: ssh root@your-server-ip "pm2 logs maplestory-info-station"
  查看状态: ssh root@your-server-ip "pm2 status"
  重启应用: ssh root@your-server-ip "pm2 restart maplestory-info-station"

💾 回滚命令 (如需要):
  ssh root@your-server-ip "cp -r /root/backups/backup-20241224-143022/* /root/gits/maplestory-info-station/ && pm2 restart maplestory-info-station"
```

## 🔍 故障排除

### 问题 1: SSH 连接失败

**解决方案**：
```bash
# 检查 SSH 密钥
ssh-add -l

# 重新配置密钥
ssh-copy-id root@您的服务器IP

# 测试连接
ssh -v root@您的服务器IP
```

### 问题 2: 文件上传失败

**解决方案**：
```bash
# 检查磁盘空间
ssh root@您的服务器IP "df -h"

# 检查权限
ssh root@您的服务器IP "ls -la /root/gits/"

# 手动创建目录
ssh root@您的服务器IP "mkdir -p /root/gits/maplestory-info-station"
```

### 问题 3: 应用启动失败

**解决方案**：
```bash
# 查看日志
ssh root@您的服务器IP "cd /root/gits/maplestory-info-station && pm2 logs"

# 检查环境变量
ssh root@您的服务器IP "cd /root/gits/maplestory-info-station && cat .env.local"

# 手动启动
ssh root@您的服务器IP "cd /root/gits/maplestory-info-station && npm start"
```

## 🎯 最佳实践

### 1. 部署前检查

- ✅ 确认本地构建成功
- ✅ 测试 SSH 连接
- ✅ 检查服务器磁盘空间
- ✅ 备份重要数据

### 2. 部署后验证

- ✅ 检查应用状态
- ✅ 测试关键功能
- ✅ 查看错误日志
- ✅ 验证环境变量

### 3. 回滚准备

- ✅ 保留备份文件
- ✅ 记录部署时间
- ✅ 准备回滚命令
- ✅ 监控应用状态

## 📝 部署清单

- [ ] 本地构建成功
- [ ] SSH 密钥已配置
- [ ] 服务器信息已配置
- [ ] 部署脚本已测试
- [ ] 备份已创建
- [ ] 文件上传成功
- [ ] 依赖安装完成
- [ ] 应用启动成功
- [ ] 功能验证通过

---

**总结**：通过这套自动化部署方案，您可以轻松地将本地构建的文件部署到服务器，整个过程自动化、安全且可回滚。
