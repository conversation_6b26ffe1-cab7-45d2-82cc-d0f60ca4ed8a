#!/usr/bin/env python3
"""
冒险岛情报站装备强化API Python客户端示例

使用方法:
    python enhancement_api_client.py

依赖:
    pip install requests

作者: 冒险岛情报站开发团队
版本: 1.0.0
"""

import requests
import json
import time
from typing import Dict, List, Optional, Union
from dataclasses import dataclass
from enum import Enum


class EnhancementType(Enum):
    """强化类型枚举"""
    STARFORCE = "starforce"
    POTENTIAL = "potential"
    BONUSSTAT = "bonusstat"


class ResultType(Enum):
    """强化结果类型枚举"""
    SUCCESS = "success"
    FAILED = "failed"
    MAJOR_FAILURE = "major_failure"


@dataclass
class EnhancementRequest:
    """强化请求数据类"""
    equipment_id: str
    current_level: int
    enhancement_type: EnhancementType
    starcatch_enabled: bool = False
    prevent_enabled: bool = False
    minigame_bonus: float = 0.0

    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            "equipmentId": self.equipment_id,
            "currentLevel": self.current_level,
            "enhancementType": self.enhancement_type.value,
            "starcatchEnabled": self.starcatch_enabled,
            "preventEnabled": self.prevent_enabled,
            "minigameBonus": self.minigame_bonus
        }


@dataclass
class EnhancementResult:
    """强化结果数据类"""
    success: bool
    result_type: ResultType
    message: str
    new_level: int
    previous_level: int
    cost_mesos: int
    materials: List[Dict[str, Union[str, int]]]
    probability: Dict[str, float]
    request_id: str
    timestamp: str

    @classmethod
    def from_dict(cls, data: Dict) -> 'EnhancementResult':
        """从字典创建实例"""
        return cls(
            success=data["success"],
            result_type=ResultType(data["result"]["type"]),
            message=data["result"]["message"],
            new_level=data["result"]["newLevel"],
            previous_level=data["result"]["previousLevel"],
            cost_mesos=data["cost"]["mesos"],
            materials=data["cost"].get("materials", []),
            probability=data["probability"],
            request_id=data["requestId"],
            timestamp=data["timestamp"]
        )


class EnhancementApiError(Exception):
    """API错误异常类"""
    def __init__(self, message: str, error_code: str = None, details: Dict = None):
        super().__init__(message)
        self.error_code = error_code
        self.details = details or {}


class EnhancementApiClient:
    """装备强化API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:3000", api_key: Optional[str] = None):
        """
        初始化API客户端
        
        Args:
            base_url: API基础URL
            api_key: API密钥（可选）
        """
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        
        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'MapleStory-Enhancement-API-Client/1.0.0'
        })
        
        if self.api_key:
            self.session.headers['X-API-Key'] = self.api_key

    def enhance_equipment(self, request: EnhancementRequest) -> EnhancementResult:
        """
        执行装备强化
        
        Args:
            request: 强化请求参数
            
        Returns:
            强化结果
            
        Raises:
            EnhancementApiError: API调用失败
        """
        url = f"{self.base_url}/api/enhancement/enhance"
        
        try:
            response = self.session.post(
                url,
                json=request.to_dict(),
                timeout=30
            )
            
            data = response.json()
            
            if not response.ok:
                raise EnhancementApiError(
                    data.get("error", "API request failed"),
                    data.get("errorCode"),
                    data.get("details")
                )
            
            return EnhancementResult.from_dict(data)
            
        except requests.RequestException as e:
            raise EnhancementApiError(f"Network error: {str(e)}")
        except json.JSONDecodeError as e:
            raise EnhancementApiError(f"Invalid JSON response: {str(e)}")

    def get_api_info(self) -> Dict:
        """
        获取API信息
        
        Returns:
            API信息字典
        """
        url = f"{self.base_url}/api/enhancement/enhance"
        
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            raise EnhancementApiError(f"Failed to get API info: {str(e)}")

    def batch_enhance(self, requests_list: List[EnhancementRequest], 
                     max_concurrent: int = 5, stop_on_failure: bool = False) -> List[EnhancementResult]:
        """
        批量强化装备
        
        Args:
            requests_list: 强化请求列表
            max_concurrent: 最大并发数
            stop_on_failure: 是否在失败时停止
            
        Returns:
            强化结果列表
        """
        results = []
        errors = []
        
        # 简化的批量处理（顺序执行）
        for i, req in enumerate(requests_list):
            try:
                print(f"处理第 {i+1}/{len(requests_list)} 个强化请求...")
                result = self.enhance_equipment(req)
                results.append(result)
                
                # 添加延迟避免过快请求
                time.sleep(0.1)
                
            except EnhancementApiError as e:
                errors.append(e)
                if stop_on_failure:
                    print(f"强化失败，停止批量处理: {e}")
                    break
                else:
                    print(f"强化失败，继续处理: {e}")
        
        if errors and not stop_on_failure:
            print(f"批量强化完成，但有 {len(errors)} 个请求失败")
        
        return results


def analyze_results(results: List[EnhancementResult]) -> Dict:
    """
    分析强化结果统计
    
    Args:
        results: 强化结果列表
        
    Returns:
        统计信息字典
    """
    if not results:
        return {}
    
    total_attempts = len(results)
    success_count = sum(1 for r in results if r.result_type == ResultType.SUCCESS)
    failure_count = sum(1 for r in results if r.result_type == ResultType.FAILED)
    major_failure_count = sum(1 for r in results if r.result_type == ResultType.MAJOR_FAILURE)
    total_cost = sum(r.cost_mesos for r in results)
    
    return {
        "total_attempts": total_attempts,
        "success_count": success_count,
        "failure_count": failure_count,
        "major_failure_count": major_failure_count,
        "success_rate": (success_count / total_attempts) * 100 if total_attempts > 0 else 0,
        "total_cost": total_cost,
        "average_cost": total_cost / total_attempts if total_attempts > 0 else 0
    }


def main():
    """主函数 - 演示API使用"""
    print("🔨 冒险岛装备强化API Python客户端示例")
    print("=" * 50)
    
    # 创建API客户端
    client = EnhancementApiClient(
        base_url="http://localhost:3000",
        api_key="demo-key-123"  # 可选的API密钥
    )
    
    try:
        # 1. 获取API信息
        print("📋 获取API信息...")
        api_info = client.get_api_info()
        print(f"API版本: {api_info.get('version', 'Unknown')}")
        print(f"API描述: {api_info.get('description', 'Unknown')}")
        print()
        
        # 2. 单次强化示例
        print("⚡ 执行单次星力强化...")
        request = EnhancementRequest(
            equipment_id="weapon_001",
            current_level=10,
            enhancement_type=EnhancementType.STARFORCE,
            starcatch_enabled=False,
            prevent_enabled=True,
            minigame_bonus=5.0
        )
        
        result = client.enhance_equipment(request)
        
        print(f"强化结果: {result.result_type.value}")
        print(f"消息: {result.message}")
        print(f"等级变化: {result.previous_level} → {result.new_level}")
        print(f"消耗金币: {result.cost_mesos:,}")
        print(f"成功概率: {result.probability['success']:.1f}%")
        print()
        
        # 3. 批量强化示例
        print("🔄 执行批量强化...")
        batch_requests = [
            EnhancementRequest("armor_001", 5, EnhancementType.STARFORCE, prevent_enabled=True),
            EnhancementRequest("accessory_001", 0, EnhancementType.POTENTIAL),
            EnhancementRequest("weapon_002", 15, EnhancementType.STARFORCE, starcatch_enabled=True),
        ]
        
        batch_results = client.batch_enhance(batch_requests)
        
        # 4. 分析结果
        stats = analyze_results(batch_results)
        print("📊 批量强化统计:")
        print(f"总尝试次数: {stats.get('total_attempts', 0)}")
        print(f"成功次数: {stats.get('success_count', 0)}")
        print(f"失败次数: {stats.get('failure_count', 0)}")
        print(f"大失败次数: {stats.get('major_failure_count', 0)}")
        print(f"成功率: {stats.get('success_rate', 0):.1f}%")
        print(f"总消耗: {stats.get('total_cost', 0):,} 金币")
        print(f"平均消耗: {stats.get('average_cost', 0):,.0f} 金币")
        
    except EnhancementApiError as e:
        print(f"❌ API错误: {e}")
        if e.error_code:
            print(f"错误代码: {e.error_code}")
        if e.details:
            print(f"详细信息: {e.details}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")


if __name__ == "__main__":
    main()
