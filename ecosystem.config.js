module.exports = {
  apps: [
    {
      // 应用配置
      name: 'maplestory-info-station',
      script: 'npm',
      args: 'start',
      cwd: '/home/<USER>/app',
      
      // 集群配置
      instances: process.env.NODE_ENV === 'production' ? 'max' : 1,
      exec_mode: process.env.NODE_ENV === 'production' ? 'cluster' : 'fork',
      
      // 环境变量
      env: {
        NODE_ENV: 'development',
        PORT: 3000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      
      // 环境变量文件
      env_file: process.env.NODE_ENV === 'production' ? '.env.production' : '.env.local',
      
      // 日志配置
      log_file: '/home/<USER>/logs/app.log',
      out_file: '/home/<USER>/logs/out.log',
      error_file: '/home/<USER>/logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // 性能配置
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024',
      
      // 监控配置
      min_uptime: '10s',
      max_restarts: 10,
      
      // 健康检查
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true,
      
      // 自动重启配置
      autorestart: true,
      watch: false,
      ignore_watch: [
        'node_modules',
        '.next',
        'logs',
        'backups'
      ],
      
      // 进程配置
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // 源码映射支持
      source_map_support: true,
      
      // 时间配置
      time: true
    }
  ],
  
  // 部署配置
  deploy: {
    production: {
      user: 'maplestory',
      host: ['your-server-ip'],
      ref: 'origin/main',
      repo: 'your-git-repository-url',
      path: '/home/<USER>/app',
      'pre-deploy-local': '',
      'post-deploy': 'npm ci --only=production && npm run build && npx prisma generate && npx prisma db push && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    },
    
    staging: {
      user: 'maplestory',
      host: ['staging-server-ip'],
      ref: 'origin/develop',
      repo: 'your-git-repository-url',
      path: '/home/<USER>/app-staging',
      'post-deploy': 'npm ci && npm run build && npx prisma generate && npx prisma db push && pm2 reload ecosystem.config.js --env staging',
      env: {
        NODE_ENV: 'staging',
        PORT: 3001
      }
    }
  }
}
