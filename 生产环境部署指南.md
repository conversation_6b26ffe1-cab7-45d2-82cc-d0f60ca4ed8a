# 🚀 MapleStory 信息站 - 生产环境部署指南

## 📋 部署概述

您的项目已经成功构建并准备好部署到生产环境。所有必要的文件都已经整理到 `deploy/` 目录中。

## 📁 部署文件结构

```
deploy/                          # 部署根目录
├── .next/                      # Next.js 构建输出 (必需)
│   ├── static/                 # 静态资源 (JS, CSS, 字体)
│   ├── server/                 # 服务端代码
│   └── [其他构建文件]
├── public/                     # 公共静态资源 (必需)
├── prisma/                     # 数据库配置 (必需)
├── docs/                       # 数据文件 (必需)
│   └── starforcePB.csv        # 星力概率数据
├── package.json               # 依赖配置 (必需)
├── pnpm-lock.yaml            # 锁定文件
├── next.config.mjs           # Next.js 配置 (必需)
├── .env.example              # 环境变量模板
├── start.sh                  # Linux/Mac 启动脚本
├── start.bat                 # Windows 启动脚本
├── README-DEPLOY.md          # 详细部署说明
└── README.md                 # 项目说明
```

## 🎯 部署方案选择

### 方案一：直接部署 deploy 目录 (推荐)

**适用场景**: 自有服务器、VPS、云服务器

**步骤**:
1. 将整个 `deploy/` 目录上传到服务器
2. 配置环境变量
3. 安装依赖并启动

### 方案二：创建部署压缩包

**适用场景**: 需要通过文件传输或版本控制

**创建压缩包**:
```bash
node scripts/create-deploy-package.js
```

这会创建一个包含所有部署文件的 zip 压缩包。

### 方案三：Docker 部署

**适用场景**: 容器化部署

我可以为您创建 Dockerfile 和 docker-compose.yml 文件。

## 🔧 环境要求

### 服务器要求
- **操作系统**: Linux (Ubuntu 20.04+), CentOS 7+, Windows Server
- **内存**: 最少 2GB RAM (推荐 4GB+)
- **存储**: 最少 10GB 可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **Node.js**: 18.17+ (推荐 LTS 版本)
- **npm**: 9.0+ 或 **pnpm**: 8.0+
- **PostgreSQL**: 13+ (数据库)
- **Redis**: 6.0+ (可选，用于缓存)

## 📝 详细部署步骤

### 1. 准备服务器环境

**安装 Node.js (Ubuntu/CentOS)**:
```bash
# 使用 NodeSource 仓库
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt-get install -y nodejs

# 或使用 nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install --lts
```

**安装 PostgreSQL**:
```bash
# Ubuntu
sudo apt update
sudo apt install postgresql postgresql-contrib

# 创建数据库
sudo -u postgres createdb mxd_info_db
```

### 2. 上传部署文件

**方法一: SCP 上传**:
```bash
# 上传整个 deploy 目录
scp -r deploy/ user@your-server:/path/to/app/

# 或上传压缩包后解压
scp maplestory-info-station-*.zip user@your-server:/path/to/app/
ssh user@your-server "cd /path/to/app && unzip maplestory-info-station-*.zip"
```

**方法二: Git 部署**:
```bash
# 在服务器上克隆仓库
git clone https://github.com/your-username/maplestory-info-station.git
cd maplestory-info-station

# 构建项目
npm install
npm run build
```

### 3. 配置环境变量

在部署目录创建 `.env.local` 文件：

```bash
# 数据库配置
DATABASE_URL="postgresql://postgres:your_password@localhost:5432/mxd_info_db"

# NextAuth 配置
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-very-secure-secret-key-here"

# 邮件配置 (可选)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Redis 配置 (可选)
REDIS_URL="redis://localhost:6379"

# 其他配置
NODE_ENV="production"
PORT="3000"
```

### 4. 安装依赖

```bash
cd /path/to/app/deploy

# 使用 npm
npm install --production

# 或使用 pnpm (推荐)
npm install -g pnpm
pnpm install --production
```

### 5. 设置数据库

```bash
# 生成 Prisma 客户端
npx prisma generate

# 运行数据库迁移
npx prisma migrate deploy

# (可选) 填充初始数据
npx prisma db seed
```

### 6. 启动应用

**方法一: 直接启动**:
```bash
npm start
```

**方法二: 使用启动脚本**:
```bash
# Linux/Mac
chmod +x start.sh
./start.sh

# Windows
start.bat
```

**方法三: 使用 PM2 (推荐生产环境)**:
```bash
# 安装 PM2
npm install -g pm2

# 启动应用
pm2 start npm --name "maplestory-info" -- start

# 设置开机自启
pm2 startup
pm2 save
```

## 🌐 Web 服务器配置

### Nginx 配置

创建 `/etc/nginx/sites-available/maplestory-info`:

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;

    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    # SSL 证书配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    # 代理到 Next.js 应用
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # 静态文件缓存优化
    location /_next/static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        proxy_pass http://localhost:3000;
    }

    # 安全头部
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
}
```

启用配置：
```bash
sudo ln -s /etc/nginx/sites-available/maplestory-info /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔒 安全配置

### 1. 防火墙设置

```bash
# Ubuntu UFW
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable

# CentOS firewalld
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 2. SSL 证书

**使用 Let's Encrypt (免费)**:
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 3. 数据库安全

```bash
# PostgreSQL 安全配置
sudo -u postgres psql
\password postgres  # 设置强密码
```

## 📊 监控和维护

### 1. 应用监控

**使用 PM2**:
```bash
pm2 status          # 查看状态
pm2 logs            # 查看日志
pm2 monit           # 实时监控
pm2 restart all     # 重启应用
```

### 2. 系统监控

**安装监控工具**:
```bash
# htop - 系统资源监控
sudo apt install htop

# nginx 状态监控
sudo apt install nginx-extras
```

### 3. 日志管理

**配置日志轮转**:
```bash
# 创建 logrotate 配置
sudo nano /etc/logrotate.d/maplestory-info
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**:
   ```bash
   sudo lsof -i :3000
   sudo kill -9 <PID>
   ```

2. **数据库连接失败**:
   - 检查 DATABASE_URL 配置
   - 确认 PostgreSQL 服务运行
   - 检查防火墙设置

3. **静态文件 404**:
   - 确认 .next/static 目录存在
   - 检查文件权限

4. **内存不足**:
   - 增加服务器内存
   - 配置 swap 分区

### 日志查看

```bash
# PM2 日志
pm2 logs

# Nginx 日志
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log

# 系统日志
sudo journalctl -u nginx
```

## 🎉 部署完成检查

部署完成后，请验证以下功能：

- [ ] 网站可以正常访问
- [ ] 用户注册和登录功能正常
- [ ] 强化模拟器功能正常
- [ ] API 接口响应正常
- [ ] 数据库连接正常
- [ ] 邮件发送功能正常 (如果配置)
- [ ] SSL 证书有效
- [ ] 性能监控正常

## 📞 技术支持

如果在部署过程中遇到问题，请检查：

1. **环境要求**: Node.js 版本、数据库配置
2. **文件权限**: 确保应用有读写权限
3. **网络连接**: 数据库、Redis、外部 API
4. **日志信息**: 查看详细错误信息

---

🎊 **恭喜！您的 MapleStory 信息站已经成功部署到生产环境！**
