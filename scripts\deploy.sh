#!/bin/bash

# 冒险岛情报站 - 自动部署脚本
# 使用方法: ./deploy.sh [环境] [选项]
# 环境: dev, staging, production
# 选项: --skip-build, --skip-db, --rollback

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
APP_NAME="maplestory-info-station"
APP_DIR="/home/<USER>/app"
BACKUP_DIR="/home/<USER>/backups"
LOG_FILE="/home/<USER>/logs/deploy.log"

# 参数解析
ENVIRONMENT=${1:-production}
SKIP_BUILD=false
SKIP_DB=false
ROLLBACK=false

for arg in "$@"; do
    case $arg in
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --skip-db)
            SKIP_DB=true
            shift
            ;;
        --rollback)
            ROLLBACK=true
            shift
            ;;
    esac
done

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO:${NC} $1" | tee -a "$LOG_FILE"
}

# 检查依赖
check_dependencies() {
    log "检查系统依赖..."
    
    command -v node >/dev/null 2>&1 || error "Node.js 未安装"
    command -v npm >/dev/null 2>&1 || error "npm 未安装"
    command -v pm2 >/dev/null 2>&1 || error "PM2 未安装"
    command -v git >/dev/null 2>&1 || error "Git 未安装"
    
    log "依赖检查完成"
}

# 创建备份
create_backup() {
    log "创建备份..."
    
    local backup_name="backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    if [ -d "$APP_DIR" ]; then
        tar -czf "$BACKUP_DIR/${backup_name}.tar.gz" -C "$APP_DIR" . || error "备份失败"
        log "备份创建成功: ${backup_name}.tar.gz"
    fi
    
    # 数据库备份
    if command -v pg_dump >/dev/null 2>&1; then
        pg_dump -h localhost -U maplestory mxd_info_db | gzip > "$BACKUP_DIR/db_${backup_name}.sql.gz" || warning "数据库备份失败"
        log "数据库备份创建成功: db_${backup_name}.sql.gz"
    fi
}

# 回滚功能
rollback() {
    log "开始回滚..."
    
    local latest_backup=$(ls -t "$BACKUP_DIR"/backup_*.tar.gz 2>/dev/null | head -n1)
    
    if [ -z "$latest_backup" ]; then
        error "未找到备份文件"
    fi
    
    log "使用备份文件: $latest_backup"
    
    # 停止应用
    pm2 stop "$APP_NAME" || true
    
    # 恢复文件
    rm -rf "$APP_DIR"
    mkdir -p "$APP_DIR"
    tar -xzf "$latest_backup" -C "$APP_DIR" || error "恢复失败"
    
    # 重启应用
    pm2 start "$APP_NAME" || error "应用启动失败"
    
    log "回滚完成"
    exit 0
}

# 部署代码
deploy_code() {
    log "部署代码..."
    
    cd "$APP_DIR" || error "无法进入应用目录"
    
    # 拉取最新代码
    git fetch origin || error "拉取代码失败"
    git reset --hard origin/main || error "重置代码失败"
    
    # 安装依赖
    npm ci --only=production || error "依赖安装失败"
    
    log "代码部署完成"
}

# 构建应用
build_app() {
    if [ "$SKIP_BUILD" = true ]; then
        info "跳过构建步骤"
        return
    fi
    
    log "构建应用..."
    
    cd "$APP_DIR" || error "无法进入应用目录"
    npm run build || error "构建失败"
    
    log "应用构建完成"
}

# 数据库迁移
migrate_database() {
    if [ "$SKIP_DB" = true ]; then
        info "跳过数据库迁移"
        return
    fi
    
    log "执行数据库迁移..."
    
    cd "$APP_DIR" || error "无法进入应用目录"
    
    npx prisma generate || error "Prisma 客户端生成失败"
    npx prisma db push || error "数据库迁移失败"
    
    log "数据库迁移完成"
}

# 重启应用
restart_app() {
    log "重启应用..."
    
    # 检查应用是否存在
    if pm2 list | grep -q "$APP_NAME"; then
        pm2 restart "$APP_NAME" || error "应用重启失败"
    else
        pm2 start ecosystem.config.js || error "应用启动失败"
    fi
    
    # 等待应用启动
    sleep 10
    
    log "应用重启完成"
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:3000/health >/dev/null 2>&1; then
            log "健康检查通过"
            return 0
        fi
        
        info "健康检查失败，重试 $attempt/$max_attempts"
        sleep 2
        ((attempt++))
    done
    
    error "健康检查失败，部署可能有问题"
}

# 清理旧文件
cleanup() {
    log "清理旧文件..."
    
    # 清理旧备份（保留最近10个）
    ls -t "$BACKUP_DIR"/backup_*.tar.gz 2>/dev/null | tail -n +11 | xargs rm -f
    
    # 清理旧日志（保留最近30天）
    find /home/<USER>/logs -name "*.log" -mtime +30 -delete
    
    log "清理完成"
}

# 主函数
main() {
    log "开始部署 - 环境: $ENVIRONMENT"
    
    # 处理回滚
    if [ "$ROLLBACK" = true ]; then
        rollback
    fi
    
    # 检查依赖
    check_dependencies
    
    # 创建备份
    create_backup
    
    # 部署流程
    deploy_code
    build_app
    migrate_database
    restart_app
    health_check
    cleanup
    
    log "部署完成！"
    info "应用访问地址: http://localhost:3000"
}

# 执行主函数
main "$@"
