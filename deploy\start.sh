#!/bin/bash

# 生产环境启动脚本
echo "🚀 启动 MapleStory 信息站..."

# 检查 Node.js 版本
node_version=$(node -v)
echo "Node.js 版本: $node_version"

# 检查 npm 版本
npm_version=$(npm -v)
echo "npm 版本: $npm_version"

# 检查环境变量
if [ ! -f ".env.local" ]; then
    echo "⚠️  警告: .env.local 文件不存在，请创建并配置环境变量"
    echo "参考 .env.example 文件"
    echo "示例配置:"
    echo "DATABASE_URL=\"postgresql://postgres:password@localhost:5432/mxd_info_db\""
    echo "NEXTAUTH_URL=\"https://your-domain.com\""
    echo "NEXTAUTH_SECRET=\"your-secret-key\""
fi

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 未找到 package.json 文件"
    echo "请确保在正确的项目目录中运行此脚本"
    exit 1
fi

# 安装依赖（如果需要）
if [ ! -d "node_modules" ]; then
    echo "📦 安装生产依赖..."
    npm install --production --silent
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
fi

# 检查 .next 目录是否存在
if [ ! -d ".next" ]; then
    echo "❌ 错误: 未找到 .next 构建目录"
    echo "请确保项目已经构建完成"
    exit 1
fi

# 生成 Prisma 客户端（如果需要）
if [ -d "prisma" ]; then
    echo "🔧 生成 Prisma 客户端..."
    npx prisma generate --silent
fi

# 启动应用
echo "🌟 启动应用..."
echo "应用将在 http://localhost:3000 启动"
echo "按 Ctrl+C 停止应用"
echo ""

# 使用 npm start 而不是直接调用 next
npm start
