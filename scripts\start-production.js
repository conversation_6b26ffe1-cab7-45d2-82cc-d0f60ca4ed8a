#!/usr/bin/env node

/**
 * 生产环境启动脚本
 * 显示环境变量信息后启动 Next.js 应用
 */

const { spawn } = require('child_process')

// 导入启动日志功能
async function loadStartupLogger() {
  try {
    const { logStartupInfo } = await import('../lib/startup-logger.js')
    return logStartupInfo
  } catch (error) {
    console.warn('⚠️ 无法加载启动日志模块:', error.message)
    return null
  }
}

async function startProduction() {
  console.log('🚀 启动生产环境应用...')
  console.log('')
  
  // 显示启动信息
  const logStartupInfo = await loadStartupLogger()
  if (logStartupInfo) {
    logStartupInfo()
  }
  
  console.log('')
  console.log('📦 启动 Next.js 应用...')
  console.log('')
  
  // 启动 Next.js 应用
  const child = spawn('npm', ['start'], {
    stdio: 'inherit',
    env: process.env
  })
  
  child.on('error', (error) => {
    console.error('❌ 启动失败:', error.message)
    process.exit(1)
  })
  
  child.on('exit', (code) => {
    if (code !== 0) {
      console.error(`❌ 应用异常退出，退出码: ${code}`)
    } else {
      console.log('📴 应用已停止')
    }
    process.exit(code)
  })
  
  // 处理进程信号
  process.on('SIGINT', () => {
    console.log('\n📴 收到停止信号，正在关闭应用...')
    child.kill('SIGINT')
  })
  
  process.on('SIGTERM', () => {
    console.log('\n📴 收到终止信号，正在关闭应用...')
    child.kill('SIGTERM')
  })
}

// 如果直接运行此脚本
if (require.main === module) {
  startProduction().catch(error => {
    console.error('❌ 启动脚本错误:', error.message)
    process.exit(1)
  })
}

module.exports = { startProduction }
