
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for components/enhancement-simulator/ItemTooltip.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">components/enhancement-simulator</a> ItemTooltip.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/15</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/48</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/14</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">'use client'
&nbsp;
import { ItemTooltipProps } from '@/types/enhancement'
import {
  getItemLevel,
  getItemTypeName,
  isCashItem,
  isBossReward,
  isOnlyEquip,
  getJobRequirement,
  getStatRequirements
} from <span class="cstat-no" title="statement not covered" >'@/lib/item-filter-utils'</span>
&nbsp;
export default function <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >ItemTooltip({</span></span> item, isVisible, position }: ItemTooltipProps) {
<span class="cstat-no" title="statement not covered" >  if (!isVisible || !item) <span class="cstat-no" title="statement not covered" >return null</span></span>
&nbsp;
  const metadata = <span class="cstat-no" title="statement not covered" >item.detailInfo?.metadata</span>
  const common = <span class="cstat-no" title="statement not covered" >metadata?.common</span>
  const required = <span class="cstat-no" title="statement not covered" >metadata?.required</span>
  const stats = <span class="cstat-no" title="statement not covered" >metadata?.stats</span>
&nbsp;
  const level = <span class="cstat-no" title="statement not covered" >getItemLevel(item)</span>
  const typeName = <span class="cstat-no" title="statement not covered" >getItemTypeName(item)</span>
  const jobRequirement = <span class="cstat-no" title="statement not covered" >getJobRequirement(item)</span>
  const statRequirements = <span class="cstat-no" title="statement not covered" >getStatRequirements(item)</span>
  const cashItem = <span class="cstat-no" title="statement not covered" >isCashItem(item)</span>
  const bossReward = <span class="cstat-no" title="statement not covered" >isBossReward(item)</span>
  const onlyEquip = <span class="cstat-no" title="statement not covered" >isOnlyEquip(item)</span>
&nbsp;
  return (
    &lt;div
      className="fixed bg-black border border-yellow-400/70 rounded-lg p-3 z-[1000] pointer-events-none text-xs"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        width: '280px',
        maxHeight: '400px',
        overflowY: 'auto',
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.8), 0 0 0 1px rgba(255, 255, 255, 0.1)'
      }}
    &gt;
      {/* 装备名称和基本信息 */}
      &lt;div className="mb-3"&gt;
        &lt;div className="text-yellow-400 text-sm font-bold mb-1"&gt;
          {item.name}
        &lt;/div&gt;
        &lt;div className="text-gray-400 text-xs"&gt;
          #{item.itemId} ({cashItem ? '现金道具' : '装备道具'})
        &lt;/div&gt;
        {<span class="branch-0 cbranch-no" title="branch not covered" >bossReward &amp;&amp; (</span>
          &lt;div className="text-red-400 text-xs mt-1"&gt;★ BOSS奖励装备&lt;/div&gt;
        )}
      &lt;/div&gt;
&nbsp;
      {/* 需求信息区域 */}
      &lt;div className="bg-blue-900/20 border border-blue-400/30 rounded p-2 mb-3"&gt;
        &lt;div className="text-blue-300 font-bold mb-1"&gt;装备要求&lt;/div&gt;
        &lt;div className="text-white"&gt;REQ LEV: {level}&lt;/div&gt;
        {(statRequirements.str &gt; 0 || statRequirements.dex &gt; 0 ||
          statRequirements.int &gt; 0 || statRequirements.luk &gt; 0) &amp;&amp; (
          &lt;div className="grid grid-cols-2 gap-1 mt-1 text-xs"&gt;
            {<span class="branch-0 cbranch-no" title="branch not covered" >statRequirements.str &gt; 0 &amp;&amp; &lt;</span>div className="text-red-300"&gt;STR: {statRequirements.str}&lt;/div&gt;}
            {<span class="branch-0 cbranch-no" title="branch not covered" >statRequirements.dex &gt; 0 &amp;&amp; &lt;</span>div className="text-green-300"&gt;DEX: {statRequirements.dex}&lt;/div&gt;}
            {<span class="branch-0 cbranch-no" title="branch not covered" >statRequirements.int &gt; 0 &amp;&amp; &lt;</span>div className="text-blue-300"&gt;INT: {statRequirements.int}&lt;/div&gt;}
            {<span class="branch-0 cbranch-no" title="branch not covered" >statRequirements.luk &gt; 0 &amp;&amp; &lt;</span>div className="text-yellow-300"&gt;LUK: {statRequirements.luk}&lt;/div&gt;}
          &lt;/div&gt;
        )}
        &lt;div className="text-gray-300 text-xs mt-1"&gt;职业: {jobRequirement}&lt;/div&gt;
      &lt;/div&gt;
&nbsp;
      {/* 装备类型 */}
      &lt;div className="mb-3"&gt;
        &lt;div className="text-gray-300"&gt;Equipment Type: {typeName}&lt;/div&gt;
      &lt;/div&gt;
&nbsp;
      {/* 装备属性 */}
      {<span class="branch-0 cbranch-no" title="branch not covered" >stats &amp;&amp; (</span>
        &lt;div className="bg-green-900/20 border border-green-400/30 rounded p-2 mb-3"&gt;
          &lt;div className="text-green-300 font-bold mb-1"&gt;装备属性&lt;/div&gt;
          &lt;div className="grid grid-cols-2 gap-1"&gt;
            {<span class="branch-0 cbranch-no" title="branch not covered" >stats.str &gt; 0 &amp;&amp; &lt;</span>div className="text-red-300"&gt;STR: +{stats.str}&lt;/div&gt;}
            {<span class="branch-0 cbranch-no" title="branch not covered" >stats.dex &gt; 0 &amp;&amp; &lt;</span>div className="text-green-300"&gt;DEX: +{stats.dex}&lt;/div&gt;}
            {<span class="branch-0 cbranch-no" title="branch not covered" >stats.int &gt; 0 &amp;&amp; &lt;</span>div className="text-blue-300"&gt;INT: +{stats.int}&lt;/div&gt;}
            {<span class="branch-0 cbranch-no" title="branch not covered" >stats.luk &gt; 0 &amp;&amp; &lt;</span>div className="text-yellow-300"&gt;LUK: +{stats.luk}&lt;/div&gt;}
            {<span class="branch-0 cbranch-no" title="branch not covered" >stats.maxHp &gt; 0 &amp;&amp; &lt;</span>div className="text-pink-300"&gt;HP: +{stats.maxHp}&lt;/div&gt;}
            {<span class="branch-0 cbranch-no" title="branch not covered" >stats.maxMp &gt; 0 &amp;&amp; &lt;</span>div className="text-cyan-300"&gt;MP: +{stats.maxMp}&lt;/div&gt;}
            {<span class="branch-0 cbranch-no" title="branch not covered" >stats.pad &gt; 0 &amp;&amp; &lt;</span>div className="text-orange-300"&gt;攻击力: +{stats.pad}&lt;/div&gt;}
            {<span class="branch-0 cbranch-no" title="branch not covered" >stats.mad &gt; 0 &amp;&amp; &lt;</span>div className="text-purple-300"&gt;魔力: +{stats.mad}&lt;/div&gt;}
            {<span class="branch-0 cbranch-no" title="branch not covered" >stats.pdd &gt; 0 &amp;&amp; &lt;</span>div className="text-gray-300"&gt;物防: +{stats.pdd}&lt;/div&gt;}
            {<span class="branch-0 cbranch-no" title="branch not covered" >stats.mdd &gt; 0 &amp;&amp; &lt;</span>div className="text-gray-300"&gt;魔防: +{stats.mdd}&lt;/div&gt;}
            {<span class="branch-0 cbranch-no" title="branch not covered" >stats.acc &gt; 0 &amp;&amp; &lt;</span>div className="text-white"&gt;命中: +{stats.acc}&lt;/div&gt;}
            {<span class="branch-0 cbranch-no" title="branch not covered" >stats.eva &gt; 0 &amp;&amp; &lt;</span>div className="text-white"&gt;回避: +{stats.eva}&lt;/div&gt;}
            {<span class="branch-0 cbranch-no" title="branch not covered" >stats.speed &gt; 0 &amp;&amp; &lt;</span>div className="text-white"&gt;移动: +{stats.speed}&lt;/div&gt;}
            {<span class="branch-0 cbranch-no" title="branch not covered" >stats.jump &gt; 0 &amp;&amp; &lt;</span>div className="text-white"&gt;跳跃: +{stats.jump}&lt;/div&gt;}
          &lt;/div&gt;
        &lt;/div&gt;
      )}
&nbsp;
      {/* 功能支持 */}
      &lt;div className="bg-purple-900/20 border border-purple-400/30 rounded p-2 mb-3"&gt;
        &lt;div className="text-purple-300 font-bold mb-1"&gt;强化支持&lt;/div&gt;
        &lt;div className="space-y-1"&gt;
          &lt;div className={`flex items-center ${
            common?.enableStarforce ? 'text-green-400' : 'text-red-400'
          }`}&gt;
            &lt;span className="mr-2 w-3"&gt;
              {common?.enableStarforce ? '✓' : '✗'}
            &lt;/span&gt;
            星力强化 {common?.maxStarforce ? `(最大${common.maxStarforce}星)` : ''}
          &lt;/div&gt;
&nbsp;
          &lt;div className={`flex items-center ${
            !common?.blockUpgradePotential ? 'text-green-400' : 'text-red-400'
          }`}&gt;
            &lt;span className="mr-2 w-3"&gt;
              {!common?.blockUpgradePotential ? '✓' : '✗'}
            &lt;/span&gt;
            潜能重设
          &lt;/div&gt;
&nbsp;
          &lt;div className={`flex items-center ${
            !common?.blockUpgradeExtraOption ? 'text-green-400' : 'text-red-400'
          }`}&gt;
            &lt;span className="mr-2 w-3"&gt;
              {!common?.blockUpgradeExtraOption ? '✓' : '✗'}
            &lt;/span&gt;
            额外属性
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
&nbsp;
      {/* 特殊标记 */}
      &lt;div className="flex flex-wrap gap-1"&gt;
        {<span class="branch-0 cbranch-no" title="branch not covered" >cashItem &amp;&amp; (</span>
          &lt;span className="px-2 py-1 bg-purple-600/30 text-purple-300 rounded text-xs"&gt;
            💎 现金道具
          &lt;/span&gt;
        )}
        {<span class="branch-0 cbranch-no" title="branch not covered" >bossReward &amp;&amp; (</span>
          &lt;span className="px-2 py-1 bg-red-600/30 text-red-300 rounded text-xs"&gt;
            👑 BOSS奖励
          &lt;/span&gt;
        )}
        {<span class="branch-0 cbranch-no" title="branch not covered" >onlyEquip &amp;&amp; (</span>
          &lt;span className="px-2 py-1 bg-orange-600/30 text-orange-300 rounded text-xs"&gt;
            ⚡ 唯一装备
          &lt;/span&gt;
        )}
        {<span class="branch-0 cbranch-no" title="branch not covered" >common?.isMintable &amp;&amp; (</span>
          &lt;span className="px-2 py-1 bg-green-600/30 text-green-300 rounded text-xs"&gt;
            🔨 可铸造
          &lt;/span&gt;
        )}
      &lt;/div&gt;
    &lt;/div&gt;
  )
}
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-23T10:34:42.331Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    