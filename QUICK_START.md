# 🚀 冒险岛情报站 - 快速开始指南

## 📋 概述

本指南将帮助您快速部署冒险岛情报站项目。根据您的需求选择合适的部署方式：

- **开发环境**: 本地开发和测试
- **Vercel部署**: 快速云端部署
- **自建服务器**: 完全控制的生产环境

## 🛠️ 开发环境快速启动

### 1. 环境要求
```bash
Node.js >= 18.0.0
PostgreSQL >= 14.0
Redis >= 6.0 (可选)
```

### 2. 快速安装
```bash
# 克隆项目
git clone <your-repository-url>
cd maplestory-info-station

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env.local
# 编辑 .env.local 文件，配置数据库连接

# 初始化数据库
npx prisma generate
npx prisma db push
npx prisma db seed

# 启动开发服务器
npm run dev
```

### 3. 访问应用
- 主页: http://localhost:3000
- 登录: http://localhost:3000/login
- 注册: http://localhost:3000/register
- 调试: http://localhost:3000/debug

### 4. 测试账户
```
管理员账户:
邮箱: <EMAIL>
密码: admin123456
```

## ☁️ Vercel 快速部署

### 1. 准备工作
```bash
# 确保代码已推送到 Git 仓库
git add .
git commit -m "准备部署"
git push origin main
```

### 2. 数据库准备
推荐使用 [Neon](https://neon.tech) 或 [Supabase](https://supabase.com)：

```bash
# 获取数据库连接字符串
# 格式: ****************************************/dbname?sslmode=require
```

### 3. Vercel 部署
1. 访问 [vercel.com](https://vercel.com)
2. 导入 Git 仓库
3. 配置环境变量（见下方配置）
4. 点击部署

### 4. 环境变量配置
```env
DATABASE_URL=****************************************/dbname?sslmode=require
NEXTAUTH_URL=https://your-domain.vercel.app
NEXTAUTH_SECRET=your-32-character-secret
RESEND_API_KEY=re_your-api-key
EMAIL_FROM=<EMAIL>
```

## 🖥️ 自建服务器快速部署

### 1. 服务器初始化（Ubuntu）
```bash
# 下载并运行初始化脚本
wget https://raw.githubusercontent.com/your-repo/scripts/setup-server.sh
chmod +x setup-server.sh

# 运行初始化（替换为您的域名和邮箱）
sudo ./setup-server.sh -d your-domain.com -e <EMAIL>
```

### 2. 部署应用
```bash
# 切换到应用用户
sudo su - maplestory

# 克隆代码
git clone <your-repository-url> app
cd app

# 配置环境变量
cp .env.production.template .env.production
nano .env.production  # 编辑配置

# 运行部署脚本
chmod +x scripts/deploy.sh
./scripts/deploy.sh production
```

### 3. 验证部署
```bash
# 检查应用状态
pm2 status

# 查看日志
pm2 logs

# 健康检查
curl http://localhost:3000/health
```

## 🔧 常见问题解决

### 数据库连接失败
```bash
# 检查 PostgreSQL 状态
sudo systemctl status postgresql

# 测试连接
psql -h localhost -U postgres -d mxd_info_db
```

### 应用启动失败
```bash
# 查看详细日志
pm2 logs maplestory-info-station

# 重启应用
pm2 restart maplestory-info-station
```

### 端口被占用
```bash
# 查看端口占用
sudo lsof -i :3000

# 杀死进程
sudo kill -9 <PID>
```

## 📊 监控和维护

### 健康检查
```bash
# 运行健康检查脚本
./scripts/health-check.sh

# 设置定时检查
crontab -e
# 添加: */5 * * * * /home/<USER>/app/scripts/health-check.sh
```

### 日志查看
```bash
# 应用日志
pm2 logs

# 系统日志
sudo journalctl -u nginx -f

# 数据库日志
sudo tail -f /var/log/postgresql/postgresql-14-main.log
```

### 备份
```bash
# 手动备份
./scripts/backup.sh

# 自动备份（每日凌晨2点）
crontab -e
# 添加: 0 2 * * * /home/<USER>/app/scripts/backup.sh
```

## 🔒 安全配置

### SSL 证书
```bash
# 检查证书状态
sudo certbot certificates

# 手动续期
sudo certbot renew
```

### 防火墙
```bash
# 检查防火墙状态
sudo ufw status

# 允许必要端口
sudo ufw allow 80
sudo ufw allow 443
```

### 更新依赖
```bash
# 检查安全漏洞
npm audit

# 修复漏洞
npm audit fix
```

## 📈 性能优化

### 数据库优化
```sql
-- 查看慢查询
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- 分析表
ANALYZE;
```

### 缓存配置
```bash
# Redis 内存优化
sudo nano /etc/redis/redis.conf
# 设置: maxmemory 512mb
# 设置: maxmemory-policy allkeys-lru
```

### Nginx 优化
```nginx
# 启用 Gzip 压缩
gzip on;
gzip_types text/plain text/css application/json application/javascript;

# 设置缓存
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## 🆘 获取帮助

### 文档资源
- [完整部署指南](./DEPLOYMENT_GUIDE.md)
- [API 文档](./API_DOCS.md)
- [故障排查](./TROUBLESHOOTING.md)

### 联系支持
- 邮箱: <EMAIL>
- GitHub Issues: [项目地址]/issues

### 社区
- Discord: [邀请链接]
- QQ群: [群号]

---

## 📝 下一步

部署完成后，您可以：

1. **配置邮件服务**: 设置 Resend API 密钥启用邮件功能
2. **自定义配置**: 修改虚拟货币设置和功能开关
3. **添加内容**: 上传装备数据和游戏资源
4. **监控设置**: 配置告警和监控系统
5. **备份策略**: 设置自动备份和恢复流程

祝您使用愉快！🎉
