#!/usr/bin/env node

/**
 * 生产环境部署文件准备脚本
 * 将所有必要的文件复制到 deploy 目录
 */

const fs = require('fs')
const path = require('path')

// 源目录和目标目录
const sourceDir = process.cwd()
const deployDir = path.join(sourceDir, 'deploy')

// 需要复制的文件和目录
const filesToCopy = [
  // 构建输出
  '.next',
  
  // 静态资源
  'public',
  
  // 配置文件
  'package.json',
  'package-lock.json',
  'pnpm-lock.yaml',
  'next.config.mjs',
  
  // 数据库相关
  'prisma',
  
  // 文档和数据
  'docs',
  
  // 环境变量模板
  '.env.example',
  
  // 其他必要文件
  'README.md'
]

// 不需要复制的文件和目录
const excludePatterns = [
  'node_modules',
  '.git',
  '.vscode',
  '__tests__',
  'coverage',
  '.env.local',
  '.env',
  'deploy',
  'scripts',
  '*.log',
  '.DS_Store',
  'Thumbs.db'
]

/**
 * 递归复制目录
 */
function copyDirectory(src, dest) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true })
  }

  const entries = fs.readdirSync(src, { withFileTypes: true })

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name)
    const destPath = path.join(dest, entry.name)

    // 检查是否应该排除
    if (shouldExclude(entry.name, srcPath)) {
      continue
    }

    if (entry.isDirectory()) {
      copyDirectory(srcPath, destPath)
    } else {
      fs.copyFileSync(srcPath, destPath)
    }
  }
}

/**
 * 复制单个文件
 */
function copyFile(src, dest) {
  const destDir = path.dirname(dest)
  if (!fs.existsSync(destDir)) {
    fs.mkdirSync(destDir, { recursive: true })
  }
  fs.copyFileSync(src, dest)
}

/**
 * 检查是否应该排除文件/目录
 */
function shouldExclude(name, fullPath) {
  return excludePatterns.some(pattern => {
    if (pattern.includes('*')) {
      const regex = new RegExp(pattern.replace(/\*/g, '.*'))
      return regex.test(name)
    }
    return name === pattern || fullPath.includes(pattern)
  })
}

/**
 * 创建生产环境配置文件
 */
function createProductionFiles() {
  // 创建生产环境启动脚本
  const startScript = `#!/bin/bash

# 生产环境启动脚本
echo "🚀 启动 MapleStory 信息站..."

# 检查 Node.js 版本
node_version=$(node -v)
echo "Node.js 版本: $node_version"

# 检查环境变量
if [ ! -f ".env.local" ]; then
    echo "⚠️  警告: .env.local 文件不存在，请创建并配置环境变量"
    echo "参考 .env.example 文件"
fi

# 安装依赖（如果需要）
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install --production
fi

# 启动应用
echo "🌟 启动应用..."
npm start
`

  fs.writeFileSync(path.join(deployDir, 'start.sh'), startScript)
  fs.chmodSync(path.join(deployDir, 'start.sh'), '755')

  // 创建 Windows 启动脚本
  const startBat = `@echo off
echo 🚀 启动 MapleStory 信息站...

REM 检查 Node.js 版本
node -v

REM 检查环境变量
if not exist ".env.local" (
    echo ⚠️  警告: .env.local 文件不存在，请创建并配置环境变量
    echo 参考 .env.example 文件
)

REM 安装依赖（如果需要）
if not exist "node_modules" (
    echo 📦 安装依赖...
    npm install --production
)

REM 启动应用
echo 🌟 启动应用...
npm start
`

  fs.writeFileSync(path.join(deployDir, 'start.bat'), startBat)

  // 创建部署说明文件
  const deployReadme = `# 生产环境部署说明

## 📁 目录结构

\`\`\`
deploy/
├── .next/              # 构建输出（必需）
├── public/             # 静态资源（必需）
├── prisma/             # 数据库配置（必需）
├── docs/               # 数据文件（必需）
├── package.json        # 依赖配置（必需）
├── next.config.mjs     # Next.js配置（必需）
├── start.sh           # Linux/Mac 启动脚本
├── start.bat          # Windows 启动脚本
└── README-DEPLOY.md   # 本文件
\`\`\`

## 🚀 部署步骤

### 1. 环境要求

- Node.js 18.17+ 
- npm 或 pnpm
- PostgreSQL 数据库
- Redis（可选，用于缓存）

### 2. 配置环境变量

创建 \`.env.local\` 文件：

\`\`\`bash
# 数据库配置
DATABASE_URL="postgresql://postgres:postgres@localhost:5433/mxd_info_db"

# NextAuth 配置
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-secret-key"

# 邮件配置（可选）
SMTP_HOST="your-smtp-host"
SMTP_PORT="587"
SMTP_USER="your-email"
SMTP_PASS="your-password"

# Redis 配置（可选）
REDIS_URL="redis://localhost:6379"
\`\`\`

### 3. 安装依赖

\`\`\`bash
npm install --production
\`\`\`

### 4. 数据库设置

\`\`\`bash
# 生成 Prisma 客户端
npx prisma generate

# 运行数据库迁移
npx prisma migrate deploy
\`\`\`

### 5. 启动应用

**Linux/Mac:**
\`\`\`bash
chmod +x start.sh
./start.sh
\`\`\`

**Windows:**
\`\`\`cmd
start.bat
\`\`\`

**手动启动:**
\`\`\`bash
npm start
\`\`\`

## 🔧 生产环境配置

### Nginx 配置示例

\`\`\`nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # 静态文件缓存
    location /_next/static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        proxy_pass http://localhost:3000;
    }
}
\`\`\`

### PM2 配置

\`\`\`json
{
  "name": "maplestory-info-station",
  "script": "npm",
  "args": "start",
  "instances": "max",
  "exec_mode": "cluster",
  "env": {
    "NODE_ENV": "production",
    "PORT": "3000"
  }
}
\`\`\`

## 📊 监控和日志

- 应用日志: \`pm2 logs\`
- 性能监控: \`pm2 monit\`
- 重启应用: \`pm2 restart maplestory-info-station\`

## 🔒 安全建议

1. 使用强密码和密钥
2. 配置防火墙规则
3. 启用 HTTPS
4. 定期更新依赖
5. 监控应用性能和错误

## 📞 支持

如有问题，请检查：
1. Node.js 版本是否正确
2. 环境变量是否配置完整
3. 数据库连接是否正常
4. 端口是否被占用
`

  fs.writeFileSync(path.join(deployDir, 'README-DEPLOY.md'), deployReadme)
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始准备生产环境部署文件...')

  // 清理目标目录
  if (fs.existsSync(deployDir)) {
    console.log('🧹 清理现有部署目录...')
    fs.rmSync(deployDir, { recursive: true, force: true })
  }

  // 创建部署目录
  fs.mkdirSync(deployDir, { recursive: true })

  // 复制文件
  console.log('📁 复制必要文件...')
  
  for (const item of filesToCopy) {
    const srcPath = path.join(sourceDir, item)
    const destPath = path.join(deployDir, item)

    if (fs.existsSync(srcPath)) {
      const stat = fs.statSync(srcPath)
      
      if (stat.isDirectory()) {
        console.log(`📂 复制目录: ${item}`)
        copyDirectory(srcPath, destPath)
      } else {
        console.log(`📄 复制文件: ${item}`)
        copyFile(srcPath, destPath)
      }
    } else {
      console.log(`⚠️  跳过不存在的文件: ${item}`)
    }
  }

  // 创建生产环境配置文件
  console.log('⚙️  创建生产环境配置文件...')
  createProductionFiles()

  console.log('✅ 部署文件准备完成!')
  console.log(`📁 部署目录: ${deployDir}`)
  console.log('📖 请查看 deploy/README-DEPLOY.md 了解部署说明')
}

// 运行脚本
if (require.main === module) {
  main()
}

module.exports = { main }
