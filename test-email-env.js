#!/usr/bin/env node

/**
 * 测试邮件环境变量配置
 * 验证邮件功能中使用的 URL 是否正确
 */

// 模拟邮件模块的环境变量获取逻辑
function getAppUrl() {
  return process.env.NEXTAUTH_URL || 
         process.env.NEXT_PUBLIC_APP_URL || 
         process.env.APP_URL ||
         'http://localhost:3000'
}

function testEmailEnvironment() {
  console.log('🧪 测试邮件环境变量配置')
  console.log('='.repeat(50))
  console.log('')
  
  // 显示所有相关环境变量
  console.log('📋 环境变量状态:')
  console.log(`   NODE_ENV: ${process.env.NODE_ENV || '未设置'}`)
  console.log(`   NEXTAUTH_URL: ${process.env.NEXTAUTH_URL || '未设置'}`)
  console.log(`   NEXT_PUBLIC_APP_URL: ${process.env.NEXT_PUBLIC_APP_URL || '未设置'}`)
  console.log(`   APP_URL: ${process.env.APP_URL || '未设置'}`)
  console.log('')
  
  // 测试 URL 获取逻辑
  const appUrl = getAppUrl()
  console.log('🔗 URL 获取测试:')
  console.log(`   最终使用的 APP_URL: ${appUrl}`)
  
  // 验证 URL 是否正确
  const expectedUrl = 'https://mxd.hyhuman.xyz'
  const isCorrect = appUrl === expectedUrl
  
  console.log(`   期望的 URL: ${expectedUrl}`)
  console.log(`   配置正确: ${isCorrect ? '✅ 是' : '❌ 否'}`)
  console.log('')
  
  // 模拟邮件链接生成
  const testEmail = '<EMAIL>'
  const testToken = 'abc123def456'
  const verificationUrl = `${appUrl}/verify-email?token=${testToken}&email=${encodeURIComponent(testEmail)}`
  
  console.log('📧 邮件链接测试:')
  console.log(`   验证链接: ${verificationUrl}`)
  console.log(`   使用 HTTPS: ${verificationUrl.startsWith('https://') ? '✅ 是' : '❌ 否'}`)
  console.log(`   域名正确: ${verificationUrl.includes('mxd.hyhuman.xyz') ? '✅ 是' : '❌ 否'}`)
  console.log('')
  
  // 检查环境文件
  const fs = require('fs')
  const envFiles = ['.env.local', '.env.production', '.env']
  
  console.log('📄 环境文件检查:')
  envFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`   ${file}: ✅ 存在`)
      
      const content = fs.readFileSync(file, 'utf8')
      const hasNextAuthUrl = content.includes('NEXTAUTH_URL')
      const hasPublicAppUrl = content.includes('NEXT_PUBLIC_APP_URL')
      const hasAppUrl = content.includes('APP_URL')
      
      console.log(`     - NEXTAUTH_URL: ${hasNextAuthUrl ? '✅' : '❌'}`)
      console.log(`     - NEXT_PUBLIC_APP_URL: ${hasPublicAppUrl ? '✅' : '❌'}`)
      console.log(`     - APP_URL: ${hasAppUrl ? '✅' : '❌'}`)
    } else {
      console.log(`   ${file}: ❌ 不存在`)
    }
  })
  console.log('')
  
  // 生成修复建议
  if (!isCorrect) {
    console.log('🔧 修复建议:')
    console.log('')
    
    if (!process.env.NEXTAUTH_URL) {
      console.log('1. 在 .env.local 中添加:')
      console.log('   NEXTAUTH_URL="https://mxd.hyhuman.xyz"')
      console.log('')
    }
    
    if (!process.env.APP_URL) {
      console.log('2. 在 .env.local 中添加:')
      console.log('   APP_URL="https://mxd.hyhuman.xyz"')
      console.log('')
    }
    
    console.log('3. 运行修复脚本:')
    console.log('   chmod +x fix-email-url.sh')
    console.log('   ./fix-email-url.sh')
    console.log('')
    
    console.log('4. 重启应用:')
    console.log('   pm2 restart maplestory-info-station')
    console.log('   # 或')
    console.log('   pkill -f "next start" && npm start')
    console.log('')
  } else {
    console.log('✅ 配置正确！邮件中的链接应该使用正确的 HTTPS 域名。')
    console.log('')
  }
  
  // 测试邮件模块导入
  try {
    console.log('📦 测试邮件模块:')
    
    // 动态导入邮件模块（如果存在）
    const path = require('path')
    const emailPath = path.join(process.cwd(), 'lib', 'email.ts')
    
    if (fs.existsSync(emailPath)) {
      console.log('   邮件模块文件: ✅ 存在')
      
      // 检查文件内容
      const emailContent = fs.readFileSync(emailPath, 'utf8')
      const hasGetAppUrl = emailContent.includes('getAppUrl')
      const hasAppUrlVar = emailContent.includes('APP_URL')
      
      console.log(`   包含 getAppUrl 函数: ${hasGetAppUrl ? '✅' : '❌'}`)
      console.log(`   包含 APP_URL 变量: ${hasAppUrlVar ? '✅' : '❌'}`)
    } else {
      console.log('   邮件模块文件: ❌ 不存在')
    }
  } catch (error) {
    console.log(`   邮件模块测试失败: ${error.message}`)
  }
  
  console.log('')
  console.log('🎯 总结:')
  if (isCorrect) {
    console.log('✅ 环境变量配置正确，邮件链接应该使用正确的域名')
  } else {
    console.log('❌ 环境变量配置有问题，需要修复')
  }
  
  console.log('')
  console.log('📞 如需进一步调试:')
  console.log('1. 注册测试用户并查看邮件')
  console.log('2. 检查应用日志中的环境变量调试信息')
  console.log('3. 运行: node test-email-env.js')
}

// 如果直接运行此脚本
if (require.main === module) {
  testEmailEnvironment()
}

module.exports = { testEmailEnvironment, getAppUrl }
