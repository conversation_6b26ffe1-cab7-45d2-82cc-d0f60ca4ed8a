// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户基础表
model User {
  id                String   @id @default(cuid())
  email             String   @unique
  username          String?  @unique  // 新增：用户名字段
  emailVerified     DateTime?
  hashedPassword    String?
  name              String?
  avatar            String?
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  lastLoginAt       DateTime?
  lastLogoutAt      DateTime?  // 新增：最后登出时间

  // 关联关系
  accounts          Account[]
  sessions          Session[]
  userRoles         UserRole[]
  currencyBalance   CurrencyBalance?
  transactions      Transaction[]
  deviceFingerprints DeviceFingerprint[]
  enhancementLogs   EnhancementLog[]

  @@map("users")
}

// OAuth账户表
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([provider, providerAccountId])
  @@map("accounts")
}

// 会话表
model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("sessions")
}

// 验证令牌表
model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// 角色表
model Role {
  id          String   @id @default(cuid())
  name        String   @unique // guest, registered, vip, diamond, admin
  displayName String
  description String?
  permissions Json     // 权限列表
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  userRoles UserRole[]
  
  @@map("roles")
}

// 用户角色关联表
model UserRole {
  id        String    @id @default(cuid())
  userId    String
  roleId    String
  grantedAt DateTime  @default(now())
  expiresAt DateTime? // VIP过期时间
  grantedBy String?   // 授权人
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)
  
  @@unique([userId, roleId])
  @@map("user_roles")
}

// 虚拟货币余额表
model CurrencyBalance {
  id            String   @id @default(cuid())
  userId        String   @unique
  balance       Decimal  @default(0) @db.Decimal(10, 2)
  frozenBalance Decimal  @default(0) @db.Decimal(10, 2) // 冻结余额
  totalEarned   Decimal  @default(0) @db.Decimal(10, 2) // 总收入
  totalSpent    Decimal  @default(0) @db.Decimal(10, 2) // 总支出
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("currency_balances")
}

// 交易记录表
model Transaction {
  id            String            @id @default(cuid())
  userId        String
  type          TransactionType   // RECHARGE, CONSUME, REWARD, REFUND
  amount        Decimal           @db.Decimal(10, 2)
  balanceBefore Decimal           @db.Decimal(10, 2)
  balanceAfter  Decimal           @db.Decimal(10, 2)
  description   String
  metadata      Json?             // 额外信息
  status        TransactionStatus @default(PENDING)
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("transactions")
}

// 设备指纹表
model DeviceFingerprint {
  id          String   @id @default(cuid())
  userId      String?  // 可为空，游客用户
  fingerprint String   @unique
  userAgent   String
  ipAddress   String
  metadata    Json     // FingerprintJS返回的详细信息
  firstSeen   DateTime @default(now())
  lastSeen    DateTime @default(now())
  visitCount  Int      @default(1)
  
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)
  
  @@map("device_fingerprints")
}

// 装备强化日志表
model EnhancementLog {
  id             String          @id @default(cuid())
  userId         String
  enhancementType EnhancementType // STARFORCE, POTENTIAL, ADDITIONAL
  itemId         String
  itemName       String
  fromLevel      Int
  toLevel        Int
  success        Boolean
  cost           Decimal         @db.Decimal(10, 2)
  metadata       Json?           // 强化详细信息
  createdAt      DateTime        @default(now())
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("enhancement_logs")
}

// 系统配置表
model SystemConfig {
  id        String   @id @default(cuid())
  key       String   @unique
  value     Json
  category  String   // CURRENCY, ENHANCEMENT, SYSTEM
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("system_configs")
}

// 枚举定义
enum TransactionType {
  RECHARGE    // 充值
  CONSUME     // 消费
  REWARD      // 奖励
  REFUND      // 退款
  TRANSFER    // 转账
}

enum TransactionStatus {
  PENDING     // 待处理
  COMPLETED   // 已完成
  FAILED      // 失败
  CANCELLED   // 已取消
}

enum EnhancementType {
  STARFORCE   // 星之力强化
  POTENTIAL   // 潜能重设
  ADDITIONAL  // 附加属性
}
