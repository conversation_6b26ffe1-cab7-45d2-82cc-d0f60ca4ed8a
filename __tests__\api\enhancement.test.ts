/**
 * 装备强化API单元测试
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals'

// Mock UUID
jest.mock('uuid', () => ({
  v4: () => 'test-uuid-123'
}))

// Mock 星力算法模块以避免文件系统依赖
jest.mock('@/lib/starforce-algorithm', () => ({
  calculateStarforceResult: jest.fn(() => ({
    result: 'SUCCESS',
    newLevel: 11,
    previousLevel: 10,
    probabilities: { success: 0.5, failHold: 0.3, failDrop: 0.1, boom: 0.1 },
    randomValue: 0.3
  })),
  StarforceResult: {
    SUCCESS: 'SUCCESS',
    FAIL_HOLD: 'FAIL_HOLD',
    FAIL_DROP: 'FAIL_DROP',
    BOOM: 'BOOM'
  },
  getStarforceInfo: jest.fn(() => ({
    level: 10,
    baseProbability: { success: 0.5, failHold: 0.3, failDrop: 0.1, boom: 0.1 },
    finalProbability: { success: 0.5, failHold: 0.3, failDrop: 0.1, boom: 0.1 },
    options: { starcatchEnabled: false, preventEnabled: false },
    effects: ['无特殊效果']
  }))
}))

// Mock 星力数据加载器
jest.mock('@/lib/starforce-data-loader', () => ({
  exportProbabilityDataAsJSON: jest.fn(() => ({
    0: { success: 0.95, failHold: 0.05, failDrop: 0, boom: 0 },
    10: { success: 0.5, failHold: 0.5, failDrop: 0, boom: 0 },
    15: { success: 0.3, failHold: 0.679, failDrop: 0, boom: 0.021 },
    20: { success: 0.3, failHold: 0.63, failDrop: 0, boom: 0.07 },
    24: { success: 0.01, failHold: 0, failDrop: 0.594, boom: 0.396 }
  }))
}))

describe('Enhancement API Logic Tests', () => {
  describe('星力强化逻辑测试', () => {
    it('应该正确调用星力强化算法', () => {
      const { calculateStarforceResult } = require('@/lib/starforce-algorithm')

      // 验证mock被正确调用
      expect(calculateStarforceResult).toBeDefined()

      // 调用函数并验证返回值
      const result = calculateStarforceResult(10, false, false, {})
      expect(result).toHaveProperty('result')
      expect(result).toHaveProperty('newLevel')
      expect(result).toHaveProperty('previousLevel')
      expect(result.result).toBe('SUCCESS')
      expect(result.newLevel).toBe(11)
      expect(result.previousLevel).toBe(10)
    })

    it('应该正确调用星力信息获取', () => {
      const { getStarforceInfo } = require('@/lib/starforce-algorithm')

      // 验证mock被正确调用
      expect(getStarforceInfo).toBeDefined()

      // 调用函数并验证返回值
      const info = getStarforceInfo(10, { starcatchEnabled: false, preventEnabled: false }, {})
      expect(info).toHaveProperty('level')
      expect(info).toHaveProperty('baseProbability')
      expect(info).toHaveProperty('finalProbability')
      expect(info).toHaveProperty('effects')
      expect(info.level).toBe(10)
    })

    it('应该正确调用概率数据导出', () => {
      const { exportProbabilityDataAsJSON } = require('@/lib/starforce-data-loader')

      // 验证mock被正确调用
      expect(exportProbabilityDataAsJSON).toBeDefined()

      // 调用函数并验证返回值
      const data = exportProbabilityDataAsJSON()
      expect(data).toHaveProperty('0')
      expect(data).toHaveProperty('10')
      expect(data[0]).toHaveProperty('success')
      expect(data[0]).toHaveProperty('failHold')
    })
  })

  describe('UUID生成测试', () => {
    it('应该生成测试UUID', () => {
      const { v4 } = require('uuid')

      // 验证mock被正确调用
      expect(v4).toBeDefined()

      // 调用函数并验证返回值
      const uuid = v4()
      expect(uuid).toBe('test-uuid-123')
    })
  })
})
