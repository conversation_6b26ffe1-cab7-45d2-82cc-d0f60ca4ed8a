/**
 * 装备强化API单元测试
 */

import { NextRequest } from 'next/server'
import { POST, GET } from '@/app/api/enhancement/enhance/route'

// Mock UUID
jest.mock('uuid', () => ({
  v4: () => 'test-uuid-123'
}))

// Mock 星力算法模块以避免文件系统依赖
jest.mock('@/lib/starforce-algorithm', () => ({
  calculateStarforceResult: jest.fn(() => ({
    result: 'SUCCESS',
    newLevel: 11,
    previousLevel: 10,
    probabilities: { success: 0.5, failHold: 0.3, failDrop: 0.1, boom: 0.1 },
    randomValue: 0.3
  })),
  StarforceResult: {
    SUCCESS: 'SUCCESS',
    FAIL_HOLD: 'FAIL_HOLD',
    FAIL_DROP: 'FAIL_DROP',
    BOOM: 'BOOM'
  },
  getStarforceInfo: jest.fn(() => ({
    level: 10,
    baseProbability: { success: 0.5, failHold: 0.3, failDrop: 0.1, boom: 0.1 },
    finalProbability: { success: 0.5, failHold: 0.3, failDrop: 0.1, boom: 0.1 },
    options: { starcatchEnabled: false, preventEnabled: false },
    effects: ['无特殊效果']
  })),
  validateProbabilityData: jest.fn(() => ({
    isValid: true,
    errors: [],
    warnings: []
  }))
}))

describe('/api/enhancement/enhance', () => {
  describe('POST', () => {
    it('应该成功处理有效的星力强化请求', async () => {
      const request = new NextRequest('http://localhost:3000/api/enhancement/enhance', {
        method: 'POST',
        body: JSON.stringify({
          equipmentId: 'weapon_001',
          currentLevel: 10,
          enhancementType: 'starforce',
          starcatchEnabled: false,
          preventEnabled: true,
          minigameBonus: 5
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.result).toBeDefined()
      expect(data.result.type).toMatch(/^(success|failed|major_failure)$/)
      expect(data.result.newLevel).toBeGreaterThanOrEqual(0)
      expect(data.result.previousLevel).toBe(10)
      expect(data.cost).toBeDefined()
      expect(data.cost.mesos).toBeGreaterThan(0)
      expect(data.probability).toBeDefined()
      expect(data.requestId).toBe('test-uuid-123')
      expect(data.timestamp).toBeDefined()
    })

    it('应该成功处理潜能重设请求', async () => {
      const request = new NextRequest('http://localhost:3000/api/enhancement/enhance', {
        method: 'POST',
        body: JSON.stringify({
          equipmentId: 'armor_001',
          currentLevel: 0,
          enhancementType: 'potential'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.result.type).toBe('success') // 潜能总是成功
      expect(data.result.newLevel).toBe(0) // 潜能不改变等级
      expect(data.cost.mesos).toBe(500000) // 潜能固定费用
    })

    it('应该拒绝无效的装备ID', async () => {
      const request = new NextRequest('http://localhost:3000/api/enhancement/enhance', {
        method: 'POST',
        body: JSON.stringify({
          equipmentId: '',
          currentLevel: 10,
          enhancementType: 'starforce'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.errorCode).toBe('VALIDATION_ERROR')
      expect(data.error).toContain('验证失败')
    })

    it('应该拒绝无效的强化等级', async () => {
      const request = new NextRequest('http://localhost:3000/api/enhancement/enhance', {
        method: 'POST',
        body: JSON.stringify({
          equipmentId: 'weapon_001',
          currentLevel: -1,
          enhancementType: 'starforce'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.errorCode).toBe('VALIDATION_ERROR')
    })

    it('应该拒绝超过最大等级的强化请求', async () => {
      const request = new NextRequest('http://localhost:3000/api/enhancement/enhance', {
        method: 'POST',
        body: JSON.stringify({
          equipmentId: 'weapon_001',
          currentLevel: 25,
          enhancementType: 'starforce'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.errorCode).toBe('MAX_LEVEL_REACHED')
    })

    it('应该拒绝无效的强化类型', async () => {
      const request = new NextRequest('http://localhost:3000/api/enhancement/enhance', {
        method: 'POST',
        body: JSON.stringify({
          equipmentId: 'weapon_001',
          currentLevel: 10,
          enhancementType: 'invalid_type'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.errorCode).toBe('VALIDATION_ERROR')
    })

    it('应该验证API密钥', async () => {
      const request = new NextRequest('http://localhost:3000/api/enhancement/enhance', {
        method: 'POST',
        body: JSON.stringify({
          equipmentId: 'weapon_001',
          currentLevel: 10,
          enhancementType: 'starforce'
        }),
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': 'invalid-key'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.errorCode).toBe('INVALID_API_KEY')
    })

    it('应该接受有效的API密钥', async () => {
      const request = new NextRequest('http://localhost:3000/api/enhancement/enhance', {
        method: 'POST',
        body: JSON.stringify({
          equipmentId: 'weapon_001',
          currentLevel: 10,
          enhancementType: 'starforce'
        }),
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': 'demo-key-123'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
    })

    it('应该处理防止破坏选项', async () => {
      const request = new NextRequest('http://localhost:3000/api/enhancement/enhance', {
        method: 'POST',
        body: JSON.stringify({
          equipmentId: 'weapon_001',
          currentLevel: 15,
          enhancementType: 'starforce',
          preventEnabled: true
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      // 防止破坏启用时，不应该出现装备损坏
      if (data.result.type === 'major_failure') {
        expect(data.result.message).toContain('防止破坏生效')
      }
    })

    it('应该处理迷你游戏加成', async () => {
      const request = new NextRequest('http://localhost:3000/api/enhancement/enhance', {
        method: 'POST',
        body: JSON.stringify({
          equipmentId: 'weapon_001',
          currentLevel: 10,
          enhancementType: 'starforce',
          minigameBonus: 10
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.probability.success).toBe(90) // 80 + 10 加成
    })

    it('应该处理JSON解析错误', async () => {
      const request = new NextRequest('http://localhost:3000/api/enhancement/enhance', {
        method: 'POST',
        body: 'invalid json',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.errorCode).toBe('INTERNAL_ERROR')
    })
  })

  describe('GET', () => {
    it('应该返回API文档信息', async () => {
      const response = await GET()
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.name).toBe('Enhancement API')
      expect(data.version).toBe('1.0.0')
      expect(data.description).toBeDefined()
      expect(data.endpoints).toBeDefined()
      expect(data.endpoints.enhance).toBeDefined()
      expect(data.examples).toBeDefined()
    })
  })
})

describe('强化逻辑测试', () => {
  it('应该根据概率正确计算强化结果', async () => {
    const results = []
    
    // 执行多次强化来测试概率分布
    for (let i = 0; i < 100; i++) {
      const request = new NextRequest('http://localhost:3000/api/enhancement/enhance', {
        method: 'POST',
        body: JSON.stringify({
          equipmentId: 'test_equipment',
          currentLevel: 10,
          enhancementType: 'starforce'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()
      
      if (data.success) {
        results.push(data.result.type)
      }
    }

    // 验证结果分布合理
    const successCount = results.filter(r => r === 'success').length
    const failureCount = results.filter(r => r === 'failed').length
    const majorFailureCount = results.filter(r => r === 'major_failure').length

    expect(successCount).toBeGreaterThan(0)
    expect(failureCount + majorFailureCount).toBeGreaterThan(0)
    
    // 成功率应该大致符合预期（80%左右，允许一定偏差）
    const successRate = (successCount / results.length) * 100
    expect(successRate).toBeGreaterThan(60) // 允许较大偏差
    expect(successRate).toBeLessThan(100)
  })

  it('应该正确计算不同等级的强化费用', async () => {
    const testCases = [
      { level: 0, expected: 1000000 },
      { level: 10, expected: 1024000000 },
      { level: 20, expected: 1048576000000 }
    ]

    for (const testCase of testCases) {
      const request = new NextRequest('http://localhost:3000/api/enhancement/enhance', {
        method: 'POST',
        body: JSON.stringify({
          equipmentId: 'test_equipment',
          currentLevel: testCase.level,
          enhancementType: 'starforce'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      if (data.success) {
        expect(data.cost.mesos).toBe(testCase.expected)
      }
    }
  })
})

describe('真实概率数据集成测试', () => {
  beforeEach(() => {
    // 重置 mock 以使用真实的算法
    jest.clearAllMocks()
  })

  it('应该使用真实的星力概率数据', async () => {
    const request = new NextRequest('http://localhost:3000/api/enhancement/enhance', {
      method: 'POST',
      body: JSON.stringify({
        equipmentId: 'test_equipment',
        currentLevel: 10,
        enhancementType: 'starforce',
        starcatchEnabled: false,
        preventEnabled: false
      }),
      headers: {
        'Content-Type': 'application/json'
      }
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data.success).toBe(true)

    // 验证概率数据格式
    expect(data.probability).toBeDefined()
    expect(data.probability.success).toBeGreaterThan(0)
    expect(data.probability.failure).toBeGreaterThanOrEqual(0)
    expect(data.probability.majorFailure).toBeGreaterThanOrEqual(0)
    expect(data.probability.failureDrop).toBeGreaterThanOrEqual(0)

    // 验证概率总和接近100%
    const total = data.probability.success + data.probability.failure +
                 data.probability.majorFailure + data.probability.failureDrop
    expect(total).toBeCloseTo(100, 1)
  })

  it('应该正确应用抓星星效果', async () => {
    const request = new NextRequest('http://localhost:3000/api/enhancement/enhance', {
      method: 'POST',
      body: JSON.stringify({
        equipmentId: 'test_equipment',
        currentLevel: 10,
        enhancementType: 'starforce',
        starcatchEnabled: true,
        preventEnabled: false
      }),
      headers: {
        'Content-Type': 'application/json'
      }
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data.success).toBe(true)

    // 抓星星应该提高成功率
    expect(data.probability.success).toBeGreaterThan(50) // 基础成功率应该被提升
  })
})
