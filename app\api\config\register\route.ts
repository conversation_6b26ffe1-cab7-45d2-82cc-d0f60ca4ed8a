import { NextResponse } from 'next/server'
import { isAuthProviderEnabled } from '@/lib/auth-config'

export async function GET() {
  try {
    const config = {
      showEmailTab: isAuthProviderEnabled('credentials') || isAuthProviderEnabled('email'),
      showUsernameTab: isAuthProviderEnabled('username')
    }

    return NextResponse.json(config)
  } catch (error) {
    console.error('Error getting register config:', error)
    
    // 返回默认配置
    return NextResponse.json({
      showEmailTab: true,
      showUsernameTab: false
    })
  }
}
