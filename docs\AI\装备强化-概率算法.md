# 1
我有一个 CSV 文件，记录了《冒险岛》装备“星之力”强化系统的概率数据，字段包括：
当前级, 下一级, 成功率, 失败保级, 失败降级, 失败损坏。
请用 Python 编写一个强化模拟器，要求如下：

1. 从该 CSV 文件中读取并构建强化概率映射（按当前星级索引）。
2. 实现函数 simulate_enhancement(current_star: int) -> Tuple[str, int]，用于模拟一次强化。
3. 每次强化使用对应星级的概率，根据以下逻辑判断结果：
成功：进入“下一级”，返回 "Success"
失败保级：返回 "Fail (Hold)"，星级不变
失败降级：返回 "Fail (Drop)"，星级 -1
失败损坏：返回 "Boom"，星级变为 0
4. 所有概率总和应为 1（或接近 1）。可使用 random.choices() 或等效方式实现带权重随机选择。
5. 提供一个函数用于连续模拟 N 次强化过程，从 0 星开始，打印每次星级和结果。
无需使用任何外部依赖，仅使用标准库完成。

还要考虑，用户开启了“解锁抓星星”和“防止破坏”选项。

# 2
任务： 添加装备强化API接口

按照`全栈Next.js架构实施方案.md`文档，添加装备强化API接口。一个现代的优雅的API接口。可以被其他第三方使用的API接口。
同时生成一份python代码调用这个接口。

页面模拟强化的代码文件:`components/enhancement-simulator/EnhancementSimulator.tsx`
在"装备强化模拟器"页面，用户点击强化，后就调用这个接口。装备强化模拟器页面通过这个接口返回的成功、失败保级、失败损坏。来显示不同的页面效果。

保持代码符合 strict 模式规范。
用户开启了“解锁抓星星”和“防止破坏”选项。
## 2.1 
任务：为冒险岛情报站添加装备强化API接口

**背景**：
- 项目使用Next.js 14 + TypeScript + Prisma + PostgreSQL架构
- 需要为现有的装备强化模拟器页面(`components/enhancement-simulator/EnhancementSimulator.tsx`)提供后端API支持
- API需要符合RESTful设计原则，支持第三方调用

**具体要求**：

1. **API接口设计**：
    - 创建 `app/api/enhancement/enhance/route.ts` 文件
    - 实现POST方法处理装备强化请求
    - 输入参数：装备ID、当前强化等级、强化类型(星力/潜能等)、启用解锁抓星星、启用防止破坏
    - 输出结果：强化结果(成功/失败保级/失败损坏)、新等级、消耗材料/费用
    - 包含完整的错误处理和参数验证
    - 添加API文档注释和TypeScript类型定义
    - 只需要实现一个简单80%成功、10%失败保级、5%失败损坏,5%失败降级。具体的业务逻辑，后续再开发。

2. **数据库设计**：
    - 暂时不需要。

3. **前端集成**：
    - 修改 `EnhancementSimulator.tsx` 组件，将模拟逻辑替换为API调用
    - 保持现有的UI交互效果(成功/失败动画等)
    - 添加加载状态和错误处理

4. **第三方调用支持**：
    - 提供API密钥验证机制
    - 生成完整的API文档(包含请求/响应示例)
    - 创建Python调用示例代码

5. **代码质量要求**：
    - 严格遵循TypeScript strict模式
    - 符合项目现有的代码规范和架构模式
    - 包含单元测试用例
    - 添加适当的日志记录

**交付物**：
1. 完整的API接口实现
2. 更新后的前端组件
3. Python调用示例代码
4. API文档说明
5. 相关的类型定义和数据库模型(如需要)

请按照项目现有的文件结构和命名规范实施，确保与现有认证系统、错误处理机制等保持一致。
