卷 DATA 的文件夹 PATH 列表
卷序列号为 DAF0-A359
D:.
├─Common
│  ├─CostInfo
│  │  └─button_close
│  │      ├─disabled
│  │      ├─mouseOver
│  │      ├─normal
│  │      └─pressed
│  ├─dialog
│  │  ├─popUp
│  │  │  ├─button_cancel
│  │  │  │  ├─disabled
│  │  │  │  ├─mouseOver
│  │  │  │  ├─normal
│  │  │  │  └─pressed
│  │  │  └─button_confirm
│  │  │      ├─disabled
│  │  │      ├─mouseOver
│  │  │      ├─normal
│  │  │      └─pressed
│  │  ├─popUp2
│  │  │  └─button_confirm
│  │  │      ├─disabled
│  │  │      ├─mouseOver
│  │  │      ├─normal
│  │  │      └─pressed
│  │  └─popUp3
│  │      └─button_confirm
│  │          ├─disabled
│  │          ├─mouseOver
│  │          ├─normal
│  │          └─pressed
│  └─miniGame
│      ├─button_stop
│      │  ├─disabled
│      │  ├─mouseOver
│      │  ├─normal
│      │  └─pressed
│      ├─gauge
│      ├─particle
│      ├─particle2
│      ├─particle3
│      ├─particle4
│      ├─particle5
│      ├─star
│      ├─startEff
│      ├─stopEff
│      ├─successEff
│      └─time
├─Exchange
│  ├─button_Close
│  │  ├─disabled
│  │  ├─mouseOver
│  │  ├─normal
│  │  └─pressed
│  ├─button_Exchange
│  │  ├─disabled
│  │  ├─mouseOver
│  │  ├─normal
│  │  └─pressed
│  ├─button_Shortcut1
│  │  ├─disabled
│  │  ├─mouseOver
│  │  ├─normal
│  │  └─pressed
│  ├─button_Shortcut2
│  │  ├─disabled
│  │  ├─mouseOver
│  │  ├─normal
│  │  └─pressed
│  ├─button_Shortcut3
│  │  ├─disabled
│  │  ├─mouseOver
│  │  ├─normal
│  │  └─pressed
│  └─button_Shortcut4
│      ├─disabled
│      ├─mouseOver
│      ├─normal
│      └─pressed
├─ExchangeResult
│  ├─button_cancel
│  │  ├─disabled
│  │  ├─mouseOver
│  │  ├─normal
│  │  └─pressed
│  └─button_confirm
│      ├─disabled
│      ├─mouseOver
│      ├─normal
│      └─pressed
├─Main
│  ├─button_Cancel
│  │  ├─disabled
│  │  ├─mouseOver
│  │  ├─normal
│  │  └─pressed
│  ├─button_Close
│  │  ├─disabled
│  │  ├─mouseOver
│  │  ├─normal
│  │  └─pressed
│  ├─button_Enhance
│  │  ├─disabled
│  │  ├─mouseOver
│  │  ├─normal
│  │  └─pressed
│  ├─button_Guide
│  │  ├─disabled
│  │  ├─mouseOver
│  │  ├─normal
│  │  └─pressed
│  ├─Cost
│  │  ├─Exchange
│  │  ├─Function
│  │  │  ├─button_Auto
│  │  │  │  ├─checked
│  │  │  │  └─normal
│  │  │  ├─button_Exchange
│  │  │  │  ├─disabled
│  │  │  │  ├─mouseOver
│  │  │  │  ├─normal
│  │  │  │  └─pressed
│  │  │  ├─button_History
│  │  │  │  ├─disabled
│  │  │  │  ├─mouseOver
│  │  │  │  ├─normal
│  │  │  │  └─pressed
│  │  │  ├─Notice
│  │  │  ├─Tag
│  │  │  └─Timer
│  │  └─Title
│  │      ├─2048716
│  │      ├─2048717
│  │      ├─2048753
│  │      └─4037189
│  ├─Equip
│  │  ├─Effect
│  │  │  ├─Bonusstat
│  │  │  │  ├─Result
│  │  │  │  └─Standby
│  │  │  ├─Potential
│  │  │  │  ├─Progress
│  │  │  │  │  └─Loop
│  │  │  │  ├─Result
│  │  │  │  └─Standby
│  │  │  └─Starforce
│  │  │      ├─Progress
│  │  │      │  └─Loop
│  │  │      ├─Result
│  │  │      │  ├─Destroyed
│  │  │      │  ├─Failed
│  │  │      │  └─Success
│  │  │      └─Standby
│  │  ├─Notice
│  │  │  └─msg
│  │  └─Slot
│  │      └─Badge
│  ├─Info
│  │  ├─Bonusstat
│  │  │  ├─Function
│  │  │  │  └─Flame
│  │  │  │      ├─2048716
│  │  │  │      │  ├─disabled
│  │  │  │      │  ├─normal
│  │  │  │      │  └─selected
│  │  │  │      ├─2048717
│  │  │  │      │  ├─disabled
│  │  │  │      │  ├─normal
│  │  │  │      │  └─selected
│  │  │  │      └─2048753
│  │  │  │          ├─disabled
│  │  │  │          ├─normal
│  │  │  │          └─selected
│  │  │  └─Result
│  │  │      ├─Button
│  │  │      │  ├─button_SelectNext
│  │  │      │  │  ├─disabled
│  │  │      │  │  ├─mouseOver
│  │  │      │  │  ├─normal
│  │  │      │  │  └─pressed
│  │  │      │  └─button_SelectPrev
│  │  │      │      ├─disabled
│  │  │      │      ├─mouseOver
│  │  │      │      ├─normal
│  │  │      │      └─pressed
│  │  │      ├─Effect
│  │  │      │  ├─Common
│  │  │      │  └─Select
│  │  │      ├─Next
│  │  │      │  ├─0
│  │  │      │  └─1
│  │  │      ├─Prev
│  │  │      │  ├─0
│  │  │      │  └─1
│  │  │      └─Title
│  │  │          ├─Result
│  │  │          ├─Select
│  │  │          └─Standby
│  │  ├─Potential
│  │  │  ├─Function
│  │  │  │  ├─button_Next
│  │  │  │  │  ├─disabled
│  │  │  │  │  ├─mouseOver
│  │  │  │  │  ├─normal
│  │  │  │  │  └─pressed
│  │  │  │  ├─button_Prev
│  │  │  │  │  ├─disabled
│  │  │  │  │  ├─mouseOver
│  │  │  │  │  ├─normal
│  │  │  │  │  └─pressed
│  │  │  │  └─Cube
│  │  │  │      ├─2711000
│  │  │  │      │  ├─disabled
│  │  │  │      │  ├─normal
│  │  │  │      │  └─selected
│  │  │  │      ├─2730000
│  │  │  │      │  ├─disabled
│  │  │  │      │  ├─normal
│  │  │  │      │  └─selected
│  │  │  │      ├─5062009
│  │  │  │      │  ├─disabled
│  │  │  │      │  ├─normal
│  │  │  │      │  └─selected
│  │  │  │      ├─5062010
│  │  │  │      │  ├─disabled
│  │  │  │      │  ├─normal
│  │  │  │      │  └─selected
│  │  │  │      └─5062500
│  │  │  │          ├─disabled
│  │  │  │          ├─normal
│  │  │  │          └─selected
│  │  │  └─Result
│  │  │      ├─Button
│  │  │      │  ├─button_SelectNext
│  │  │      │  │  ├─disabled
│  │  │      │  │  ├─mouseOver
│  │  │      │  │  ├─normal
│  │  │      │  │  └─pressed
│  │  │      │  └─button_SelectPrev
│  │  │      │      ├─disabled
│  │  │      │      ├─mouseOver
│  │  │      │      ├─normal
│  │  │      │      └─pressed
│  │  │      ├─Effect
│  │  │      │  ├─Common
│  │  │      │  ├─Grade
│  │  │      │  │  ├─Loop
│  │  │      │  │  │  ├─0
│  │  │      │  │  │  ├─1
│  │  │      │  │  │  ├─2
│  │  │      │  │  │  └─3
│  │  │      │  │  └─Result
│  │  │      │  │      ├─0
│  │  │      │  │      ├─1
│  │  │      │  │      ├─2
│  │  │      │  │      └─3
│  │  │      │  └─Select
│  │  │      ├─Next
│  │  │      │  └─Grade
│  │  │      │      ├─0
│  │  │      │      ├─1
│  │  │      │      ├─2
│  │  │      │      ├─3
│  │  │      │      └─4
│  │  │      ├─Prev
│  │  │      │  └─Grade
│  │  │      │      ├─0
│  │  │      │      ├─1
│  │  │      │      ├─2
│  │  │      │      ├─3
│  │  │      │      └─4
│  │  │      └─Title
│  │  │          ├─Result
│  │  │          ├─Select
│  │  │          └─Standby
│  │  └─Starforce
│  │      ├─Grade
│  │      │  └─Number
│  │      │      ├─After
│  │      │      └─Before
│  │      ├─Result
│  │      │  └─ListItem
│  │      └─Title
│  │          ├─Off
│  │          └─On
│  └─Tab
│      ├─Bonusstat
│      │  ├─disabled
│      │  ├─normal
│      │  └─selected
│      ├─Potential
│      │  ├─disabled
│      │  ├─normal
│      │  └─selected
│      └─Starforce
│          ├─disabled
│          ├─normal
│          └─selected
└─Register
    ├─button_Close
    │  ├─disabled
    │  ├─mouseOver
    │  ├─normal
    │  └─pressed
    ├─button_Guide
    │  ├─disabled
    │  ├─mouseOver
    │  ├─normal
    │  └─pressed
    └─button_Inventory
        ├─disabled
        ├─mouseOver
        ├─normal
        └─pressed
