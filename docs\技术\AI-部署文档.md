基于我们已经完成的冒险岛情报站全栈Next.js架构升级（包含NextAuth.js v5认证系统、Prisma ORM、PostgreSQL数据库、Redis缓存、虚拟货币系统等），请提供详细的部署指南，涵盖以下三种环境：

**1. 开发环境部署**
- 本地开发环境的完整配置步骤
- 数据库初始化和种子数据设置
- 环境变量配置示例
- 开发服务器启动流程
- 常见开发环境问题排查

**2. 生产环境 - Vercel部署**
- Vercel平台的项目配置
- 环境变量在Vercel中的设置方法
- 数据库连接配置（外部PostgreSQL）
- 构建和部署流程
- 域名配置和SSL证书
- 监控和日志查看

**3. 生产环境 - 自建服务器部署（无Docker）**
- 服务器环境要求（Node.js版本、系统要求等）
- 手动部署流程（不使用Docker容器化）
- PostgreSQL和Redis在服务器上的安装配置
- PM2或其他进程管理工具的使用
- Nginx反向代理配置
- SSL证书配置（Let's Encrypt）
- 自动化部署脚本
- 服务器监控和日志管理
- 备份策略

**4. 生产环境 - 自建服务器部署（无Nginx）**
- 服务器环境要求（Node.js版本、系统要求等）
- 手动部署流程（不使用Docker容器化）
- PostgreSQL和Redis在服务器上的安装配置
- PM2或其他进程管理工具的使用
- 自动化部署脚本
- 服务器监控和日志管理
- 备份策略

**具体要求：**
- 提供完整的命令行指令和配置文件示例
- 包含环境变量的安全配置建议
- 说明数据库迁移在不同环境中的处理方式
- 提供故障排查和常见问题解决方案
- 考虑邮件服务（Resend）在不同环境中的配置差异
- 包含性能优化建议和安全配置要点

# 2
请为冒险岛情报站项目创建一个专门针对 Rocky Linux 9.5 生产环境的应用部署文档。文档应该专注于以下要求：

**环境假设：**
- 目标系统：Rocky Linux 9.5
- PostgreSQL 和 Redis 已经在服务器上安装并配置完成
- 服务器已具备基础的网络和安全配置

**文档重点内容：**
1. Rocky Linux 9.5 特定的系统配置和依赖安装
2. Node.js 18+ 在 Rocky Linux 9.5 上的安装和配置
3. 冒险岛情报站应用的部署流程（代码部署、构建、配置）
4. PM2 进程管理器的安装和配置
5. 应用的环境变量配置（连接现有的 PostgreSQL 和 Redis）
6. 应用启动、重启、监控的具体步骤
8. 应用日志管理和故障排查
9. 自动化部署脚本（适配 Rocky Linux 9.5 的包管理器 dnf）

**排除内容：**
- PostgreSQL 数据库的安装和配置
- Redis 缓存服务的安装和配置
- Nginx 反向代理的配置（除非特别需要应用层面的配置）

请提供完整的命令行指令、配置文件示例，以及针对 Rocky Linux 9.5 系统特性的具体说明。


