'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Loader2, Eye, EyeOff, CheckCircle } from 'lucide-react'

// 客户端注册配置接口
interface ClientRegisterConfig {
  showEmailTab: boolean
  showUsernameTab: boolean
}

// 获取客户端注册配置的 hook
function useRegisterConfig(): ClientRegisterConfig | null {
  const [config, setConfig] = useState<ClientRegisterConfig | null>(null)

  useEffect(() => {
    // 从服务端获取注册配置
    fetch('/api/config/register')
      .then(res => res.json())
      .then(data => setConfig(data))
      .catch(err => {
        console.error('Failed to fetch register config:', err)
        // 使用默认配置
        setConfig({
          showEmailTab: true,
          showUsernameTab: false
        })
      })
  }, [])

  return config
}

// 邮箱注册表单验证
const emailRegisterSchema = z.object({
  name: z.string().min(2, '姓名至少2个字符').max(50, '姓名不能超过50个字符'),
  email: z.string().email('请输入有效的邮箱地址'),
  password: z.string()
    .min(8, '密码至少8位')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, '密码必须包含大小写字母和数字'),
  confirmPassword: z.string(),
  agreeToTerms: z.boolean().refine(val => val === true, '请同意服务条款')
}).refine(data => data.password === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword']
})

// 用户名注册表单验证
const usernameRegisterSchema = z.object({
  username: z.string()
    .min(3, '用户名至少3个字符')
    .max(20, '用户名不能超过20个字符')
    .regex(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'),
  // email: z.string().email('请输入有效的邮箱地址'),
  // name: z.string().min(2, '姓名至少2个字符').max(50, '姓名不能超过50个字符'),
  password: z.string()
    .min(8, '密码至少8位')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, '密码必须包含大小写字母和数字'),
  confirmPassword: z.string(),
  agreeToTerms: z.boolean().refine(val => val === true, '请同意服务条款')
}).refine(data => data.password === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword']
})

type EmailRegisterFormData = z.infer<typeof emailRegisterSchema>
type UsernameRegisterFormData = z.infer<typeof usernameRegisterSchema>

export function EnhancedRegisterForm() {
  const [activeTab, setActiveTab] = useState('email')
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  const router = useRouter()

  // 获取注册配置
  const config = useRegisterConfig()

  // 邮箱注册表单 - 必须在条件渲染之前调用
  const emailForm = useForm<EmailRegisterFormData>({
    resolver: zodResolver(emailRegisterSchema),
    defaultValues: {
      agreeToTerms: false
    }
  })

  // 用户名注册表单 - 必须在条件渲染之前调用
  const usernameForm = useForm<UsernameRegisterFormData>({
    resolver: zodResolver(usernameRegisterSchema),
    defaultValues: {
      agreeToTerms: false
    }
  })

  // 如果配置还没加载完成，显示加载状态
  if (!config) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">加载中...</span>
      </div>
    )
  }

  const onEmailSubmit = async (data: EmailRegisterFormData) => {
    console.log('邮箱注册表单提交:', { ...data, password: '[HIDDEN]', confirmPassword: '[HIDDEN]' })
    setIsLoading(true)
    setError('')

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: data.name,
          email: data.email,
          password: data.password,
        }),
      })

      const result = await response.json()

      if (response.ok) {
        console.log('邮箱注册成功:', result)
        setSuccess(true)
        setTimeout(() => {
          router.push('/login')
        }, 2000)
      } else {
        console.error('邮箱注册失败:', result)
        setError(result.error || '注册失败，请稍后重试')
      }
    } catch (error) {
      console.error('邮箱注册异常:', error)
      setError('注册失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  const onUsernameSubmit = async (data: UsernameRegisterFormData) => {
    console.log('用户名注册表单提交:', { ...data, password: '[HIDDEN]', confirmPassword: '[HIDDEN]' })
    setIsLoading(true)
    setError('')

    try {
      const response = await fetch('/api/auth/register-username', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: data.username,
          // email: data.email,
          // name: data.name,
          password: data.password,
        }),
      })

      const result = await response.json()

      if (response.ok) {
        console.log('用户名注册成功:', result)
        setSuccess(true)
        setTimeout(() => {
          router.push('/login')
        }, 2000)
      } else {
        console.error('用户名注册失败:', result)
        setError(result.error || '注册失败，请稍后重试')
      }
    } catch (error) {
      console.error('用户名注册异常:', error)
      setError('注册失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  if (success) {
    return (
      <div className="text-center space-y-4">
        <div className="flex justify-center">
          <CheckCircle className="h-16 w-16 text-green-500" />
        </div>
        <h3 className="text-lg font-semibold text-green-700">注册成功！</h3>
        <p className="text-gray-600">
          请查收验证邮件，验证后即可登录使用。
        </p>
        <p className="text-sm text-gray-500">
          页面将在 2 秒后自动跳转到登录页面...
        </p>
      </div>
    )
  }

  // 如果只有一种注册方式，不显示标签页
  if (config.showEmailTab && !config.showUsernameTab) {
    return <EmailRegisterForm />
  }

  if (!config.showEmailTab && config.showUsernameTab) {
    return <UsernameRegisterForm />
  }

  // 显示标签页选择
  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid w-full grid-cols-2">
        {config.showEmailTab && (
          <TabsTrigger value="email">邮箱注册</TabsTrigger>
        )}
        {config.showUsernameTab && (
          <TabsTrigger value="username">用户名注册</TabsTrigger>
        )}
      </TabsList>

      {config.showEmailTab && (
        <TabsContent value="email">
          <EmailRegisterForm />
        </TabsContent>
      )}

      {config.showUsernameTab && (
        <TabsContent value="username">
          <UsernameRegisterForm />
        </TabsContent>
      )}
    </Tabs>
  )

  // 邮箱注册表单组件
  function EmailRegisterForm() {
    return (
      <form onSubmit={emailForm.handleSubmit(onEmailSubmit)} className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-2">
          <Label htmlFor="email-name">姓名</Label>
          <Input
            id="email-name"
            type="text"
            placeholder="请输入您的姓名"
            {...emailForm.register('name')}
            disabled={isLoading}
          />
          {emailForm.formState.errors.name && (
            <p className="text-sm text-red-600">{emailForm.formState.errors.name.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="email-email">邮箱地址</Label>
          <Input
            id="email-email"
            type="email"
            placeholder="请输入邮箱地址"
            {...emailForm.register('email')}
            disabled={isLoading}
          />
          {emailForm.formState.errors.email && (
            <p className="text-sm text-red-600">{emailForm.formState.errors.email.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="email-password">密码</Label>
          <div className="relative">
            <Input
              id="email-password"
              type={showPassword ? 'text' : 'password'}
              placeholder="请输入密码"
              {...emailForm.register('password')}
              disabled={isLoading}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowPassword(!showPassword)}
              disabled={isLoading}
            >
              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </Button>
          </div>
          {emailForm.formState.errors.password && (
            <p className="text-sm text-red-600">{emailForm.formState.errors.password.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="email-confirmPassword">确认密码</Label>
          <div className="relative">
            <Input
              id="email-confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              placeholder="请再次输入密码"
              {...emailForm.register('confirmPassword')}
              disabled={isLoading}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              disabled={isLoading}
            >
              {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </Button>
          </div>
          {emailForm.formState.errors.confirmPassword && (
            <p className="text-sm text-red-600">{emailForm.formState.errors.confirmPassword.message}</p>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <Controller
            name="agreeToTerms"
            control={emailForm.control}
            render={({ field }) => (
              <Checkbox
                id="email-agreeToTerms"
                checked={field.value}
                onCheckedChange={field.onChange}
                disabled={isLoading}
              />
            )}
          />
          <Label htmlFor="email-agreeToTerms" className="text-sm">
            我同意{' '}
            <a href="/terms" className="text-blue-600 hover:text-blue-500 underline">
              服务条款
            </a>{' '}
            和{' '}
            <a href="/privacy" className="text-blue-600 hover:text-blue-500 underline">
              隐私政策
            </a>
          </Label>
        </div>
        {emailForm.formState.errors.agreeToTerms && (
          <p className="text-sm text-red-600">{emailForm.formState.errors.agreeToTerms.message}</p>
        )}

        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          注册账户
        </Button>
      </form>
    )
  }

  // 用户名注册表单组件
  function UsernameRegisterForm() {
    return (
      <form onSubmit={usernameForm.handleSubmit(onUsernameSubmit)} className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-2">
          <Label htmlFor="username-username">用户名</Label>
          <Input
            id="username-username"
            type="text"
            placeholder="请输入用户名（3-20个字符）"
            {...usernameForm.register('username')}
            disabled={isLoading}
          />
          {usernameForm.formState.errors.username && (
            <p className="text-sm text-red-600">{usernameForm.formState.errors.username.message}</p>
          )}
          <p className="text-xs text-gray-500">
            用户名只能包含字母、数字和下划线
          </p>
        </div>

{/*        <div className="space-y-2">
          <Label htmlFor="username-email">邮箱地址</Label>
          <Input
            id="username-email"
            type="email"
            placeholder="请输入邮箱地址"
            {...usernameForm.register('email')}
            disabled={isLoading}
          />
          {usernameForm.formState.errors.email && (
            <p className="text-sm text-red-600">{usernameForm.formState.errors.email.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="username-name">姓名</Label>
          <Input
            id="username-name"
            type="text"
            placeholder="请输入您的姓名"
            {...usernameForm.register('name')}
            disabled={isLoading}
          />
          {usernameForm.formState.errors.name && (
            <p className="text-sm text-red-600">{usernameForm.formState.errors.name.message}</p>
          )}
        </div>
*/}
        <div className="space-y-2">
          <Label htmlFor="username-password">密码</Label>
          <div className="relative">
            <Input
              id="username-password"
              type={showPassword ? 'text' : 'password'}
              placeholder="请输入密码"
              {...usernameForm.register('password')}
              disabled={isLoading}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowPassword(!showPassword)}
              disabled={isLoading}
            >
              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </Button>
          </div>
          {usernameForm.formState.errors.password && (
            <p className="text-sm text-red-600">{usernameForm.formState.errors.password.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="username-confirmPassword">确认密码</Label>
          <div className="relative">
            <Input
              id="username-confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              placeholder="请再次输入密码"
              {...usernameForm.register('confirmPassword')}
              disabled={isLoading}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              disabled={isLoading}
            >
              {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </Button>
          </div>
          {usernameForm.formState.errors.confirmPassword && (
            <p className="text-sm text-red-600">{usernameForm.formState.errors.confirmPassword.message}</p>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <Controller
            name="agreeToTerms"
            control={usernameForm.control}
            render={({ field }) => (
              <Checkbox
                id="username-agreeToTerms"
                checked={field.value}
                onCheckedChange={field.onChange}
                disabled={isLoading}
              />
            )}
          />
          <Label htmlFor="username-agreeToTerms" className="text-sm">
            我同意{' '}
            <a href="/terms" className="text-blue-600 hover:text-blue-500 underline">
              服务条款
            </a>{' '}
            和{' '}
            <a href="/privacy" className="text-blue-600 hover:text-blue-500 underline">
              隐私政策
            </a>
          </Label>
        </div>
        {usernameForm.formState.errors.agreeToTerms && (
          <p className="text-sm text-red-600">{usernameForm.formState.errors.agreeToTerms.message}</p>
        )}

        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          注册账户
        </Button>
      </form>
    )
  }
}
