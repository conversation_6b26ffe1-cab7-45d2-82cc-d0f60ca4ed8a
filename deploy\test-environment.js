#!/usr/bin/env node

/**
 * 部署环境测试脚本
 * 检查所有必要的依赖和配置是否正确
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🔍 检查部署环境...\n')

let hasErrors = false
let hasWarnings = false

function checkItem(name, check, errorMsg, warningMsg = null) {
  try {
    const result = check()
    if (result === true) {
      console.log(`✅ ${name}`)
    } else if (result === 'warning') {
      console.log(`⚠️  ${name} - ${warningMsg}`)
      hasWarnings = true
    } else {
      console.log(`❌ ${name} - ${errorMsg}`)
      hasErrors = true
    }
  } catch (error) {
    console.log(`❌ ${name} - ${errorMsg}: ${error.message}`)
    hasErrors = true
  }
}

// 检查 Node.js 版本
checkItem(
  'Node.js 版本',
  () => {
    const version = process.version
    const major = parseInt(version.slice(1).split('.')[0])
    return major >= 18
  },
  'Node.js 版本过低，需要 18.17+',
  `当前版本: ${process.version}`
)

// 检查 npm
checkItem(
  'npm 可用性',
  () => {
    try {
      execSync('npm --version', { stdio: 'pipe' })
      return true
    } catch {
      return false
    }
  },
  'npm 不可用'
)

// 检查 package.json
checkItem(
  'package.json 文件',
  () => fs.existsSync('package.json'),
  '未找到 package.json 文件'
)

// 检查 .next 目录
checkItem(
  '.next 构建目录',
  () => fs.existsSync('.next'),
  '未找到 .next 目录，请先运行构建'
)

// 检查 .next/static 目录
checkItem(
  '静态资源目录',
  () => fs.existsSync('.next/static'),
  '未找到静态资源目录'
)

// 检查环境变量文件
checkItem(
  '.env.local 文件',
  () => {
    if (fs.existsSync('.env.local')) {
      return true
    } else if (fs.existsSync('.env.example')) {
      return 'warning'
    } else {
      return false
    }
  },
  '未找到环境变量文件',
  '未找到 .env.local，但存在 .env.example'
)

// 检查 node_modules
checkItem(
  'node_modules 目录',
  () => {
    if (fs.existsSync('node_modules')) {
      return true
    } else {
      return 'warning'
    }
  },
  '未找到 node_modules',
  '需要运行 npm install'
)

// 检查 Next.js 是否可用
checkItem(
  'Next.js 可用性',
  () => {
    try {
      // 检查是否可以通过 npx 调用 next
      execSync('npx next --version', { stdio: 'pipe' })
      return true
    } catch {
      return false
    }
  },
  'Next.js 不可用，请检查安装'
)

// 检查 Prisma（如果存在）
if (fs.existsSync('prisma')) {
  checkItem(
    'Prisma 可用性',
    () => {
      try {
        execSync('npx prisma --version', { stdio: 'pipe' })
        return true
      } catch {
        return false
      }
    },
    'Prisma 不可用'
  )
}

// 检查端口可用性
checkItem(
  '端口 3000 可用性',
  () => {
    try {
      const { execSync } = require('child_process')
      if (process.platform === 'win32') {
        const result = execSync('netstat -an | find ":3000"', { encoding: 'utf8', stdio: 'pipe' })
        return result.includes('LISTENING') ? 'warning' : true
      } else {
        try {
          execSync('lsof -i :3000', { stdio: 'pipe' })
          return 'warning'
        } catch {
          return true
        }
      }
    } catch {
      return true
    }
  },
  '端口检查失败',
  '端口 3000 已被占用'
)

// 检查关键文件
const criticalFiles = [
  'next.config.mjs',
  '.next/BUILD_ID',
  '.next/server/app',
  '.next/static/chunks'
]

criticalFiles.forEach(file => {
  checkItem(
    `关键文件: ${file}`,
    () => fs.existsSync(file),
    `缺少关键文件: ${file}`
  )
})

console.log('\n📊 检查结果:')

if (hasErrors) {
  console.log('❌ 发现错误，请解决后再启动应用')
  console.log('\n🔧 建议解决方案:')
  console.log('1. 确保 Node.js 18.17+ 已安装')
  console.log('2. 运行 npm install 安装依赖')
  console.log('3. 确保项目已构建 (npm run build)')
  console.log('4. 创建 .env.local 环境变量文件')
  process.exit(1)
} else if (hasWarnings) {
  console.log('⚠️  发现警告，建议检查后启动')
  console.log('\n💡 建议操作:')
  console.log('1. 创建 .env.local 文件并配置环境变量')
  console.log('2. 如果端口被占用，请停止占用进程或使用其他端口')
  console.log('3. 运行 npm install 确保依赖完整')
} else {
  console.log('✅ 环境检查通过，可以启动应用')
}

console.log('\n🚀 启动命令:')
console.log('Linux/Mac: ./start-production.sh')
console.log('Windows:   start-production.bat')
console.log('手动启动:  npx next start')

console.log('\n📖 更多帮助请查看 README-DEPLOY.md')
