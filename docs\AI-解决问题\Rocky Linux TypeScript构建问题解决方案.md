# Rocky Linux 9.5 TypeScript 构建问题解决方案

## 🔍 问题现象

在 Rocky Linux 9.5 生产环境中，`npm run build` 报错：

```
Type error: Parameter 'token' implicitly has an 'any' type.
./app/api/auth/cleanup-tokens/route.ts:45:40
```

## 🎯 根本原因

1. **TypeScript 版本差异**：Rocky Linux 环境的 TypeScript 配置更严格
2. **隐式 any 类型**：map 函数参数没有明确类型声明
3. **Prisma 类型导入缺失**：缺少必要的类型导入

## ✅ 解决方案

### 🚀 快速修复（已完成）

**修复的文件**：`app/api/auth/cleanup-tokens/route.ts`

**修复内容**：

1. **添加类型导入**：
   ```typescript
   import type { VerificationToken } from '@prisma/client'
   ```

2. **明确参数类型**：
   ```typescript
   // 修复前
   expiredTokens.map(token => ({
   
   // 修复后
   expiredTokens.map((token: VerificationToken) => ({
   ```

### 🔧 完整修复代码

```typescript
import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import type { VerificationToken } from '@prisma/client'

// 在两个地方修复了类型问题：
// 1. POST 方法中的 expiredTokens.map
// 2. GET 方法中的 recentExpired.map

export async function POST(request: NextRequest) {
  // ... 其他代码
  
  return NextResponse.json({
    success: true,
    message: `成功清理 ${deleteResult.count} 个过期令牌`,
    deletedCount: deleteResult.count,
    expiredTokens: expiredTokens.map((token: VerificationToken) => ({
      identifier: token.identifier,
      expires: token.expires,
      expiredHours: Math.floor((now.getTime() - token.expires.getTime()) / (1000 * 60 * 60))
    }))
  })
}

export async function GET(request: NextRequest) {
  // ... 其他代码
  
  return NextResponse.json({
    success: true,
    statistics: {
      expiredCount,
      validCount,
      totalCount: expiredCount + validCount,
      recentExpired: recentExpired.map((token: VerificationToken) => ({
        identifier: token.identifier,
        expires: token.expires,
        expiredHours: Math.floor((now.getTime() - token.expires.getTime()) / (1000 * 60 * 60))
      }))
    }
  })
}
```

## 🛠️ 预防措施

### 1. TypeScript 配置优化

确保 `tsconfig.json` 包含严格的类型检查：

```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true
  }
}
```

### 2. 创建类型检查脚本

```bash
#!/bin/bash
# scripts/check-types.sh

echo "🔍 检查 TypeScript 类型..."

# 运行类型检查
npx tsc --noEmit

if [ $? -eq 0 ]; then
    echo "✅ 类型检查通过"
else
    echo "❌ 类型检查失败"
    exit 1
fi
```

### 3. 自动修复脚本

已创建 `scripts/fix-typescript-issues.js` 用于自动检测和修复常见类型问题。

## 📋 部署前检查清单

### 1. 本地验证

```bash
# 检查类型
npm run build

# 运行自动修复（如果需要）
node scripts/fix-typescript-issues.js
```

### 2. 生产环境部署

```bash
# 1. 上传修复后的文件
scp app/api/auth/cleanup-tokens/route.ts user@server:/root/gits/maplestory-info-station/app/api/auth/cleanup-tokens/

# 2. 在服务器上构建
ssh user@server
cd /root/gits/maplestory-info-station
npm run build

# 3. 如果成功，重启应用
npm start
```

### 3. 验证修复

```bash
# 测试 API 端点
curl -X POST https://mxd.hyhuman.xyz/api/auth/cleanup-tokens
curl -X GET https://mxd.hyhuman.xyz/api/auth/cleanup-tokens
```

## 🔍 常见 TypeScript 问题模式

### 1. 隐式 any 参数

```typescript
// ❌ 错误
array.map(item => item.property)

// ✅ 正确
array.map((item: ItemType) => item.property)
```

### 2. catch 块错误处理

```typescript
// ❌ 错误
catch (error) {
  console.log(error.message)
}

// ✅ 正确
catch (error) {
  console.log(error instanceof Error ? error.message : String(error))
}
```

### 3. 缺失类型导入

```typescript
// ❌ 错误
// 使用 User 类型但没有导入

// ✅ 正确
import type { User } from '@prisma/client'
```

## 🚨 故障排除

### 问题 1: 仍然有类型错误

**检查步骤**：
1. 确认所有修改已保存
2. 清理构建缓存：`rm -rf .next`
3. 重新安装依赖：`npm install`
4. 重新构建：`npm run build`

### 问题 2: Prisma 类型不可用

**解决方案**：
```bash
# 重新生成 Prisma 客户端
npx prisma generate
```

### 问题 3: Node.js 版本兼容性

**检查版本**：
```bash
node --version  # 需要 18.17+
npm --version
```

## 📊 环境差异对比

| 环境 | TypeScript 版本 | 严格模式 | 构建行为 |
|------|----------------|----------|----------|
| Windows 开发 | 5.x | 部分严格 | 较宽松 |
| Rocky Linux 9.5 | 5.x | 完全严格 | 严格检查 |

## 🎯 最佳实践

### 1. 开发环境配置

在开发环境中启用严格的 TypeScript 检查：

```json
// tsconfig.json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true
  }
}
```

### 2. CI/CD 集成

```yaml
# .github/workflows/build.yml
- name: Type Check
  run: npx tsc --noEmit

- name: Build
  run: npm run build
```

### 3. 代码审查

在代码审查中重点检查：
- map/filter/reduce 函数的参数类型
- catch 块的错误处理
- Prisma 查询的类型安全

## 🎉 验证成功

修复后的验证结果：

```bash
npm run build
# ✓ Compiled successfully
# ✓ Linting and checking validity of types
# ✓ Collecting page data
# ✓ Generating static pages (37/37)
# ✓ Finalizing page optimization
```

---

**总结**：通过明确指定 TypeScript 类型和添加必要的类型导入，成功解决了 Rocky Linux 9.5 环境中的构建问题。关键是在开发环境中启用严格的类型检查，以便提前发现和修复类型问题。
