# 邮件 URL 问题解决方案

## 🔍 问题现象

生产环境中，明明配置了 `NEXT_PUBLIC_APP_URL="https://mxd.hyhuman.xyz"`，但邮件中的验证链接仍然是：
```
http://localhost:3000/verify-email?token=...&email=...
```

## 🎯 根本原因

1. **环境变量作用域问题**：`NEXT_PUBLIC_` 前缀的环境变量主要用于客户端，在服务器端的邮件生成时可能无法正确获取
2. **环境变量加载时机**：邮件发送是在服务器端执行，但使用了客户端环境变量
3. **优先级问题**：代码中没有正确的环境变量获取优先级

## ✅ 解决方案

### 🔧 已修复的代码

**修复前** (`lib/email.ts`)：
```typescript
const APP_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
```

**修复后** (`lib/email.ts`)：
```typescript
// 获取应用 URL，优先使用服务器端环境变量
function getAppUrl(): string {
  // 优先级：NEXTAUTH_URL > NEXT_PUBLIC_APP_URL > APP_URL > 默认值
  return process.env.NEXTAUTH_URL || 
         process.env.NEXT_PUBLIC_APP_URL || 
         process.env.APP_URL ||
         'http://localhost:3000'
}

const APP_URL = getAppUrl()
```

### 📋 环境变量配置

确保 `.env.local` 文件包含以下配置：

```bash
# NextAuth 配置 - 服务器端优先使用
NEXTAUTH_URL="https://mxd.hyhuman.xyz"

# 客户端应用 URL
NEXT_PUBLIC_APP_URL="https://mxd.hyhuman.xyz"

# 备用应用 URL (用于邮件等服务器端功能)
APP_URL="https://mxd.hyhuman.xyz"
```

## 🚀 快速修复步骤

### 方法一：使用修复脚本（推荐）

```bash
# 1. 上传修复脚本到服务器
scp fix-email-url.sh user@server:/root/gits/maplestory-info-station/

# 2. 在服务器上运行修复脚本
ssh user@server
cd /root/gits/maplestory-info-station
chmod +x fix-email-url.sh
./fix-email-url.sh
```

### 方法二：手动修复

```bash
# 1. 上传修复后的邮件模块
scp lib/email.ts user@server:/root/gits/maplestory-info-station/lib/

# 2. 在服务器上检查环境变量
ssh user@server
cd /root/gits/maplestory-info-station

# 3. 确保 .env.local 包含正确配置
echo 'APP_URL="https://mxd.hyhuman.xyz"' >> .env.local

# 4. 重启应用
pm2 restart maplestory-info-station
# 或
pkill -f "next start" && npm start
```

## 🧪 验证修复

### 1. 运行测试脚本

```bash
# 在服务器上运行
node test-email-env.js
```

**期望输出**：
```
✅ 配置正确！邮件中的链接应该使用正确的 HTTPS 域名。
```

### 2. 检查应用日志

修复后的代码会输出调试信息：
```
🔧 环境变量调试:
   NEXTAUTH_URL: https://mxd.hyhuman.xyz
   NEXT_PUBLIC_APP_URL: https://mxd.hyhuman.xyz
   APP_URL: https://mxd.hyhuman.xyz
   最终使用的 APP_URL: https://mxd.hyhuman.xyz
📧 验证链接: https://mxd.hyhuman.xyz/verify-email?token=...
```

### 3. 实际测试

1. 注册一个新用户
2. 检查收到的验证邮件
3. 确认链接使用 `https://mxd.hyhuman.xyz`

## 🔍 故障排除

### 问题 1: 环境变量未加载

**检查命令**：
```bash
# 检查环境变量是否正确加载
node -e "console.log('NEXTAUTH_URL:', process.env.NEXTAUTH_URL)"
node -e "console.log('APP_URL:', process.env.APP_URL)"
```

**解决方案**：
```bash
# 确保 .env.local 文件存在且格式正确
cat .env.local | grep -E "(NEXTAUTH_URL|APP_URL)"

# 重启应用
pm2 restart maplestory-info-station
```

### 问题 2: 仍然使用 localhost

**检查应用日志**：
```bash
pm2 logs maplestory-info-station | grep "环境变量调试"
```

**可能原因**：
- 应用未完全重启
- 环境变量文件路径错误
- 缓存问题

**解决方案**：
```bash
# 完全停止并重启
pm2 delete maplestory-info-station
pm2 start ecosystem.config.js

# 或清理缓存
rm -rf .next
npm run build
npm start
```

### 问题 3: 邮件服务配置问题

**检查邮件配置**：
```bash
node -e "console.log('EMAIL_FROM:', process.env.EMAIL_FROM)"
node -e "console.log('RESEND_API_KEY:', process.env.RESEND_API_KEY ? '已设置' : '未设置')"
```

## 📊 环境变量优先级

修复后的优先级顺序：

1. **NEXTAUTH_URL** - NextAuth 官方环境变量，服务器端优先
2. **NEXT_PUBLIC_APP_URL** - Next.js 客户端环境变量
3. **APP_URL** - 自定义服务器端环境变量
4. **默认值** - `http://localhost:3000`

## 🛠️ 预防措施

### 1. 环境变量检查脚本

将 `test-email-env.js` 添加到部署流程：

```bash
# 在部署后运行检查
node test-email-env.js
```

### 2. 启动时验证

在应用启动时自动验证环境变量：

```javascript
// 在 lib/startup-logger.ts 中已包含
if (!process.env.NEXTAUTH_URL) {
  console.warn('⚠️  警告: NEXTAUTH_URL 未设置，邮件链接可能不正确')
}
```

### 3. 监控邮件链接

在邮件发送时记录链接：

```javascript
// 在 lib/email.ts 中已添加
console.log('📧 验证链接:', verificationUrl)
```

## 📋 完整的环境变量清单

**生产环境 `.env.local` 必需配置**：

```bash
# 应用 URL 配置 (三个都要配置确保兼容性)
NEXTAUTH_URL="https://mxd.hyhuman.xyz"
NEXT_PUBLIC_APP_URL="https://mxd.hyhuman.xyz"
APP_URL="https://mxd.hyhuman.xyz"

# 其他必需配置
DATABASE_URL="postgresql://postgres:password@localhost:5432/mxd_info_db"
NEXTAUTH_SECRET="your-production-secret"
RESEND_API_KEY="your-resend-api-key"
EMAIL_FROM="<EMAIL>"
```

## 🎯 验证成功标志

修复成功后，您应该看到：

1. **测试脚本输出**：
   ```
   ✅ 配置正确！邮件中的链接应该使用正确的 HTTPS 域名。
   ```

2. **应用日志显示**：
   ```
   最终使用的 APP_URL: https://mxd.hyhuman.xyz
   📧 验证链接: https://mxd.hyhuman.xyz/verify-email?token=...
   ```

3. **实际邮件内容**：
   ```
   https://mxd.hyhuman.xyz/verify-email?token=...&email=...
   ```

---

**总结**：通过修复环境变量获取逻辑并确保正确的优先级，邮件中的验证链接现在应该使用正确的 HTTPS 生产域名。关键是使用 `NEXTAUTH_URL` 作为服务器端的首选环境变量。
