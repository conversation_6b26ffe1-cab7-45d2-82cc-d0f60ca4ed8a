import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { validateUser } from '@/lib/auth-providers'
import { signIn } from 'next-auth/react'

const signinSchema = z.object({
  identifier: z.string().min(1, '请输入邮箱或用户名'),
  password: z.string().min(1, '请输入密码'),
  callbackUrl: z.string().optional()
})

export async function POST(request: NextRequest) {
  console.log('🔐 开始处理自定义登录请求')

  try {
    const body = await request.json()
    console.log('🔐 接收到登录请求:', { identifier: body.identifier })

    const { identifier, password, callbackUrl } = signinSchema.parse(body)
    console.log('✅ 登录数据验证通过')

    // 首先验证用户凭据，获取详细的错误信息
    try {
      const user = await validateUser(identifier, password)
      console.log('✅ 用户验证成功:', user.email || user.username)

      // 验证成功，返回成功状态
      return NextResponse.json({
        success: true,
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          name: user.name
        }
      })

    } catch (validationError) {
      const errorMessage = validationError instanceof Error ? validationError.message : '用户验证失败'
      console.error('❌ 用户验证失败:', errorMessage)

      // 返回具体的错误信息
      return NextResponse.json(
        {
          success: false,
          error: errorMessage,
          errorType: getErrorType(errorMessage)
        },
        { status: 401 }
      )
    }

  } catch (error) {
    console.error('❌ 自定义登录过程发生错误:', error)

    if (error instanceof z.ZodError) {
      console.error('❌ 数据验证错误:', error.errors)
      return NextResponse.json(
        { 
          success: false,
          error: '请输入完整的登录信息',
          errorType: 'VALIDATION_ERROR'
        },
        { status: 400 }
      )
    }

    console.error('❌ 自定义登录失败，未知错误:', error)
    return NextResponse.json(
      { 
        success: false,
        error: '登录失败，请稍后重试',
        errorType: 'UNKNOWN_ERROR'
      },
      { status: 500 }
    )
  }
}

// 错误类型分类
function getErrorType(errorMessage: string): string {
  if (errorMessage.includes('验证邮箱') || errorMessage.includes('邮箱未验证') || errorMessage.includes('请先验证')) {
    return 'EMAIL_NOT_VERIFIED'
  }
  if (errorMessage.includes('账户已被禁用') || errorMessage.includes('账户被禁用')) {
    return 'ACCOUNT_DISABLED'
  }
  if (errorMessage.includes('用户不存在') || errorMessage.includes('密码错误') || errorMessage.includes('登录信息')) {
    return 'INVALID_CREDENTIALS'
  }
  return 'UNKNOWN'
}
