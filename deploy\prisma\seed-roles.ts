import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 开始创建默认角色...')

  // 创建基础角色
  const roles = [
    {
      name: 'guest',
      displayName: '游客',
      description: '未注册用户，功能受限',
      permissions: ['read:public'],
      isActive: true
    },
    {
      name: 'registered',
      displayName: '注册用户',
      description: '已注册的普通用户',
      permissions: ['read:public', 'read:user', 'write:user'],
      isActive: true
    },
    {
      name: 'vip',
      displayName: 'VIP用户',
      description: 'VIP会员用户',
      permissions: ['read:public', 'read:user', 'write:user', 'read:vip', 'use:simulator'],
      isActive: true
    },
    {
      name: 'diamond',
      displayName: '钻石用户',
      description: '钻石会员用户',
      permissions: ['read:public', 'read:user', 'write:user', 'read:vip', 'use:simulator', 'read:diamond', 'export:data'],
      isActive: true
    },
    {
      name: 'admin',
      displayName: '管理员',
      description: '系统管理员',
      permissions: ['*'],
      isActive: true
    },
    {
      name: 'super_admin',
      displayName: '超级管理员',
      description: '超级管理员，拥有所有权限',
      permissions: ['*'],
      isActive: true
    }
  ]

  for (const roleData of roles) {
    try {
      const existingRole = await prisma.role.findUnique({
        where: { name: roleData.name }
      })

      if (existingRole) {
        console.log(`✅ 角色 "${roleData.name}" 已存在，跳过创建`)
        continue
      }

      const role = await prisma.role.create({
        data: roleData
      })

      console.log(`✅ 创建角色: ${role.name} (${role.displayName})`)
    } catch (error) {
      console.error(`❌ 创建角色 "${roleData.name}" 失败:`, error)
    }
  }

  console.log('🎉 角色创建完成！')
}

main()
  .catch((e) => {
    console.error('❌ 种子数据创建失败:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
