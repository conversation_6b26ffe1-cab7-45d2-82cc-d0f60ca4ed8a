# Next.js 构建输出目录说明

## 📁 主要输出目录：`.next/`

使用 `npm run build` 或 `pnpm run build` 构建后，所有编译文件都会放在 **`.next/`** 目录中。

## 🗂️ 目录结构详解

### 1. 静态资源目录：`.next/static/`

这是最重要的目录，包含所有前端静态资源：

```
.next/static/
├── chunks/                    # JavaScript 代码分割块
│   ├── app/                  # App Router 相关的块
│   ├── pages/                # Pages Router 相关的块
│   ├── framework-*.js        # React 框架代码
│   ├── main-app-*.js         # 主应用代码
│   ├── main-*.js             # 主页面代码
│   ├── polyfills-*.js        # 浏览器兼容性代码
│   ├── webpack-*.js          # Webpack 运行时
│   └── [数字]-[hash].js      # 各种代码分割块
├── css/                      # 编译后的 CSS 文件
│   ├── 89197cff08321d62.css # 全局样式
│   └── 92189c7527d89792.css # 组件样式
├── media/                    # 字体和媒体文件
│   ├── *.woff2               # 字体文件
│   └── [其他媒体文件]
└── [BUILD_ID]/               # 构建ID目录
    ├── _buildManifest.js     # 构建清单
    └── _ssgManifest.js       # 静态生成清单
```

### 2. 服务端代码目录：`.next/server/`

包含服务端渲染和API路由代码：

```
.next/server/
├── app/                      # App Router 服务端代码
├── pages/                    # Pages Router 服务端代码
├── chunks/                   # 服务端代码块
├── middleware.js             # 中间件代码
├── pages-manifest.json       # 页面清单
├── app-paths-manifest.json   # 应用路径清单
└── [其他服务端文件]
```

### 3. 缓存目录：`.next/cache/`

构建缓存，用于加速后续构建：

```
.next/cache/
├── swc/                      # SWC 编译器缓存
└── webpack/                  # Webpack 缓存
```

### 4. 类型定义：`.next/types/`

TypeScript 类型定义文件：

```
.next/types/
├── app/                      # App Router 类型
└── package.json              # 类型包配置
```

### 5. 配置文件

根目录下的各种配置和清单文件：

```
.next/
├── BUILD_ID                  # 构建ID
├── build-manifest.json       # 构建清单
├── app-build-manifest.json   # App 构建清单
├── prerender-manifest.json   # 预渲染清单
├── routes-manifest.json      # 路由清单
├── images-manifest.json      # 图片清单
└── [其他配置文件]
```

## 📊 构建统计信息

根据构建输出，您的项目包含：

### 页面统计
- **35个页面** 成功构建
- **32个动态页面** (ƒ 标记)
- **1个静态页面** (○ 标记)
- **1个SSG页面** (● 标记)

### 文件大小
- **主应用代码**: 87.2 kB (共享)
- **最大页面**: 131 kB (/register)
- **最小页面**: 87.3 kB (/database, /debug)
- **中间件**: 37.5 kB

### 关键文件
- **框架代码**: `chunks/framework-*.js` (44.8 kB)
- **主应用**: `chunks/main-app-*.js` (53.6 kB)
- **样式文件**: `css/*.css` (2个文件)
- **字体文件**: `media/*.woff2` (15个字体文件)

## 🚀 部署相关

### 1. 需要部署的文件

**完整部署** (推荐):
```bash
# 部署整个项目目录，包括：
├── .next/           # 构建输出 (必需)
├── public/          # 静态资源 (必需)
├── package.json     # 依赖配置 (必需)
├── next.config.mjs  # Next.js配置 (必需)
├── prisma/          # 数据库配置 (必需)
└── [其他配置文件]
```

**静态文件部署** (如果使用CDN):
```bash
# 只需要 .next/static/ 目录
.next/static/
├── chunks/          # JavaScript文件
├── css/             # CSS文件
├── media/           # 字体和媒体文件
└── [BUILD_ID]/      # 构建清单
```

### 2. 环境变量

确保生产环境包含必要的环境变量：
```bash
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-secret-key
DATABASE_URL=your-database-url
# [其他环境变量]
```

### 3. 启动命令

```bash
# 生产环境启动
npm start
# 或
pnpm start
```

## 🔧 优化建议

### 1. 文件大小优化

**当前较大的页面**:
- `/register` (131 kB) - 考虑代码分割
- `/cubes` (128 kB) - 考虑懒加载
- `/starforce` (129 kB) - 考虑组件优化

### 2. 缓存策略

**静态资源缓存**:
```nginx
# Nginx 配置示例
location /_next/static/ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 3. CDN 配置

**推荐上传到CDN的文件**:
- `.next/static/chunks/` - JavaScript文件
- `.next/static/css/` - CSS文件  
- `.next/static/media/` - 字体和媒体文件

## 📝 注意事项

### 1. 构建ID

每次构建都会生成新的 `BUILD_ID`，确保缓存失效和版本控制。

### 2. 文件哈希

所有静态文件都包含内容哈希，确保缓存策略的有效性。

### 3. 代码分割

Next.js 自动进行代码分割，每个页面只加载必要的代码。

### 4. 服务端代码

`.next/server/` 目录包含服务端代码，不应该暴露给客户端。

## 🎯 总结

- **主要输出目录**: `.next/`
- **静态资源**: `.next/static/`
- **服务端代码**: `.next/server/`
- **构建成功**: 35个页面，总大小合理
- **部署就绪**: 可以直接部署到生产环境

您的项目构建成功，所有文件都在 `.next/` 目录中，可以进行生产部署了！ 🎉
