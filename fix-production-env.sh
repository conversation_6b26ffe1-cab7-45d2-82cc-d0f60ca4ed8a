#!/bin/bash

# 生产环境 NextAuth 重定向修复脚本

echo "🔧 修复生产环境 NextAuth 重定向问题..."
echo ""

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 备份现有配置
if [ -f ".env.local" ]; then
    cp .env.local .env.local.backup.$(date +%Y%m%d_%H%M%S)
    echo "✅ 已备份现有 .env.local 文件"
fi

# 创建生产环境配置
echo "📝 创建生产环境配置..."

cat > .env.local << 'EOF'
# 数据库配置
DATABASE_URL="postgresql://postgres:password@localhost:5432/mxd_info_db"
REDIS_URL="redis://localhost:6379"

# NextAuth 配置 - 生产环境
NEXTAUTH_URL="https://mxd.hyhuman.xyz"
NEXTAUTH_SECRET="your-production-secret-key-change-this"

# 信任代理配置 - 重要！
AUTH_TRUST_HOST="true"
NEXTAUTH_URL_INTERNAL="http://localhost:3000"

# 认证配置
ENABLED_AUTH_PROVIDERS="credentials,email,google,username"

# 登录方式配置
ALLOW_EMAIL_LOGIN="true"
ALLOW_USERNAME_LOGIN="true"
REQUIRE_EMAIL_VERIFICATION="true"

# Google OAuth 配置
GOOGLE_CLIENT_ID="686418751121-6aeqgmeelmt3r4c292anb7l6e2l58lmt.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"

# 邮件服务 (Resend)
RESEND_API_KEY="re_hbVrpFJK_9oWrqfu8MHwjKpKJD57E39t9"
EMAIL_FROM="<EMAIL>"

# 第三方服务
NEXT_PUBLIC_FINGERPRINT_JS_API_KEY="MWO0YLKT9AgMYCg00m81"
STRIPE_SECRET_KEY="sk_test_your-stripe-key"
STRIPE_WEBHOOK_SECRET="whsec_your-webhook-secret"

# 安全配置
JWT_SECRET="f59f2fce99062058f3918822c436dc0505b617a935abe53b0a6dc8da74f34398"
ENCRYPTION_KEY="gdp+YSmMzchNZ3w2LNuQYyuhoBdlN/cEBlQ="

# 应用配置 - 生产环境
NEXT_PUBLIC_APP_URL="https://mxd.hyhuman.xyz"
NEXT_PUBLIC_APP_NAME="冒险岛情报站"

# 生产环境配置
NODE_ENV="production"
LOG_LEVEL="info"

# 虚拟货币配置
CURRENCY_NAME="欢乐豆"
DEFAULT_CURRENCY_AMOUNT="100"

# 功能开关
ENABLE_REGISTRATION="true"
ENABLE_EMAIL_VERIFICATION="true"
ENABLE_FINGERPRINT_TRACKING="true"

# Prisma 配置 (Rocky Linux)
PRISMA_QUERY_ENGINE_LIBRARY="/root/svc/mxd/mxd-tool/node_modules/.prisma/client/libquery_engine-rhel-openssl-1.0.x.so.node"
PRISMA_QUERY_ENGINE_BINARY="/root/svc/mxd/mxd-tool/node_modules/.prisma/client/query-engine-rhel-openssl-1.0.x"
CHECKPOINT_DISABLE="1"
EOF

echo "✅ 已创建生产环境配置"

# 设置正确的权限
chmod 600 .env.local

echo ""
echo "🔧 重要提醒:"
echo "1. 请更新 NEXTAUTH_SECRET 为安全的随机字符串"
echo "2. 请更新 DATABASE_URL 为实际的数据库连接"
echo "3. 确认所有配置项都正确"
echo ""

# 生成安全的 NEXTAUTH_SECRET
if command -v openssl >/dev/null 2>&1; then
    NEW_SECRET=$(openssl rand -base64 32)
    echo "🔑 建议的 NEXTAUTH_SECRET:"
    echo "NEXTAUTH_SECRET=\"$NEW_SECRET\""
    echo ""
fi

echo "📋 下一步操作:"
echo "1. 编辑 .env.local 文件，更新必要的配置"
echo "2. 重启应用: pnpm start"
echo "3. 测试登录和登出功能"
echo ""
echo "🧪 测试命令:"
echo "curl -I https://mxd.hyhuman.xyz/api/auth/signin"
echo "curl -I https://mxd.hyhuman.xyz/api/auth/signout"
