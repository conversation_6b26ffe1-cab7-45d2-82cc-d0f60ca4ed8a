@echo off
chcp 65001 >nul
echo 🚀 启动 MapleStory 信息站...
echo.

REM 检查 Node.js 版本
echo 检查 Node.js 版本...
node -v
if %errorlevel% neq 0 (
    echo ❌ 错误: Node.js 未安装或不在 PATH 中
    echo 请安装 Node.js 18.17+ 版本
    pause
    exit /b 1
)

REM 检查 npm 版本
echo 检查 npm 版本...
npm -v
if %errorlevel% neq 0 (
    echo ❌ 错误: npm 未安装或不在 PATH 中
    pause
    exit /b 1
)

REM 检查是否在正确的目录
if not exist "package.json" (
    echo ❌ 错误: 未找到 package.json 文件
    echo 请确保在正确的项目目录中运行此脚本
    pause
    exit /b 1
)

REM 检查环境变量
if not exist ".env.local" (
    echo ⚠️  警告: .env.local 文件不存在，请创建并配置环境变量
    echo 参考 .env.example 文件
    echo.
    echo 示例配置:
    echo DATABASE_URL="postgresql://postgres:password@localhost:5432/mxd_info_db"
    echo NEXTAUTH_URL="https://your-domain.com"
    echo NEXTAUTH_SECRET="your-secret-key"
    echo.
    echo 是否继续启动? (y/n)
    set /p continue=
    if /i not "%continue%"=="y" (
        echo 启动已取消
        pause
        exit /b 0
    )
)

REM 检查 .next 目录是否存在
if not exist ".next" (
    echo ❌ 错误: 未找到 .next 构建目录
    echo 请确保项目已经构建完成
    pause
    exit /b 1
)

REM 安装依赖（如果需要）
if not exist "node_modules" (
    echo 📦 安装生产依赖...
    npm install --production --silent
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

REM 生成 Prisma 客户端（如果需要）
if exist "prisma" (
    echo 🔧 生成 Prisma 客户端...
    npx prisma generate --silent
)

REM 启动应用
echo.
echo 🌟 启动应用...
echo 应用将在 http://localhost:3000 启动
echo 按 Ctrl+C 停止应用
echo.

REM 使用 npm start 而不是直接调用 next
npm start

REM 如果启动失败，显示错误信息
if %errorlevel% neq 0 (
    echo.
    echo ❌ 应用启动失败
    echo 请检查:
    echo 1. 环境变量配置是否正确
    echo 2. 数据库连接是否正常
    echo 3. 端口 3000 是否被占用
    echo 4. 查看上方的错误信息
    pause
)
