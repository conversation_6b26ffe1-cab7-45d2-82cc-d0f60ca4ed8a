import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/db'

const statusSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址')
})

export async function POST(request: NextRequest) {
  console.log('📧 开始处理激活状态查询请求')

  try {
    const body = await request.json()
    console.log('📧 接收到查询请求:', { email: body.email })

    const { email } = statusSchema.parse(body)
    console.log('✅ 邮箱格式验证通过')

    // 查找用户
    console.log('🔍 查找用户:', email)
    const user = await prisma.user.findUnique({
      where: { email }
    })

    if (!user) {
      console.log('❌ 用户不存在:', email)
      return NextResponse.json(
        { 
          exists: false,
          message: '该邮箱尚未注册'
        },
        { status: 404 }
      )
    }

    // 检查用户激活状态
    if (user.emailVerified) {
      console.log('✅ 用户邮箱已激活:', email)
      return NextResponse.json({
        exists: true,
        activated: true,
        activatedAt: user.emailVerified,
        message: '邮箱已激活，可以正常登录'
      })
    }

    // 用户未激活，查找验证令牌
    console.log('🔍 查找验证令牌:', email)
    const verificationToken = await prisma.verificationToken.findFirst({
      where: { identifier: email },
      orderBy: { expires: 'desc' } // 获取最新的令牌
    })

    if (!verificationToken) {
      console.log('❌ 未找到验证令牌:', email)
      return NextResponse.json({
        exists: true,
        activated: false,
        hasToken: false,
        message: '未找到激活令牌，请重新发送激活邮件'
      })
    }

    // 检查令牌是否过期
    const now = new Date()
    const isExpired = verificationToken.expires < now

    if (isExpired) {
      console.log('❌ 验证令牌已过期:', email)
      
      // 计算过期时间
      const expiredHours = Math.floor((now.getTime() - verificationToken.expires.getTime()) / (1000 * 60 * 60))
      
      return NextResponse.json({
        exists: true,
        activated: false,
        hasToken: true,
        tokenExpired: true,
        expiredHours,
        message: `激活令牌已过期${expiredHours > 0 ? ` (过期 ${expiredHours} 小时)` : ''}，请重新发送激活邮件`
      })
    }

    // 令牌有效，计算剩余时间
    const remainingTime = verificationToken.expires.getTime() - now.getTime()
    const remainingHours = Math.floor(remainingTime / (1000 * 60 * 60))
    const remainingMinutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60))

    console.log('✅ 验证令牌有效:', email, '剩余时间:', remainingHours, '小时', remainingMinutes, '分钟')

    return NextResponse.json({
      exists: true,
      activated: false,
      hasToken: true,
      tokenExpired: false,
      expiresAt: verificationToken.expires,
      remainingHours,
      remainingMinutes,
      message: `激活令牌有效，剩余时间: ${remainingHours} 小时 ${remainingMinutes} 分钟`
    })

  } catch (error) {
    console.error('❌ 激活状态查询过程发生错误:', error)

    if (error instanceof z.ZodError) {
      console.error('❌ 数据验证错误:', error.errors)
      return NextResponse.json(
        { error: '邮箱格式错误' },
        { status: 400 }
      )
    }

    console.error('❌ 激活状态查询失败，未知错误:', error)
    return NextResponse.json(
      { error: '查询失败，请稍后重试' },
      { status: 500 }
    )
  }
}

// GET 方法支持 URL 参数查询
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const email = searchParams.get('email')

  if (!email) {
    return NextResponse.json(
      { error: '缺少邮箱参数' },
      { status: 400 }
    )
  }

  // 重用 POST 方法的逻辑
  const mockRequest = {
    json: async () => ({ email })
  } as NextRequest

  return POST(mockRequest)
}
