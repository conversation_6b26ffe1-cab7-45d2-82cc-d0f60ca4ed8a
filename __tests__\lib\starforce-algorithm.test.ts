/**
 * 星力强化算法单元测试
 */

import { describe, it, expect, beforeAll } from '@jest/globals'
import {
  getBaseProbability,
  applyStarcatchBonus,
  applyPreventDestruction,
  calculateFinalProbability,
  calculateStarforceResult,
  getStarforceInfo,
  StarforceResult,
  StarforceProbability
} from '@/lib/starforce-algorithm'

// Mock 概率数据
const mockProbabilityData: Record<number, StarforceProbability> = {
  0: { success: 0.95, failHold: 0.05, failDrop: 0, boom: 0 },
  10: { success: 0.5, failHold: 0.5, failDrop: 0, boom: 0 },
  15: { success: 0.3, failHold: 0.679, failDrop: 0, boom: 0.021 },
  20: { success: 0.3, failHold: 0.63, failDrop: 0, boom: 0.07 },
  24: { success: 0.01, failHold: 0, failDrop: 0.594, boom: 0.396 }
}

describe('星力强化算法测试', () => {
  describe('概率数据处理', () => {
    it('应该正确获取基础概率数据', () => {
      const probability = getBaseProbability(0, mockProbabilityData)

      expect(probability).toHaveProperty('success')
      expect(probability).toHaveProperty('failHold')
      expect(probability).toHaveProperty('failDrop')
      expect(probability).toHaveProperty('boom')

      // 检查概率值范围
      expect(probability.success).toBeGreaterThanOrEqual(0)
      expect(probability.success).toBeLessThanOrEqual(1)
      expect(probability.failHold).toBeGreaterThanOrEqual(0)
      expect(probability.failHold).toBeLessThanOrEqual(1)
      expect(probability.failDrop).toBeGreaterThanOrEqual(0)
      expect(probability.failDrop).toBeLessThanOrEqual(1)
      expect(probability.boom).toBeGreaterThanOrEqual(0)
      expect(probability.boom).toBeLessThanOrEqual(1)
    })

    it('应该验证概率总和接近1.0', () => {
      for (const level of Object.keys(mockProbabilityData)) {
        const probability = getBaseProbability(parseInt(level), mockProbabilityData)
        const total = probability.success + probability.failHold + probability.failDrop + probability.boom

        expect(total).toBeCloseTo(1.0, 3) // 允许0.001的误差
      }
    })

    it('应该在找不到数据时抛出错误', () => {
      expect(() => getBaseProbability(99, mockProbabilityData)).toThrow('未找到星级 99 的概率数据')
    })
  })

  describe('抓星星效果', () => {
    it('应该正确应用抓星星加成', () => {
      const baseProbability = { success: 0.5, failHold: 0.3, failDrop: 0.1, boom: 0.1 }
      const adjustedProbability = applyStarcatchBonus(baseProbability)
      
      // 成功率应该提升5%
      expect(adjustedProbability.success).toBeCloseTo(0.525, 3)
      
      // 总概率应该仍为1.0
      const total = adjustedProbability.success + adjustedProbability.failHold + 
                   adjustedProbability.failDrop + adjustedProbability.boom
      expect(total).toBeCloseTo(1.0, 3)
      
      // 其他概率应该按比例缩减
      expect(adjustedProbability.failHold).toBeLessThan(baseProbability.failHold)
      expect(adjustedProbability.failDrop).toBeLessThan(baseProbability.failDrop)
      expect(adjustedProbability.boom).toBeLessThan(baseProbability.boom)
    })

    it('应该处理100%成功率的边界情况', () => {
      const baseProbability = { success: 1.0, failHold: 0, failDrop: 0, boom: 0 }
      const adjustedProbability = applyStarcatchBonus(baseProbability)
      
      expect(adjustedProbability.success).toBe(1.0)
      expect(adjustedProbability.failHold).toBe(0)
      expect(adjustedProbability.failDrop).toBe(0)
      expect(adjustedProbability.boom).toBe(0)
    })
  })

  describe('防止破坏效果', () => {
    it('应该正确应用防止破坏效果', () => {
      const baseProbability = { success: 0.3, failHold: 0.5, failDrop: 0.1, boom: 0.1 }
      const adjustedProbability = applyPreventDestruction(baseProbability)
      
      // 损坏概率应该转移到失败保级
      expect(adjustedProbability.boom).toBe(0)
      expect(adjustedProbability.failHold).toBe(0.6) // 0.5 + 0.1
      expect(adjustedProbability.success).toBe(0.3) // 不变
      expect(adjustedProbability.failDrop).toBe(0.1) // 不变
      
      // 总概率应该仍为1.0
      const total = adjustedProbability.success + adjustedProbability.failHold + 
                   adjustedProbability.failDrop + adjustedProbability.boom
      expect(total).toBeCloseTo(1.0, 3)
    })
  })

  describe('最终概率计算', () => {
    it('应该正确计算无特殊效果的概率', () => {
      const level = 10
      const options = { starcatchEnabled: false, preventEnabled: false }
      const finalProbability = calculateFinalProbability(level, options, mockProbabilityData)
      const baseProbability = getBaseProbability(level, mockProbabilityData)

      expect(finalProbability).toEqual(baseProbability)
    })

    it('应该正确计算抓星星效果的概率', () => {
      const level = 10
      const options = { starcatchEnabled: true, preventEnabled: false }
      const finalProbability = calculateFinalProbability(level, options, mockProbabilityData)
      const baseProbability = getBaseProbability(level, mockProbabilityData)

      expect(finalProbability.success).toBeGreaterThan(baseProbability.success)
    })

    it('应该正确计算防止破坏效果的概率', () => {
      const level = 15 // 选择有损坏概率的等级
      const options = { starcatchEnabled: false, preventEnabled: true }
      const finalProbability = calculateFinalProbability(level, options, mockProbabilityData)

      expect(finalProbability.boom).toBe(0)
    })

    it('应该正确计算同时启用两种效果的概率', () => {
      const level = 15
      const options = { starcatchEnabled: true, preventEnabled: true }
      const finalProbability = calculateFinalProbability(level, options, mockProbabilityData)
      const baseProbability = getBaseProbability(level, mockProbabilityData)

      expect(finalProbability.success).toBeGreaterThan(baseProbability.success)
      expect(finalProbability.boom).toBe(0)

      // 总概率应该为1.0
      const total = finalProbability.success + finalProbability.failHold +
                   finalProbability.failDrop + finalProbability.boom
      expect(total).toBeCloseTo(1.0, 3)
    })
  })

  describe('强化结果计算', () => {
    it('应该返回有效的强化结果', () => {
      const result = calculateStarforceResult(10, false, false, mockProbabilityData)

      expect(result).toHaveProperty('result')
      expect(result).toHaveProperty('newLevel')
      expect(result).toHaveProperty('previousLevel')
      expect(result).toHaveProperty('probabilities')
      expect(result).toHaveProperty('randomValue')

      expect(result.previousLevel).toBe(10)
      expect(Object.values(StarforceResult)).toContain(result.result)
      expect(result.randomValue).toBeGreaterThanOrEqual(0)
      expect(result.randomValue).toBeLessThan(1)
    })

    it('应该正确处理成功结果', () => {
      // 模拟成功情况（通过多次测试来验证）
      let successFound = false
      for (let i = 0; i < 100; i++) {
        const result = calculateStarforceResult(0, false, false, mockProbabilityData) // 0星成功率很高
        if (result.result === StarforceResult.SUCCESS) {
          expect(result.newLevel).toBe(1)
          successFound = true
          break
        }
      }
      expect(successFound).toBe(true)
    })

    it('应该正确处理失败保级结果', () => {
      // 通过多次测试验证失败保级逻辑
      let failHoldFound = false
      for (let i = 0; i < 100; i++) {
        const result = calculateStarforceResult(10, false, false, mockProbabilityData)
        if (result.result === StarforceResult.FAIL_HOLD) {
          expect(result.newLevel).toBe(10)
          failHoldFound = true
          break
        }
      }
      expect(failHoldFound).toBe(true)
    })

    it('应该正确处理失败降级结果', () => {
      // 使用24星测试降级逻辑，因为它有降级概率
      let failDropFound = false
      for (let i = 0; i < 100; i++) {
        const result = calculateStarforceResult(24, false, false, mockProbabilityData)
        if (result.result === StarforceResult.FAIL_DROP) {
          expect(result.newLevel).toBe(23)
          failDropFound = true
          break
        }
      }
      expect(failDropFound).toBe(true)
    })

    it('应该拒绝无效的星级输入', () => {
      expect(() => calculateStarforceResult(-1, false, false, mockProbabilityData)).toThrow()
      expect(() => calculateStarforceResult(25, false, false, mockProbabilityData)).toThrow()
    })
  })

  describe('星力信息获取', () => {
    it('应该正确获取星力强化信息', () => {
      const level = 10
      const options = { starcatchEnabled: false, preventEnabled: false }
      const info = getStarforceInfo(level, options, mockProbabilityData)

      expect(info).toHaveProperty('level')
      expect(info).toHaveProperty('baseProbability')
      expect(info).toHaveProperty('finalProbability')
      expect(info).toHaveProperty('options')
      expect(info).toHaveProperty('effects')

      expect(info.level).toBe(level)
      expect(info.options).toEqual(options)
      expect(Array.isArray(info.effects)).toBe(true)
    })

    it('应该正确显示特殊效果', () => {
      const level = 15
      const options = { starcatchEnabled: true, preventEnabled: true }
      const info = getStarforceInfo(level, options, mockProbabilityData)

      expect(info.effects).toContain('抓星星小游戏 (+5%成功率)')
      expect(info.effects).toContain('防止破坏 (损坏→保级)')
    })

    it('应该显示无特殊效果', () => {
      const level = 10
      const options = { starcatchEnabled: false, preventEnabled: false }
      const info = getStarforceInfo(level, options, mockProbabilityData)

      expect(info.effects).toContain('无特殊效果')
    })
  })

  describe('边界条件测试', () => {
    it('应该正确处理0星强化', () => {
      const result = calculateStarforceResult(0, false, false, mockProbabilityData)
      expect(result.previousLevel).toBe(0)
      expect(result.newLevel).toBeGreaterThanOrEqual(0)
    })

    it('应该正确处理24星强化', () => {
      const result = calculateStarforceResult(24, false, false, mockProbabilityData)
      expect(result.previousLevel).toBe(24)
      // 24星强化成功率很低，但仍应该有有效结果
      expect(result.newLevel).toBeGreaterThanOrEqual(0)
    })

    it('应该正确处理高星级的损坏重置', () => {
      // 通过多次测试验证高星级损坏逻辑
      let boomFound = false
      for (let i = 0; i < 100; i++) {
        const result = calculateStarforceResult(20, false, false, mockProbabilityData) // 20星有损坏概率
        if (result.result === StarforceResult.BOOM) {
          expect(result.newLevel).toBe(12) // 15星以上损坏重置到12星
          boomFound = true
          break
        }
      }
      // 注意：由于概率原因，可能不会在100次内遇到损坏，这是正常的
    })
  })
})
