/**
 * 星力强化算法单元测试
 */

import {
  loadStarforceProbabilities,
  getBaseProbability,
  applyStarcatchBonus,
  applyPreventDestruction,
  calculateFinalProbability,
  calculateStarforceResult,
  simulateStarforceEnhancement,
  validateProbabilityData,
  StarforceResult
} from '@/lib/starforce-algorithm'

describe('星力强化算法测试', () => {
  describe('CSV数据加载', () => {
    it('应该成功加载CSV概率数据', () => {
      const probabilityMap = loadStarforceProbabilities()
      
      expect(probabilityMap).toBeInstanceOf(Map)
      expect(probabilityMap.size).toBeGreaterThan(0)
      
      // 检查是否包含0-24星的数据
      for (let level = 0; level <= 24; level++) {
        expect(probabilityMap.has(level)).toBe(true)
      }
    })

    it('应该正确解析概率数据格式', () => {
      const probability = getBaseProbability(0)
      
      expect(probability).toHaveProperty('success')
      expect(probability).toHaveProperty('failHold')
      expect(probability).toHaveProperty('failDrop')
      expect(probability).toHaveProperty('boom')
      
      // 检查概率值范围
      expect(probability.success).toBeGreaterThanOrEqual(0)
      expect(probability.success).toBeLessThanOrEqual(1)
      expect(probability.failHold).toBeGreaterThanOrEqual(0)
      expect(probability.failHold).toBeLessThanOrEqual(1)
      expect(probability.failDrop).toBeGreaterThanOrEqual(0)
      expect(probability.failDrop).toBeLessThanOrEqual(1)
      expect(probability.boom).toBeGreaterThanOrEqual(0)
      expect(probability.boom).toBeLessThanOrEqual(1)
    })

    it('应该验证概率总和接近1.0', () => {
      for (let level = 0; level <= 24; level++) {
        const probability = getBaseProbability(level)
        const total = probability.success + probability.failHold + probability.failDrop + probability.boom
        
        expect(total).toBeCloseTo(1.0, 3) // 允许0.001的误差
      }
    })
  })

  describe('抓星星效果', () => {
    it('应该正确应用抓星星加成', () => {
      const baseProbability = { success: 0.5, failHold: 0.3, failDrop: 0.1, boom: 0.1 }
      const adjustedProbability = applyStarcatchBonus(baseProbability)
      
      // 成功率应该提升5%
      expect(adjustedProbability.success).toBeCloseTo(0.525, 3)
      
      // 总概率应该仍为1.0
      const total = adjustedProbability.success + adjustedProbability.failHold + 
                   adjustedProbability.failDrop + adjustedProbability.boom
      expect(total).toBeCloseTo(1.0, 3)
      
      // 其他概率应该按比例缩减
      expect(adjustedProbability.failHold).toBeLessThan(baseProbability.failHold)
      expect(adjustedProbability.failDrop).toBeLessThan(baseProbability.failDrop)
      expect(adjustedProbability.boom).toBeLessThan(baseProbability.boom)
    })

    it('应该处理100%成功率的边界情况', () => {
      const baseProbability = { success: 1.0, failHold: 0, failDrop: 0, boom: 0 }
      const adjustedProbability = applyStarcatchBonus(baseProbability)
      
      expect(adjustedProbability.success).toBe(1.0)
      expect(adjustedProbability.failHold).toBe(0)
      expect(adjustedProbability.failDrop).toBe(0)
      expect(adjustedProbability.boom).toBe(0)
    })
  })

  describe('防止破坏效果', () => {
    it('应该正确应用防止破坏效果', () => {
      const baseProbability = { success: 0.3, failHold: 0.5, failDrop: 0.1, boom: 0.1 }
      const adjustedProbability = applyPreventDestruction(baseProbability)
      
      // 损坏概率应该转移到失败保级
      expect(adjustedProbability.boom).toBe(0)
      expect(adjustedProbability.failHold).toBe(0.6) // 0.5 + 0.1
      expect(adjustedProbability.success).toBe(0.3) // 不变
      expect(adjustedProbability.failDrop).toBe(0.1) // 不变
      
      // 总概率应该仍为1.0
      const total = adjustedProbability.success + adjustedProbability.failHold + 
                   adjustedProbability.failDrop + adjustedProbability.boom
      expect(total).toBeCloseTo(1.0, 3)
    })
  })

  describe('最终概率计算', () => {
    it('应该正确计算无特殊效果的概率', () => {
      const level = 10
      const options = { starcatchEnabled: false, preventEnabled: false }
      const finalProbability = calculateFinalProbability(level, options)
      const baseProbability = getBaseProbability(level)
      
      expect(finalProbability).toEqual(baseProbability)
    })

    it('应该正确计算抓星星效果的概率', () => {
      const level = 10
      const options = { starcatchEnabled: true, preventEnabled: false }
      const finalProbability = calculateFinalProbability(level, options)
      const baseProbability = getBaseProbability(level)
      
      expect(finalProbability.success).toBeGreaterThan(baseProbability.success)
    })

    it('应该正确计算防止破坏效果的概率', () => {
      const level = 15 // 选择有损坏概率的等级
      const options = { starcatchEnabled: false, preventEnabled: true }
      const finalProbability = calculateFinalProbability(level, options)
      
      expect(finalProbability.boom).toBe(0)
    })

    it('应该正确计算同时启用两种效果的概率', () => {
      const level = 15
      const options = { starcatchEnabled: true, preventEnabled: true }
      const finalProbability = calculateFinalProbability(level, options)
      const baseProbability = getBaseProbability(level)
      
      expect(finalProbability.success).toBeGreaterThan(baseProbability.success)
      expect(finalProbability.boom).toBe(0)
      
      // 总概率应该为1.0
      const total = finalProbability.success + finalProbability.failHold + 
                   finalProbability.failDrop + finalProbability.boom
      expect(total).toBeCloseTo(1.0, 3)
    })
  })

  describe('强化结果计算', () => {
    it('应该返回有效的强化结果', () => {
      const result = calculateStarforceResult(10, false, false)
      
      expect(result).toHaveProperty('result')
      expect(result).toHaveProperty('newLevel')
      expect(result).toHaveProperty('previousLevel')
      expect(result).toHaveProperty('probabilities')
      expect(result).toHaveProperty('randomValue')
      
      expect(result.previousLevel).toBe(10)
      expect(Object.values(StarforceResult)).toContain(result.result)
      expect(result.randomValue).toBeGreaterThanOrEqual(0)
      expect(result.randomValue).toBeLessThan(1)
    })

    it('应该正确处理成功结果', () => {
      // 模拟成功情况（通过多次测试来验证）
      let successFound = false
      for (let i = 0; i < 100; i++) {
        const result = calculateStarforceResult(0, false, false) // 0星成功率很高
        if (result.result === StarforceResult.SUCCESS) {
          expect(result.newLevel).toBe(1)
          successFound = true
          break
        }
      }
      expect(successFound).toBe(true)
    })

    it('应该正确处理失败保级结果', () => {
      // 通过多次测试验证失败保级逻辑
      let failHoldFound = false
      for (let i = 0; i < 100; i++) {
        const result = calculateStarforceResult(10, false, false)
        if (result.result === StarforceResult.FAIL_HOLD) {
          expect(result.newLevel).toBe(10)
          failHoldFound = true
          break
        }
      }
      expect(failHoldFound).toBe(true)
    })

    it('应该正确处理失败降级结果', () => {
      // 通过多次测试验证失败降级逻辑
      let failDropFound = false
      for (let i = 0; i < 100; i++) {
        const result = calculateStarforceResult(12, false, false) // 12星有降级概率
        if (result.result === StarforceResult.FAIL_DROP) {
          expect(result.newLevel).toBe(11)
          failDropFound = true
          break
        }
      }
      expect(failDropFound).toBe(true)
    })

    it('应该拒绝无效的星级输入', () => {
      expect(() => calculateStarforceResult(-1, false, false)).toThrow()
      expect(() => calculateStarforceResult(25, false, false)).toThrow()
    })
  })

  describe('模拟测试', () => {
    it('应该正确执行模拟强化', () => {
      const result = simulateStarforceEnhancement(
        0, 5, 10, // 从0星到5星，模拟10次
        { starcatchEnabled: false, preventEnabled: false }
      )
      
      expect(result).toHaveProperty('totalAttempts')
      expect(result).toHaveProperty('successCount')
      expect(result).toHaveProperty('failHoldCount')
      expect(result).toHaveProperty('failDropCount')
      expect(result).toHaveProperty('boomCount')
      expect(result).toHaveProperty('averageAttempts')
      expect(result).toHaveProperty('successRate')
      expect(result).toHaveProperty('reachedTargetCount')
      
      expect(result.totalAttempts).toBeGreaterThan(0)
      expect(result.averageAttempts).toBeGreaterThan(0)
      expect(result.successRate).toBeGreaterThanOrEqual(0)
      expect(result.successRate).toBeLessThanOrEqual(100)
    })

    it('应该拒绝无效的模拟参数', () => {
      expect(() => simulateStarforceEnhancement(5, 0, 10, { starcatchEnabled: false, preventEnabled: false })).toThrow()
      expect(() => simulateStarforceEnhancement(0, 5, 0, { starcatchEnabled: false, preventEnabled: false })).toThrow()
      expect(() => simulateStarforceEnhancement(-1, 5, 10, { starcatchEnabled: false, preventEnabled: false })).toThrow()
    })
  })

  describe('数据验证', () => {
    it('应该验证概率数据的完整性', () => {
      const validation = validateProbabilityData()
      
      expect(validation).toHaveProperty('isValid')
      expect(validation).toHaveProperty('errors')
      expect(validation).toHaveProperty('warnings')
      
      expect(Array.isArray(validation.errors)).toBe(true)
      expect(Array.isArray(validation.warnings)).toBe(true)
      
      // 如果有错误，应该记录在控制台
      if (!validation.isValid) {
        console.warn('概率数据验证失败:', validation.errors)
      }
    })
  })

  describe('边界条件测试', () => {
    it('应该正确处理0星强化', () => {
      const result = calculateStarforceResult(0, false, false)
      expect(result.previousLevel).toBe(0)
      expect(result.newLevel).toBeGreaterThanOrEqual(0)
    })

    it('应该正确处理24星强化', () => {
      const result = calculateStarforceResult(24, false, false)
      expect(result.previousLevel).toBe(24)
      // 24星强化成功率很低，但仍应该有有效结果
      expect(result.newLevel).toBeGreaterThanOrEqual(0)
    })

    it('应该正确处理高星级的损坏重置', () => {
      // 通过多次测试验证高星级损坏逻辑
      let boomFound = false
      for (let i = 0; i < 100; i++) {
        const result = calculateStarforceResult(20, false, false) // 20星有损坏概率
        if (result.result === StarforceResult.BOOM) {
          expect(result.newLevel).toBe(12) // 15星以上损坏重置到12星
          boomFound = true
          break
        }
      }
      // 注意：由于概率原因，可能不会在100次内遇到损坏，这是正常的
    })
  })
})
