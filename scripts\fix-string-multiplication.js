#!/usr/bin/env node

/**
 * 修复字符串乘法错误
 * 将 '=' * 60 替换为 '='.repeat(60)
 */

const fs = require('fs')
const path = require('path')

// 需要检查的文件扩展名
const fileExtensions = ['.ts', '.tsx', '.js', '.jsx']

// 需要排除的目录
const excludeDirs = [
  'node_modules',
  '.next',
  'dist',
  'build',
  'coverage',
  '.git'
]

function getAllFiles(dir, files = []) {
  const items = fs.readdirSync(dir)
  
  for (const item of items) {
    const fullPath = path.join(dir, item)
    const stat = fs.statSync(fullPath)
    
    if (stat.isDirectory()) {
      if (!excludeDirs.includes(item)) {
        getAllFiles(fullPath, files)
      }
    } else if (fileExtensions.some(ext => item.endsWith(ext))) {
      files.push(fullPath)
    }
  }
  
  return files
}

function fixStringMultiplication(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8')
    let modified = false
    
    // 匹配字符串乘法模式：'字符' * 数字
    const patterns = [
      {
        regex: /(['"`])([^'"`])\1\s*\*\s*(\d+)/g,
        replacement: "$1$2$1.repeat($3)"
      },
      {
        regex: /(['"`])([^'"`]+)\1\s*\*\s*(\d+)/g,
        replacement: "$1$2$1.repeat($3)"
      }
    ]
    
    patterns.forEach(pattern => {
      const originalContent = content
      content = content.replace(pattern.regex, pattern.replacement)
      
      if (content !== originalContent) {
        console.log(`🔧 修复 ${filePath} 中的字符串乘法`)
        modified = true
      }
    })
    
    if (modified) {
      fs.writeFileSync(filePath, content)
      return true
    }
    
    return false
  } catch (error) {
    console.error(`❌ 处理文件 ${filePath} 时出错:`, error.message)
    return false
  }
}

function main() {
  console.log('🔧 检查和修复字符串乘法错误...')
  
  const files = getAllFiles('.')
  console.log(`📁 扫描 ${files.length} 个文件`)
  
  let fixedFiles = 0
  
  files.forEach(file => {
    if (fixStringMultiplication(file)) {
      fixedFiles++
    }
  })
  
  console.log(`\n📊 修复统计:`)
  console.log(`   检查文件: ${files.length}`)
  console.log(`   修复文件: ${fixedFiles}`)
  
  if (fixedFiles > 0) {
    console.log('\n🔍 验证修复结果...')
    
    // 运行构建测试
    const { execSync } = require('child_process')
    try {
      execSync('npm run build', { stdio: 'pipe' })
      console.log('✅ 构建成功，所有字符串乘法错误已修复!')
    } catch (error) {
      console.log('❌ 构建仍有问题，请检查其他错误')
      console.log(error.stdout?.toString() || error.message)
    }
  } else {
    console.log('\n✅ 没有发现字符串乘法错误')
  }
}

if (require.main === module) {
  main()
}

module.exports = { fixStringMultiplication }
