#!/usr/bin/env node

/**
 * TypeScript 类型问题修复脚本
 * 检查和修复常见的 TypeScript 类型错误
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🔧 检查和修复 TypeScript 类型问题...')

// 常见的类型问题模式
const typeIssuePatterns = [
  {
    name: 'catch 块中的 error 类型',
    pattern: /catch\s*\(\s*error\s*\)\s*{[\s\S]*?error\.message/g,
    fix: (content) => {
      return content.replace(
        /catch\s*\(\s*error\s*\)\s*{([\s\S]*?)error\.message/g,
        'catch (error) {$1error instanceof Error ? error.message : String(error)'
      )
    }
  },
  {
    name: 'map 函数中的隐式 any 参数',
    pattern: /\.map\s*\(\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=>/g,
    fix: (content, filePath) => {
      // 根据文件路径推断类型
      if (filePath.includes('prisma') || filePath.includes('db')) {
        return content.replace(
          /\.map\s*\(\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=>/g,
          '.map(($1: any) =>'
        )
      }
      return content
    }
  }
]

// 需要检查的文件扩展名
const fileExtensions = ['.ts', '.tsx']

// 需要排除的目录
const excludeDirs = [
  'node_modules',
  '.next',
  'dist',
  'build',
  'coverage',
  '.git',
  'incremental-updates'
]

function getAllTypeScriptFiles(dir, files = []) {
  const items = fs.readdirSync(dir)
  
  for (const item of items) {
    const fullPath = path.join(dir, item)
    const stat = fs.statSync(fullPath)
    
    if (stat.isDirectory()) {
      if (!excludeDirs.includes(item)) {
        getAllTypeScriptFiles(fullPath, files)
      }
    } else if (fileExtensions.some(ext => item.endsWith(ext))) {
      files.push(fullPath)
    }
  }
  
  return files
}

function checkTypeScriptBuild() {
  try {
    console.log('🔍 检查 TypeScript 构建...')
    execSync('npm run build', { stdio: 'pipe' })
    console.log('✅ TypeScript 构建成功')
    return true
  } catch (error) {
    console.log('❌ TypeScript 构建失败')
    console.log(error.stdout?.toString() || error.message)
    return false
  }
}

function fixTypeIssuesInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8')
    let modified = false
    
    for (const pattern of typeIssuePatterns) {
      const originalContent = content
      
      if (typeof pattern.fix === 'function') {
        content = pattern.fix(content, filePath)
      }
      
      if (content !== originalContent) {
        console.log(`🔧 修复 ${filePath} 中的 ${pattern.name}`)
        modified = true
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content)
      return true
    }
    
    return false
  } catch (error) {
    console.error(`❌ 处理文件 ${filePath} 时出错:`, error.message)
    return false
  }
}

function addMissingImports(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8')
    let modified = false
    
    // 检查是否需要添加 Prisma 类型导入
    if (content.includes('VerificationToken') && !content.includes('import type { VerificationToken }')) {
      const importLine = "import type { VerificationToken } from '@prisma/client'"
      
      if (content.includes("import { prisma }")) {
        content = content.replace(
          "import { prisma } from '@/lib/db'",
          `import { prisma } from '@/lib/db'\n${importLine}`
        )
        modified = true
        console.log(`🔧 添加 VerificationToken 类型导入到 ${filePath}`)
      }
    }
    
    // 检查是否需要添加 User 类型导入
    if (content.includes(': User') && !content.includes('import type { User }')) {
      const importLine = "import type { User } from '@prisma/client'"
      
      if (content.includes("import { prisma }")) {
        content = content.replace(
          "import { prisma } from '@/lib/db'",
          `import { prisma } from '@/lib/db'\n${importLine}`
        )
        modified = true
        console.log(`🔧 添加 User 类型导入到 ${filePath}`)
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content)
      return true
    }
    
    return false
  } catch (error) {
    console.error(`❌ 添加导入到 ${filePath} 时出错:`, error.message)
    return false
  }
}

function main() {
  console.log('📁 扫描 TypeScript 文件...')
  
  const tsFiles = getAllTypeScriptFiles('.')
  console.log(`找到 ${tsFiles.length} 个 TypeScript 文件`)
  
  let fixedFiles = 0
  
  // 修复类型问题
  for (const filePath of tsFiles) {
    if (fixTypeIssuesInFile(filePath)) {
      fixedFiles++
    }
    
    if (addMissingImports(filePath)) {
      fixedFiles++
    }
  }
  
  console.log(`\n📊 修复统计:`)
  console.log(`   检查文件: ${tsFiles.length}`)
  console.log(`   修复文件: ${fixedFiles}`)
  
  // 检查构建
  console.log('\n🔍 验证修复结果...')
  const buildSuccess = checkTypeScriptBuild()
  
  if (buildSuccess) {
    console.log('\n🎉 所有 TypeScript 类型问题已修复!')
  } else {
    console.log('\n⚠️  仍有类型问题需要手动修复')
  }
  
  return buildSuccess
}

if (require.main === module) {
  main()
}

module.exports = { main }
