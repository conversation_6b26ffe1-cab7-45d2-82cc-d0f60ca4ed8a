#!/bin/bash

# 检查 Nginx 配置是否正确设置了反向代理头部

echo "🔍 检查 Nginx 反向代理配置..."
echo ""

# 检查 Nginx 配置文件
NGINX_CONF="/etc/nginx/sites-available/mxd.https.conf"
NGINX_ENABLED="/etc/nginx/sites-enabled/mxd.https.conf"

if [ -f "$NGINX_CONF" ]; then
    echo "✅ 找到 Nginx 配置文件: $NGINX_CONF"
    
    # 检查必要的代理头部
    echo "🔍 检查代理头部配置..."
    
    if grep -q "proxy_set_header X-Forwarded-Proto" "$NGINX_CONF"; then
        echo "✅ X-Forwarded-Proto 头部已配置"
    else
        echo "❌ 缺少 X-Forwarded-Proto 头部"
    fi
    
    if grep -q "proxy_set_header X-Forwarded-Host" "$NGINX_CONF"; then
        echo "✅ X-Forwarded-Host 头部已配置"
    else
        echo "⚠️  建议添加 X-Forwarded-Host 头部"
    fi
    
    if grep -q "proxy_set_header Host" "$NGINX_CONF"; then
        echo "✅ Host 头部已配置"
    else
        echo "❌ 缺少 Host 头部"
    fi
    
else
    echo "❌ 未找到 Nginx 配置文件"
    echo "请确保配置文件位于: $NGINX_CONF"
fi

# 检查配置是否已启用
if [ -L "$NGINX_ENABLED" ]; then
    echo "✅ 配置已启用"
else
    echo "❌ 配置未启用"
    echo "运行: sudo ln -s $NGINX_CONF $NGINX_ENABLED"
fi

# 测试 Nginx 配置
echo ""
echo "🧪 测试 Nginx 配置..."
if sudo nginx -t; then
    echo "✅ Nginx 配置语法正确"
else
    echo "❌ Nginx 配置语法错误"
fi

# 检查 Nginx 状态
echo ""
echo "🔍 检查 Nginx 状态..."
if systemctl is-active --quiet nginx; then
    echo "✅ Nginx 服务正在运行"
else
    echo "❌ Nginx 服务未运行"
    echo "启动命令: sudo systemctl start nginx"
fi

# 测试 HTTPS 连接
echo ""
echo "🌐 测试 HTTPS 连接..."
if curl -I -s https://mxd.hyhuman.xyz | head -1 | grep -q "200\|301\|302"; then
    echo "✅ HTTPS 连接正常"
else
    echo "❌ HTTPS 连接失败"
fi

# 测试 NextAuth 端点
echo ""
echo "🔐 测试 NextAuth 端点..."
if curl -I -s https://mxd.hyhuman.xyz/api/auth/signin | head -1 | grep -q "200\|302"; then
    echo "✅ NextAuth 登录端点可访问"
else
    echo "❌ NextAuth 登录端点不可访问"
fi

# 创建修复建议
echo ""
echo "🔧 如果存在问题，请尝试以下修复:"
echo ""

cat > nginx-config-fix.conf << 'EOF'
# 在 Nginx 配置的 location / 块中添加以下头部：

location / {
    proxy_pass http://localhost:3000;
    
    # 基本代理配置
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_cache_bypass $http_upgrade;
    
    # 重要：NextAuth.js 需要的头部
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Port $server_port;
    
    # 超时配置
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
}
EOF

echo "📄 已创建 nginx-config-fix.conf 参考文件"
echo ""
echo "📋 完整的修复步骤:"
echo "1. 更新 Nginx 配置添加必要的代理头部"
echo "2. 重新加载 Nginx: sudo systemctl reload nginx"
echo "3. 更新应用环境变量: ./fix-nextauth-proxy.sh"
echo "4. 重启应用: pnpm start"
echo "5. 测试登录: https://mxd.hyhuman.xyz/login"
