# Prisma 类型导入问题解决方案

## 🔍 问题现象

在 Rocky Linux 9.5 生产环境中，尝试从 `@prisma/client` 导入 `VerificationToken` 类型时报错：

```
Type error: Module '"@prisma/client"' has no exported member 'VerificationToken'.
```

## 🎯 根本原因

1. **Prisma 客户端生成问题**：可能 Prisma 客户端没有正确生成或版本不匹配
2. **模型名称映射**：Prisma 可能没有将模型名称正确导出为 TypeScript 类型
3. **环境差异**：开发环境和生产环境的 Prisma 版本或配置不同

## ✅ 解决方案

### 🚀 方案一：使用自定义类型定义（推荐）

**优势**：
- 不依赖 Prisma 客户端的类型导出
- 更稳定，不受 Prisma 版本影响
- 明确的类型定义，便于维护

**实现**：

```typescript
import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// 定义验证令牌类型
type VerificationToken = {
  identifier: string
  token: string
  expires: Date
}

// 使用自定义类型
export async function POST(request: NextRequest) {
  const expiredTokens = await prisma.verificationToken.findMany({
    where: { expires: { lt: new Date() } }
  })

  return NextResponse.json({
    expiredTokens: expiredTokens.map((token: VerificationToken) => ({
      identifier: token.identifier,
      expires: token.expires,
      expiredHours: Math.floor((Date.now() - token.expires.getTime()) / (1000 * 60 * 60))
    }))
  })
}
```

### 🔧 方案二：使用 Prisma 的 Awaited 类型

```typescript
import { prisma } from '@/lib/db'

// 从 Prisma 查询结果推断类型
type VerificationToken = Awaited<ReturnType<typeof prisma.verificationToken.findFirst>>

// 或者使用 NonNullable 确保非空
type VerificationTokenNonNull = NonNullable<Awaited<ReturnType<typeof prisma.verificationToken.findFirst>>>
```

### 🔧 方案三：重新生成 Prisma 客户端

```bash
# 清理并重新生成
rm -rf node_modules/.prisma
npm install
npx prisma generate
```

### 🔧 方案四：使用 Prisma 的通用类型

```typescript
import type { Prisma } from '@prisma/client'

// 使用 Prisma 的模型类型
type VerificationToken = Prisma.VerificationTokenCreateInput
// 或者
type VerificationToken = Prisma.VerificationTokenWhereInput
```

## 📋 完整修复代码

**修复后的 `app/api/auth/cleanup-tokens/route.ts`**：

```typescript
import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// 定义验证令牌类型
type VerificationToken = {
  identifier: string
  token: string
  expires: Date
}

// 清理过期验证令牌的 API
export async function POST(request: NextRequest) {
  console.log('🧹 开始清理过期验证令牌')

  try {
    const now = new Date()
    
    // 查找过期的令牌
    const expiredTokens = await prisma.verificationToken.findMany({
      where: {
        expires: {
          lt: now
        }
      }
    })

    console.log(`🔍 找到 ${expiredTokens.length} 个过期令牌`)

    if (expiredTokens.length === 0) {
      return NextResponse.json({
        success: true,
        message: '没有过期令牌需要清理',
        deletedCount: 0
      })
    }

    // 删除过期令牌
    const deleteResult = await prisma.verificationToken.deleteMany({
      where: {
        expires: {
          lt: now
        }
      }
    })

    console.log(`✅ 成功删除 ${deleteResult.count} 个过期令牌`)

    return NextResponse.json({
      success: true,
      message: `成功清理 ${deleteResult.count} 个过期令牌`,
      deletedCount: deleteResult.count,
      expiredTokens: expiredTokens.map((token: VerificationToken) => ({
        identifier: token.identifier,
        expires: token.expires,
        expiredHours: Math.floor((now.getTime() - token.expires.getTime()) / (1000 * 60 * 60))
      }))
    })

  } catch (error) {
    console.error('❌ 清理过期令牌失败:', error)
    return NextResponse.json(
      { error: '清理过期令牌失败' },
      { status: 500 }
    )
  }
}

// 获取过期令牌统计信息
export async function GET(request: NextRequest) {
  console.log('📊 获取过期令牌统计信息')

  try {
    const now = new Date()
    
    // 统计过期令牌
    const expiredCount = await prisma.verificationToken.count({
      where: {
        expires: {
          lt: now
        }
      }
    })

    // 统计有效令牌
    const validCount = await prisma.verificationToken.count({
      where: {
        expires: {
          gte: now
        }
      }
    })

    // 获取最近过期的令牌信息
    const recentExpired = await prisma.verificationToken.findMany({
      where: {
        expires: {
          lt: now,
          gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) // 最近7天过期的
        }
      },
      orderBy: {
        expires: 'desc'
      },
      take: 10
    })

    return NextResponse.json({
      success: true,
      statistics: {
        expiredCount,
        validCount,
        totalCount: expiredCount + validCount,
        recentExpired: recentExpired.map((token: VerificationToken) => ({
          identifier: token.identifier,
          expires: token.expires,
          expiredHours: Math.floor((now.getTime() - token.expires.getTime()) / (1000 * 60 * 60))
        }))
      }
    })

  } catch (error) {
    console.error('❌ 获取令牌统计信息失败:', error)
    return NextResponse.json(
      { error: '获取统计信息失败' },
      { status: 500 }
    )
  }
}
```

## 🛠️ 预防措施

### 1. 创建类型定义文件

创建 `types/prisma.ts` 文件：

```typescript
// types/prisma.ts
export type VerificationToken = {
  identifier: string
  token: string
  expires: Date
}

export type User = {
  id: string
  email: string | null
  name: string | null
  // ... 其他字段
}

// 从 Prisma 查询结果推断类型的工具类型
export type PrismaModel<T> = T extends (...args: any[]) => Promise<infer U> ? U : never
```

### 2. 统一导入方式

```typescript
// 统一从类型文件导入
import type { VerificationToken, User } from '@/types/prisma'
```

### 3. Prisma 版本管理

确保 `package.json` 中的 Prisma 版本一致：

```json
{
  "dependencies": {
    "@prisma/client": "^5.7.1"
  },
  "devDependencies": {
    "prisma": "^5.7.1"
  }
}
```

## 🔍 故障排除

### 问题 1: Prisma 客户端未生成

```bash
# 重新生成客户端
npx prisma generate

# 检查生成的类型
ls -la node_modules/.prisma/client/
```

### 问题 2: 版本不匹配

```bash
# 检查版本
npx prisma --version
npm list @prisma/client

# 更新到最新版本
npm update @prisma/client prisma
```

### 问题 3: 缓存问题

```bash
# 清理缓存
rm -rf node_modules/.prisma
rm -rf .next
npm install
npx prisma generate
npm run build
```

## 🎯 最佳实践

### 1. 类型安全

```typescript
// 使用严格的类型定义
type VerificationTokenWithMeta = VerificationToken & {
  expiredHours: number
  isExpired: boolean
}
```

### 2. 错误处理

```typescript
// 添加类型守卫
function isVerificationToken(obj: any): obj is VerificationToken {
  return obj && 
         typeof obj.identifier === 'string' &&
         typeof obj.token === 'string' &&
         obj.expires instanceof Date
}
```

### 3. 文档化

```typescript
/**
 * 验证令牌类型定义
 * 对应 Prisma schema 中的 VerificationToken 模型
 */
type VerificationToken = {
  /** 用户标识符（通常是邮箱） */
  identifier: string
  /** 验证令牌 */
  token: string
  /** 过期时间 */
  expires: Date
}
```

## ✅ 验证成功

修复后的构建结果：

```bash
npm run build
# ✓ Compiled successfully
# ✓ Linting and checking validity of types
# ✓ Collecting page data
# ✓ Generating static pages (37/37)
# ✓ Finalizing page optimization
```

---

**总结**：通过使用自定义类型定义而不是依赖 Prisma 客户端的类型导出，成功解决了类型导入问题。这种方法更稳定，不受 Prisma 版本变化影响，同时保持了类型安全。
