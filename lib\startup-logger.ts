/**
 * 启动日志记录器
 * 在应用启动时记录环境变量和配置信息
 */

interface EnvConfig {
  key: string
  category: string
  required: boolean
  sensitive?: boolean
  description?: string
}

// 环境变量配置定义
const ENV_CONFIGS: EnvConfig[] = [
  // 应用配置
  { key: 'NODE_ENV', category: '应用', required: true, description: '运行环境' },
  { key: 'PORT', category: '应用', required: false, description: '应用端口' },
  { key: 'NEXT_PUBLIC_APP_URL', category: '应用', required: false, description: '应用URL' },
  { key: 'NEXT_PUBLIC_APP_NAME', category: '应用', required: false, description: '应用名称' },
  
  // 认证配置
  { key: 'NEXTAUTH_URL', category: '认证', required: true, description: 'NextAuth URL' },
  { key: 'NEXTAUTH_SECRET', category: '认证', required: true, sensitive: true, description: 'NextAuth 密钥' },
  { key: 'AUTH_TRUST_HOST', category: '认证', required: false, description: '信任主机' },
  { key: 'NEXTAUTH_URL_INTERNAL', category: '认证', required: false, description: '内部URL' },
  
  // 数据库配置
  { key: 'DATABASE_URL', category: '数据库', required: true, sensitive: true, description: '数据库连接' },
  { key: 'REDIS_URL', category: '数据库', required: false, sensitive: true, description: 'Redis连接' },
  
  // 邮件配置
  { key: 'RESEND_API_KEY', category: '邮件', required: false, sensitive: true, description: 'Resend API密钥' },
  { key: 'EMAIL_FROM', category: '邮件', required: false, description: '发件人邮箱' },
  
  // 第三方服务
  { key: 'GOOGLE_CLIENT_ID', category: '第三方', required: false, description: 'Google客户端ID' },
  { key: 'GOOGLE_CLIENT_SECRET', category: '第三方', required: false, sensitive: true, description: 'Google客户端密钥' },
  { key: 'NEXT_PUBLIC_FINGERPRINT_JS_API_KEY', category: '第三方', required: false, sensitive: true, description: '指纹识别API' },
  
  // 安全配置
  { key: 'JWT_SECRET', category: '安全', required: false, sensitive: true, description: 'JWT密钥' },
  { key: 'ENCRYPTION_KEY', category: '安全', required: false, sensitive: true, description: '加密密钥' },
  
  // 功能开关
  { key: 'ENABLE_REGISTRATION', category: '功能', required: false, description: '启用注册' },
  { key: 'ENABLE_EMAIL_VERIFICATION', category: '功能', required: false, description: '启用邮箱验证' },
  { key: 'ALLOW_EMAIL_LOGIN', category: '功能', required: false, description: '允许邮箱登录' },
  { key: 'ALLOW_USERNAME_LOGIN', category: '功能', required: false, description: '允许用户名登录' }
]

// 颜色常量
const COLORS = {
  RESET: '\x1b[0m',
  BRIGHT: '\x1b[1m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m'
} as const

function colorize(color: keyof typeof COLORS, text: string): string {
  if (process.env.NODE_ENV === 'production' && !process.env.ENABLE_COLOR_LOGS) {
    return text
  }
  return `${COLORS[color]}${text}${COLORS.RESET}`
}

function maskSensitiveValue(value: string | undefined, sensitive: boolean = false): string {
  if (!value) {
    return colorize('RED', '未设置')
  }
  
  if (sensitive) {
    if (value.length <= 8) {
      return colorize('GREEN', '***已设置***')
    }
    return colorize('GREEN', `${value.substring(0, 4)}...${value.substring(value.length - 4)}`)
  }
  
  return colorize('GREEN', value)
}

export function logStartupInfo(): void {
  const startTime = new Date()
  
  console.log('')
  console.log(colorize('CYAN', '🚀 MapleStory 信息站启动'))
  console.log('='.repeat(60))
  
  // 基本信息
  console.log(colorize('BRIGHT', '📋 启动信息:'))
  console.log(`   时间: ${startTime.toLocaleString('zh-CN')}`)
  console.log(`   环境: ${colorize('YELLOW', process.env.NODE_ENV || 'development')}`)
  console.log(`   Node.js: ${process.version}`)
  console.log(`   平台: ${process.platform} ${process.arch}`)
  console.log(`   进程ID: ${process.pid}`)
  console.log('')
  
  // 环境变量检查
  console.log(colorize('BRIGHT', '🔧 环境变量配置:'))
  console.log('')
  
  // 按分类分组
  const categories = ENV_CONFIGS.reduce((acc, config) => {
    if (!acc[config.category]) {
      acc[config.category] = []
    }
    acc[config.category].push(config)
    return acc
  }, {} as Record<string, EnvConfig[]>)
  
  let totalRequired = 0
  let configuredRequired = 0
  const missingRequired: string[] = []
  
  Object.entries(categories).forEach(([category, configs]) => {
    console.log(colorize('YELLOW', `📂 ${category}:`))
    
    configs.forEach(config => {
      const value = process.env[config.key]
      const displayValue = maskSensitiveValue(value, config.sensitive)
      const requiredMark = config.required ? colorize('RED', ' *') : ''
      const description = config.description ? ` - ${config.description}` : ''
      
      console.log(`   ${config.key}${requiredMark}: ${displayValue}${description}`)
      
      if (config.required) {
        totalRequired++
        if (value) {
          configuredRequired++
        } else {
          missingRequired.push(config.key)
        }
      }
    })
    console.log('')
  })
  
  // 配置统计
  console.log(colorize('BRIGHT', '📊 配置统计:'))
  console.log(`   必需配置: ${colorize('CYAN', `${configuredRequired}/${totalRequired}`)}`)
  console.log(`   完成度: ${colorize('CYAN', `${Math.round(configuredRequired / totalRequired * 100)}%`)}`)
  console.log('')
  
  // 警告信息
  if (missingRequired.length > 0) {
    console.log(colorize('RED', '⚠️  警告: 缺少必需的环境变量:'))
    missingRequired.forEach(key => {
      console.log(colorize('RED', `   - ${key}`))
    })
    console.log('')
  }
  
  // 网络配置
  if (process.env.PORT || process.env.NEXTAUTH_URL) {
    console.log(colorize('BRIGHT', '🌐 网络配置:'))
    if (process.env.PORT) {
      console.log(`   本地端口: ${colorize('CYAN', process.env.PORT)}`)
    }
    if (process.env.NEXTAUTH_URL) {
      console.log(`   外部URL: ${colorize('CYAN', process.env.NEXTAUTH_URL)}`)
    }
    console.log('')
  }
  
  // 功能状态
  const features = [
    { key: 'ENABLE_REGISTRATION', name: '用户注册' },
    { key: 'ENABLE_EMAIL_VERIFICATION', name: '邮箱验证' },
    { key: 'ALLOW_EMAIL_LOGIN', name: '邮箱登录' },
    { key: 'ALLOW_USERNAME_LOGIN', name: '用户名登录' }
  ]
  
  const enabledFeatures = features.filter(f => process.env[f.key] === 'true')
  
  if (enabledFeatures.length > 0) {
    console.log(colorize('BRIGHT', '🎮 启用的功能:'))
    enabledFeatures.forEach(feature => {
      console.log(`   ✅ ${feature.name}`)
    })
    console.log('')
  }
  
  // 完成信息
  if (missingRequired.length === 0) {
    console.log(colorize('GREEN', '✅ 环境配置检查完成，应用启动中...'))
  } else {
    console.log(colorize('YELLOW', '⚠️  环境配置存在问题，但应用将尝试启动...'))
  }
  
  console.log('')
}

export function logShutdownInfo(): void {
  console.log('')
  console.log(colorize('YELLOW', '📴 MapleStory 信息站正在关闭...'))
  console.log(`   关闭时间: ${new Date().toLocaleString('zh-CN')}`)
  console.log('')
}

// 防止重复执行的标志
let hasLoggedStartup = false

// 在服务器端自动调用（仅执行一次）
if (typeof window === 'undefined' &&
    process.env.NODE_ENV !== 'test' &&
    !hasLoggedStartup &&
    process.env.NODE_ENV !== 'production') { // 生产构建时不执行

  hasLoggedStartup = true

  // 延迟执行，确保所有环境变量都已加载
  setTimeout(() => {
    logStartupInfo()
  }, 100)

  // 监听进程退出事件
  process.on('SIGINT', logShutdownInfo)
  process.on('SIGTERM', logShutdownInfo)
}
