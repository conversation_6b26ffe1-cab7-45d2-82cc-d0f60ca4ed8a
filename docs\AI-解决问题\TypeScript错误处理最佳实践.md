# TypeScript 错误处理最佳实践

## 🔍 遇到的问题

在 TypeScript 中，`catch` 块中的 `error` 参数类型是 `unknown`，不能直接访问 `.message` 属性：

```typescript
// ❌ 错误写法
catch (error) {
  setDebugInfo({ error: error.message }) // Type error: 'error' is of type 'unknown'
}
```

## ✅ 正确的解决方案

### 方案一：类型守卫检查（推荐）

```typescript
// ✅ 正确写法
catch (error) {
  setDebugInfo({ 
    error: error instanceof Error ? error.message : String(error) 
  })
}
```

### 方案二：类型断言

```typescript
catch (error) {
  setDebugInfo({ 
    error: (error as Error).message 
  })
}
```

### 方案三：完整的错误处理函数

```typescript
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message
  }
  
  if (typeof error === 'string') {
    return error
  }
  
  if (error && typeof error === 'object' && 'message' in error) {
    return String(error.message)
  }
  
  return 'Unknown error occurred'
}

// 使用
catch (error) {
  setDebugInfo({ error: getErrorMessage(error) })
}
```

## 🛠️ 创建通用错误处理工具

### 1. 错误处理工具函数

```typescript
// lib/error-utils.ts
export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message
  }
  
  if (typeof error === 'string') {
    return error
  }
  
  if (error && typeof error === 'object' && 'message' in error) {
    return String(error.message)
  }
  
  return 'An unknown error occurred'
}

export function getErrorDetails(error: unknown): {
  message: string
  name?: string
  stack?: string
} {
  if (error instanceof Error) {
    return {
      message: error.message,
      name: error.name,
      stack: error.stack
    }
  }
  
  return {
    message: getErrorMessage(error)
  }
}

export function logError(error: unknown, context?: string): void {
  const details = getErrorDetails(error)
  console.error(`Error${context ? ` in ${context}` : ''}:`, details)
}
```

### 2. React 错误边界

```typescript
// components/ErrorBoundary.tsx
import React from 'react'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

export class ErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h2>Something went wrong.</h2>
          <details>
            <summary>Error details</summary>
            <pre>{this.state.error?.message}</pre>
          </details>
        </div>
      )
    }

    return this.props.children
  }
}
```

### 3. 异步错误处理 Hook

```typescript
// hooks/useAsyncError.ts
import { useState, useCallback } from 'react'
import { getErrorMessage } from '@/lib/error-utils'

export function useAsyncError() {
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)

  const execute = useCallback(async <T>(
    asyncFunction: () => Promise<T>
  ): Promise<T | null> => {
    try {
      setLoading(true)
      setError(null)
      const result = await asyncFunction()
      return result
    } catch (err) {
      const errorMessage = getErrorMessage(err)
      setError(errorMessage)
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  return { error, loading, execute, clearError }
}
```

## 📋 在项目中应用

### 1. 更新调试页面

```typescript
// app/debug-auth/page.tsx
import { getErrorMessage } from '@/lib/error-utils'

const fetchDebugInfo = async () => {
  try {
    // ... 原有代码
  } catch (error) {
    console.error('获取调试信息失败:', error)
    setDebugInfo({ 
      error: getErrorMessage(error) 
    })
  }
}
```

### 2. API 路由错误处理

```typescript
// app/api/debug/env/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { getErrorMessage } from '@/lib/error-utils'

export async function GET(request: NextRequest) {
  try {
    // ... 原有代码
  } catch (error) {
    return NextResponse.json(
      { error: getErrorMessage(error) },
      { status: 500 }
    )
  }
}
```

### 3. 组件错误处理

```typescript
// components/SomeComponent.tsx
import { useAsyncError } from '@/hooks/useAsyncError'

export function SomeComponent() {
  const { error, loading, execute, clearError } = useAsyncError()

  const handleAction = async () => {
    const result = await execute(async () => {
      // 可能抛出错误的异步操作
      const response = await fetch('/api/some-endpoint')
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return response.json()
    })

    if (result) {
      // 处理成功结果
    }
  }

  return (
    <div>
      {error && (
        <div className="error-message">
          {error}
          <button onClick={clearError}>Clear</button>
        </div>
      )}
      <button onClick={handleAction} disabled={loading}>
        {loading ? 'Loading...' : 'Execute'}
      </button>
    </div>
  )
}
```

## 🔧 TypeScript 配置优化

### 1. 严格模式配置

```json
// tsconfig.json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "useUnknownInCatchVariables": true
  }
}
```

### 2. ESLint 规则

```json
// .eslintrc.json
{
  "rules": {
    "@typescript-eslint/no-explicit-any": "error",
    "@typescript-eslint/no-unsafe-assignment": "error",
    "@typescript-eslint/no-unsafe-member-access": "error",
    "@typescript-eslint/no-unsafe-call": "error"
  }
}
```

## 🎯 最佳实践总结

### 1. 错误处理原则

- ✅ 总是使用类型守卫检查 `error instanceof Error`
- ✅ 提供有意义的错误消息
- ✅ 记录错误详情用于调试
- ✅ 优雅地处理错误状态

### 2. 避免的做法

- ❌ 直接访问 `error.message` 而不检查类型
- ❌ 使用 `any` 类型绕过类型检查
- ❌ 忽略错误或静默失败
- ❌ 向用户显示技术性错误消息

### 3. 推荐的模式

```typescript
// ✅ 推荐模式
try {
  await riskyOperation()
} catch (error) {
  // 记录详细错误信息
  console.error('Operation failed:', error)
  
  // 向用户显示友好消息
  setUserMessage('操作失败，请稍后重试')
  
  // 设置错误状态
  setError(getErrorMessage(error))
}
```

## 🚀 构建验证

修复后，确保两种构建方式都能成功：

```bash
# npm 构建
npm run build

# pnpm 构建  
pnpm run build
```

---

**总结**：通过正确的 TypeScript 错误处理模式，可以避免类型错误并提供更好的用户体验。关键是使用类型守卫和创建可复用的错误处理工具函数。
