/**
 * 认证配置管理
 * 统一管理所有认证方式的启用/禁用状态
 */

export type AuthProviderType = 'credentials' | 'email' | 'google' | 'username'

export interface AuthConfig {
  enabledProviders: AuthProviderType[]
  allowEmailLogin: boolean
  allowUsernameLogin: boolean
  requireEmailVerification: boolean
  googleOAuth: {
    enabled: boolean
    clientId?: string
    clientSecret?: string
  }
}

// 默认认证配置
const defaultAuthConfig: AuthConfig = {
  enabledProviders: ['credentials', 'email'],
  allowEmailLogin: true,
  allowUsernameLogin: false,
  requireEmailVerification: true,
  googleOAuth: {
    enabled: false,
  }
}

// 从环境变量读取配置
export function getAuthConfig(): AuthConfig {
  const config: AuthConfig = {
    ...defaultAuthConfig,
    enabledProviders: (process.env.ENABLED_AUTH_PROVIDERS?.split(',') as AuthProviderType[]) || defaultAuthConfig.enabledProviders,
    allowEmailLogin: process.env.ALLOW_EMAIL_LOGIN !== 'false',
    allowUsernameLogin: process.env.ALLOW_USERNAME_LOGIN === 'true',
    requireEmailVerification: process.env.REQUIRE_EMAIL_VERIFICATION !== 'false',
    googleOAuth: {
      enabled: process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET ? true : false,
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }
  }

  // 如果启用了 Google OAuth，自动添加到启用列表
  if (config.googleOAuth.enabled && !config.enabledProviders.includes('google')) {
    config.enabledProviders.push('google')
  }

  // 如果启用了用户名登录，自动添加到启用列表
  if (config.allowUsernameLogin && !config.enabledProviders.includes('username')) {
    config.enabledProviders.push('username')
  }

  return config
}

// 检查特定认证方式是否启用
export function isAuthProviderEnabled(provider: AuthProviderType): boolean {
  const config = getAuthConfig()
  return config.enabledProviders.includes(provider)
}

// 获取启用的认证方式列表
export function getEnabledAuthProviders(): AuthProviderType[] {
  return getAuthConfig().enabledProviders
}

// 验证认证配置
export function validateAuthConfig(): { valid: boolean; errors: string[] } {
  const config = getAuthConfig()
  const errors: string[] = []

  // 至少需要启用一种认证方式
  if (config.enabledProviders.length === 0) {
    errors.push('至少需要启用一种认证方式')
  }

  // 如果启用了 Google OAuth，检查必需的环境变量
  if (config.enabledProviders.includes('google')) {
    if (!config.googleOAuth.clientId) {
      errors.push('启用 Google OAuth 需要设置 GOOGLE_CLIENT_ID 环境变量')
    }
    if (!config.googleOAuth.clientSecret) {
      errors.push('启用 Google OAuth 需要设置 GOOGLE_CLIENT_SECRET 环境变量')
    }
  }

  // 如果启用了邮箱或用户名登录，需要启用 credentials
  if ((config.allowEmailLogin || config.allowUsernameLogin) && !config.enabledProviders.includes('credentials')) {
    errors.push('启用邮箱或用户名登录需要同时启用 credentials 认证方式')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

// 获取登录字段配置
export function getLoginFieldConfig() {
  const config = getAuthConfig()
  
  return {
    showEmailField: config.allowEmailLogin,
    showUsernameField: config.allowUsernameLogin,
    showGoogleButton: config.googleOAuth.enabled,
    emailPlaceholder: config.allowEmailLogin && config.allowUsernameLogin 
      ? '邮箱地址或用户名' 
      : config.allowEmailLogin 
        ? '邮箱地址' 
        : '用户名',
    loginFieldLabel: config.allowEmailLogin && config.allowUsernameLogin 
      ? '邮箱/用户名' 
      : config.allowEmailLogin 
        ? '邮箱地址' 
        : '用户名'
  }
}

// 认证方式显示名称映射
export const AUTH_PROVIDER_NAMES: Record<AuthProviderType, string> = {
  credentials: '账号密码',
  email: '邮箱登录',
  google: 'Google 登录',
  username: '用户名登录'
}

// 获取认证方式的显示信息
export function getAuthProviderInfo(provider: AuthProviderType) {
  return {
    name: AUTH_PROVIDER_NAMES[provider],
    enabled: isAuthProviderEnabled(provider)
  }
}
