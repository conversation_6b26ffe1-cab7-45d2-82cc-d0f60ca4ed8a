#!/bin/bash

# 冒险岛情报站 - Rocky Linux 9.5 环境初始化脚本
# 专注于应用运行环境准备，不包括数据库等基础服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
APP_USER="maplestory"
NODE_VERSION="18"
DOMAIN=""
USE_NGINX=false

# 参数解析
while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--domain)
            DOMAIN="$2"
            shift 2
            ;;
        --with-nginx)
            USE_NGINX=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
    exit 1
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO:${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Rocky Linux 9.5 冒险岛情报站环境初始化脚本"
    echo ""
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -d, --domain DOMAIN    设置域名"
    echo "  --with-nginx           同时配置 Nginx"
    echo "  -h, --help             显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                           # 基础环境初始化"
    echo "  $0 -d example.com            # 指定域名"
    echo "  $0 --with-nginx -d example.com  # 包含 Nginx 配置"
}

# 检查root权限
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "此脚本需要root权限运行"
    fi
}

# 检查系统版本
check_system() {
    log "检查系统版本..."
    
    if ! grep -q "Rocky Linux release 9" /etc/redhat-release 2>/dev/null; then
        warning "此脚本专为 Rocky Linux 9.5 设计"
    fi
    
    log "系统版本检查完成"
}

# 更新系统
update_system() {
    log "更新系统包..."
    
    dnf update -y
    dnf install -y curl wget git unzip tar gzip bc
    
    log "系统更新完成"
}

# 配置防火墙
setup_firewall() {
    log "配置防火墙..."
    
    # 启用防火墙
    systemctl enable --now firewalld
    
    if [ "$USE_NGINX" = true ]; then
        # 使用 Nginx 时开放 80 和 443 端口
        firewall-cmd --permanent --add-service=http
        firewall-cmd --permanent --add-service=https
        info "已开放 HTTP(80) 和 HTTPS(443) 端口"
    else
        # 直接暴露应用端口
        firewall-cmd --permanent --add-port=3000/tcp
        info "已开放应用端口 3000"
    fi
    
    # 重新加载防火墙规则
    firewall-cmd --reload
    
    # 显示当前规则
    firewall-cmd --list-all
    
    log "防火墙配置完成"
}

# 创建应用用户
create_app_user() {
    log "创建应用用户..."
    
    if ! id "$APP_USER" &>/dev/null; then
        useradd -m -s /bin/bash "$APP_USER"
        log "用户 $APP_USER 创建成功"
    else
        info "用户 $APP_USER 已存在"
    fi
    
    # 创建必要目录
    sudo -u "$APP_USER" mkdir -p /home/<USER>/{app,logs,backups}
    
    # 设置目录权限
    chown -R $APP_USER:$APP_USER /home/<USER>/
    
    log "应用用户配置完成"
}

# 安装 Node.js
install_nodejs() {
    log "安装 Node.js $NODE_VERSION..."
    
    # 检查是否已安装
    if command -v node >/dev/null 2>&1; then
        local current_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$current_version" -ge "$NODE_VERSION" ]; then
            info "Node.js 已安装，版本: $(node --version)"
            return
        fi
    fi
    
    # 添加 NodeSource 仓库
    curl -fsSL https://rpm.nodesource.com/setup_${NODE_VERSION}.x | bash -
    
    # 安装 Node.js
    dnf install -y nodejs
    
    # 验证安装
    node --version
    npm --version
    
    log "Node.js 安装完成"
}

# 安装 PM2
install_pm2() {
    log "安装 PM2 进程管理器..."
    
    # 检查是否已安装
    if command -v pm2 >/dev/null 2>&1; then
        info "PM2 已安装，版本: $(pm2 --version)"
        return
    fi
    
    # 全局安装 PM2
    npm install -g pm2
    
    # 验证安装
    pm2 --version
    
    log "PM2 安装完成"
}

# 安装 Nginx（可选）
install_nginx() {
    if [ "$USE_NGINX" = false ]; then
        info "跳过 Nginx 安装"
        return
    fi
    
    log "安装 Nginx..."
    
    # 检查是否已安装
    if command -v nginx >/dev/null 2>&1; then
        info "Nginx 已安装"
        return
    fi
    
    # 安装 Nginx
    dnf install -y nginx
    
    # 启动并启用 Nginx
    systemctl enable --now nginx
    
    # 删除默认配置
    rm -f /etc/nginx/conf.d/default.conf
    
    log "Nginx 安装完成"
}

# 创建 Nginx 配置（可选）
create_nginx_config() {
    if [ "$USE_NGINX" = false ] || [ -z "$DOMAIN" ]; then
        info "跳过 Nginx 配置创建"
        return
    fi
    
    log "创建 Nginx 配置..."
    
    cat > "/etc/nginx/conf.d/maplestory.conf" << EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 静态文件缓存
    location /_next/static/ {
        alias /home/<USER>/app/.next/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location /images/ {
        alias /home/<USER>/app/public/images/;
        expires 1y;
        add_header Cache-Control "public";
    }
    
    # 主应用代理
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\\n";
        add_header Content-Type text/plain;
    }
}
EOF
    
    # 测试 Nginx 配置
    nginx -t
    
    # 重新加载 Nginx
    systemctl reload nginx
    
    log "Nginx 配置创建完成"
}

# 配置系统优化
setup_system_optimization() {
    log "配置系统优化..."
    
    # 增加文件描述符限制
    cat >> /etc/security/limits.conf << EOF

# 冒险岛情报站应用优化
$APP_USER soft nofile 65536
$APP_USER hard nofile 65536
EOF
    
    # 配置内核参数
    cat >> /etc/sysctl.conf << EOF

# 冒险岛情报站网络优化
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.core.netdev_max_backlog = 5000
EOF
    
    # 应用内核参数
    sysctl -p
    
    log "系统优化配置完成"
}

# 创建监控脚本
create_monitoring_script() {
    log "创建监控脚本..."
    
    cat > "/home/<USER>/monitor.sh" << 'EOF'
#!/bin/bash

LOG_FILE="/home/<USER>/logs/monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# 检查应用状态
if ! pm2 list | grep -q "online"; then
    echo "[$DATE] 应用离线，尝试重启..." >> $LOG_FILE
    pm2 restart maplestory-info-station
fi

# 检查应用响应
if ! curl -f http://localhost:3000/health >/dev/null 2>&1; then
    echo "[$DATE] 应用健康检查失败" >> $LOG_FILE
    pm2 restart maplestory-info-station
fi

# 检查磁盘空间
DISK_USAGE=$(df /home | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "[$DATE] 磁盘空间不足: ${DISK_USAGE}%" >> $LOG_FILE
fi

# 检查内存使用
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.2f", $3*100/$2}')
if (( $(echo "$MEMORY_USAGE > 90" | bc -l) )); then
    echo "[$DATE] 内存使用过高: ${MEMORY_USAGE}%" >> $LOG_FILE
fi
EOF
    
    # 设置权限
    chown $APP_USER:$APP_USER /home/<USER>/monitor.sh
    chmod +x /home/<USER>/monitor.sh
    
    log "监控脚本创建完成"
}

# 配置日志轮转
setup_log_rotation() {
    log "配置日志轮转..."
    
    cat > "/etc/logrotate.d/maplestory" << EOF
/home/<USER>/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $APP_USER $APP_USER
    postrotate
        sudo -u $APP_USER pm2 reloadLogs
    endscript
}
EOF
    
    log "日志轮转配置完成"
}

# 创建环境变量模板
create_env_template() {
    log "创建环境变量模板..."
    
    local app_url="http://localhost:3000"
    if [ -n "$DOMAIN" ]; then
        app_url="https://$DOMAIN"
    fi
    
    cat > "/home/<USER>/app/.env.production.template" << EOF
# 数据库配置（请根据实际情况修改）
DATABASE_URL="postgresql://username:password@localhost:5432/mxd_info_db"
REDIS_URL="redis://localhost:6379"

# NextAuth配置
NEXTAUTH_URL="$app_url"
NEXTAUTH_SECRET="$(openssl rand -base64 32)"

# 邮件服务 (需要配置)
RESEND_API_KEY="re_your-production-resend-api-key"
EMAIL_FROM="noreply@${DOMAIN:-localhost}"

# 第三方服务 (需要配置)
NEXT_PUBLIC_FINGERPRINT_JS_API_KEY="your-production-fingerprint-js-key"
STRIPE_SECRET_KEY="sk_live_your-production-stripe-key"
STRIPE_WEBHOOK_SECRET="whsec_your-production-webhook-secret"

# 安全配置
JWT_SECRET="$(openssl rand -base64 32)"
ENCRYPTION_KEY="$(openssl rand -base64 32)"

# 应用配置
NEXT_PUBLIC_APP_URL="$app_url"
NEXT_PUBLIC_APP_NAME="冒险岛情报站"

# 生产配置
NODE_ENV="production"
LOG_LEVEL="info"
PORT="3000"

# 虚拟货币配置
CURRENCY_NAME="欢乐豆"
DEFAULT_CURRENCY_AMOUNT="100"

# 功能开关
ENABLE_REGISTRATION="true"
ENABLE_EMAIL_VERIFICATION="true"
ENABLE_FINGERPRINT_TRACKING="true"
EOF
    
    chown $APP_USER:$APP_USER /home/<USER>/app/.env.production.template
    
    log "环境变量模板创建完成"
}

# 显示完成信息
show_completion_info() {
    log "Rocky Linux 9.5 环境初始化完成！"
    echo ""
    echo "=== 环境信息 ==="
    echo "操作系统: $(cat /etc/redhat-release)"
    echo "Node.js 版本: $(node --version)"
    echo "npm 版本: $(npm --version)"
    echo "PM2 版本: $(pm2 --version)"
    if [ "$USE_NGINX" = true ]; then
        echo "Nginx 版本: $(nginx -v 2>&1 | cut -d' ' -f3)"
    fi
    echo ""
    echo "=== 应用配置 ==="
    echo "应用用户: $APP_USER"
    echo "应用目录: /home/<USER>/app"
    echo "日志目录: /home/<USER>/logs"
    echo "备份目录: /home/<USER>/backups"
    echo ""
    echo "=== 下一步操作 ==="
    echo "1. 切换到应用用户: sudo su - $APP_USER"
    echo "2. 克隆应用代码到 /home/<USER>/app"
    echo "3. 复制并编辑环境变量: cp .env.production.template .env.production"
    echo "4. 运行部署脚本: ./scripts/rocky-deploy.sh"
    echo ""
    if [ "$USE_NGINX" = true ] && [ -n "$DOMAIN" ]; then
        echo "网站地址: http://$DOMAIN"
        echo "注意: 如需 HTTPS，请手动配置 SSL 证书"
    else
        echo "应用地址: http://your-server-ip:3000"
    fi
    echo ""
    echo "=== 监控和维护 ==="
    echo "监控脚本: /home/<USER>/monitor.sh"
    echo "设置定时监控: sudo -u $APP_USER crontab -e"
    echo "添加: */5 * * * * /home/<USER>/monitor.sh"
}

# 主函数
main() {
    log "开始 Rocky Linux 9.5 环境初始化..."
    
    check_root
    check_system
    update_system
    setup_firewall
    create_app_user
    install_nodejs
    install_pm2
    install_nginx
    create_nginx_config
    setup_system_optimization
    create_monitoring_script
    setup_log_rotation
    create_env_template
    show_completion_info
}

# 执行主函数
main "$@"
