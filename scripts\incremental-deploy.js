#!/usr/bin/env node

/**
 * 统一增量部署工具
 * 整合多种增量打包方式
 */

const fs = require('fs')
const path = require('path')
const { createIncrementalPackage } = require('./create-incremental-package')
const { createQuickUpdate, updateTemplates } = require('./quick-update')
const { createGitIncrementalPackage } = require('./git-incremental-package')

function showHelp() {
  console.log('🚀 增量部署工具')
  console.log('')
  console.log('用法:')
  console.log('  node scripts/incremental-deploy.js <模式> [选项]')
  console.log('')
  console.log('模式:')
  console.log('  hash        - 基于文件哈希的增量包（检测所有变更）')
  console.log('  git         - 基于 Git 提交的增量包')
  console.log('  quick       - 快速更新包（预定义模板）')
  console.log('  custom      - 自定义文件更新')
  console.log('')
  console.log('示例:')
  console.log('  node scripts/incremental-deploy.js hash')
  console.log('  node scripts/incremental-deploy.js git HEAD~2 HEAD')
  console.log('  node scripts/incremental-deploy.js quick nextauth')
  console.log('  node scripts/incremental-deploy.js custom lib/auth.ts .env.local')
  console.log('')
  console.log('快速更新模板:')
  Object.keys(updateTemplates).forEach(key => {
    console.log(`  ${key.padEnd(12)} - ${updateTemplates[key].name}`)
  })
}

function createCurrentAuthUpdate() {
  console.log('🔧 创建当前 NextAuth 修复更新包...')

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19)
  const outputBaseDir = path.join('..', 'maplestory-incremental-updates')
  const packageDir = path.join(outputBaseDir, `nextauth-fix-${timestamp}`)

  // 确保输出目录存在
  if (!fs.existsSync(outputBaseDir)) {
    fs.mkdirSync(outputBaseDir, { recursive: true })
  }
  
  // 创建输出目录
  fs.mkdirSync(packageDir, { recursive: true })
  
  // 要更新的文件列表
  const filesToUpdate = [
    'lib/auth.ts',
    '.env.local'
  ]
  
  // 复制文件
  console.log('📁 复制修复文件...')
  filesToUpdate.forEach(file => {
    if (fs.existsSync(file)) {
      const destPath = path.join(packageDir, file)
      const destDir = path.dirname(destPath)
      
      if (!fs.existsSync(destDir)) {
        fs.mkdirSync(destDir, { recursive: true })
      }
      
      fs.copyFileSync(file, destPath)
      console.log(`   ✓ ${file}`)
    }
  })
  
  // 创建环境变量模板
  const envTemplate = `# NextAuth 反向代理修复配置
DATABASE_URL="postgresql://postgres:password@localhost:5432/mxd_info_db"

# NextAuth 配置 - 修复反向代理问题
NEXTAUTH_URL="https://mxd.hyhuman.xyz"
NEXTAUTH_SECRET="$(openssl rand -base64 32)"

# 信任代理配置 - 关键！
AUTH_TRUST_HOST="true"
NEXTAUTH_URL_INTERNAL="http://localhost:3000"
NEXTAUTH_COOKIE_SECURE="true"

# 应用配置
NODE_ENV="production"
PORT="3000"

# Prisma 配置
PRISMA_QUERY_ENGINE_LIBRARY="/root/svc/mxd/mxd-tool/node_modules/.prisma/client/libquery_engine-rhel-openssl-1.0.x.so.node"
PRISMA_QUERY_ENGINE_BINARY="/root/svc/mxd/mxd-tool/node_modules/.prisma/client/query-engine-rhel-openssl-1.0.x"
CHECKPOINT_DISABLE="1"
`
  
  fs.writeFileSync(path.join(packageDir, '.env.local.template'), envTemplate)
  
  // 创建更新清单
  const manifest = {
    type: 'nextauth-fix',
    name: 'NextAuth 反向代理修复',
    timestamp,
    files: filesToUpdate,
    needsBuild: false,
    needsRestart: true,
    instructions: [
      '修复 NextAuth.js 反向代理问题',
      '更新环境变量配置',
      '重启应用即可生效'
    ]
  }
  
  fs.writeFileSync(
    path.join(packageDir, 'update-manifest.json'),
    JSON.stringify(manifest, null, 2)
  )
  
  // 创建应用脚本
  const applyScript = `#!/bin/bash

# NextAuth 反向代理修复脚本
echo "🔧 应用 NextAuth 反向代理修复..."
echo ""

# 检查环境
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 备份现有文件
echo "📁 备份现有文件..."
BACKUP_DIR="backup-nextauth-$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

[ -f "lib/auth.ts" ] && cp "lib/auth.ts" "$BACKUP_DIR/"
[ -f ".env.local" ] && cp ".env.local" "$BACKUP_DIR/"

echo "✅ 备份完成: $BACKUP_DIR"

# 停止应用
echo "🛑 停止应用..."
pkill -f "next start" || true
sleep 2

# 更新文件
echo "📋 更新配置文件..."
cp "lib/auth.ts" "lib/auth.ts"

# 处理环境变量
if [ -f ".env.local" ]; then
    echo "⚠️  .env.local 已存在，请手动合并配置"
    echo "参考模板: .env.local.template"
else
    echo "📝 创建 .env.local 文件..."
    cp ".env.local.template" ".env.local"
    echo "⚠️  请编辑 .env.local 文件，配置正确的数据库连接等信息"
fi

echo "✅ 文件更新完成"

# 重启应用
echo "🚀 重启应用..."
npm start &

echo ""
echo "🎉 NextAuth 修复完成!"
echo ""
echo "📋 验证步骤:"
echo "1. 访问 https://mxd.hyhuman.xyz/login"
echo "2. 尝试登录"
echo "3. 测试登出功能"
echo ""
echo "如有问题:"
echo "- 检查 Nginx 配置"
echo "- 查看应用日志"
echo "- 使用备份恢复: cp $BACKUP_DIR/* ./"
`

  fs.writeFileSync(path.join(packageDir, 'apply-nextauth-fix.sh'), applyScript)
  
  try {
    const { execSync } = require('child_process')
    execSync(`chmod +x "${path.join(packageDir, 'apply-nextauth-fix.sh')}"`)
  } catch {}
  
  console.log(`✅ NextAuth 修复包已创建: ${packageDir}`)
  console.log('')
  console.log('📋 使用说明:')
  console.log(`1. 上传 ${packageDir} 目录到服务器`)
  console.log('2. 在服务器上运行: ./apply-nextauth-fix.sh')
  console.log('3. 编辑 .env.local 配置正确的环境变量')
  console.log('4. 测试登录功能')
  
  return packageDir
}

// 主函数
function main() {
  const args = process.argv.slice(2)
  
  if (args.length === 0) {
    showHelp()
    process.exit(1)
  }
  
  const mode = args[0]
  
  switch (mode) {
    case 'hash':
      createIncrementalPackage()
      break
      
    case 'git':
      const fromCommit = args[1] || 'HEAD~1'
      const toCommit = args[2] || 'HEAD'
      createGitIncrementalPackage(fromCommit, toCommit)
      break
      
    case 'quick':
      const template = args[1]
      if (!template) {
        console.log('❌ 请指定快速更新模板')
        console.log('可用模板:', Object.keys(updateTemplates).join(', '))
        process.exit(1)
      }
      createQuickUpdate(template)
      break
      
    case 'custom':
      const customFiles = args.slice(1)
      if (customFiles.length === 0) {
        console.log('❌ 请指定要更新的文件')
        process.exit(1)
      }
      createQuickUpdate('custom', customFiles)
      break
      
    case 'nextauth-fix':
      createCurrentAuthUpdate()
      break
      
    case 'help':
    case '--help':
    case '-h':
      showHelp()
      break
      
    default:
      console.log(`❌ 未知模式: ${mode}`)
      showHelp()
      process.exit(1)
  }
}

if (require.main === module) {
  main()
}
