# 冒险岛情报站 - Docker Compose 配置
# 用于本地开发环境

version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:14-alpine
    container_name: maplestory-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: mxd_info_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - maplestory-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: maplestory-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - maplestory-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Next.js 应用 (开发模式)
  app-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: maplestory-app-dev
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/mxd_info_db
      - REDIS_URL=redis://redis:6379
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=dev-secret-key-at-least-32-characters-long
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - maplestory-network
    profiles:
      - dev

  # Next.js 应用 (生产模式)
  app-prod:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: maplestory-app-prod
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/mxd_info_db
      - REDIS_URL=redis://redis:6379
    env_file:
      - .env.production
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - maplestory-network
    profiles:
      - prod

  # Nginx 反向代理 (生产模式)
  nginx:
    image: nginx:alpine
    container_name: maplestory-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf
      - ./config/nginx-sites:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app-prod
    networks:
      - maplestory-network
    profiles:
      - prod

  # 数据库管理工具 (可选)
  adminer:
    image: adminer:latest
    container_name: maplestory-adminer
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres
    depends_on:
      - postgres
    networks:
      - maplestory-network
    profiles:
      - tools

  # Redis 管理工具 (可选)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: maplestory-redis-commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      REDIS_HOSTS: local:redis:6379
    depends_on:
      - redis
    networks:
      - maplestory-network
    profiles:
      - tools

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  maplestory-network:
    driver: bridge
