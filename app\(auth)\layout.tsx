import { Metadata } from 'next'

export const metadata: Metadata = {
  title: '用户认证 - 冒险岛情报站',
  description: '用户登录、注册和密码重置',
}

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="container mx-auto px-4 pt-8 pb-16">
      <div className="flex flex-col items-center justify-start min-h-[calc(100vh-8rem)]">
        <div className="w-full max-w-md">
          {/*<div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              冒险岛情报站
            </h1>
            <p className="text-gray-600">
              您的专业冒险岛工具平台
            </p>
          </div>*/}
          {children}
        </div>
      </div>
    </div>
  )
}
