/**
 * 装备强化API客户端工具函数
 */

import { 
  EnhancementRequest, 
  EnhancementResponse, 
  EnhancementErrorResponse,
  ApiConfig 
} from '@/types/enhancement-api'

/**
 * 默认API配置
 */
const DEFAULT_CONFIG: ApiConfig = {
  version: '1.0.0',
  baseUrl: '/api/enhancement',
  timeout: 10000,
  retryAttempts: 3,
  rateLimit: {
    requestsPerMinute: 60,
    burstLimit: 10
  }
}

/**
 * API客户端类
 */
export class EnhancementApiClient {
  private config: ApiConfig
  private apiKey?: string

  constructor(config?: Partial<ApiConfig>, apiKey?: string) {
    this.config = { ...DEFAULT_CONFIG, ...config }
    this.apiKey = apiKey
  }

  /**
   * 设置API密钥
   */
  setApiKey(apiKey: string) {
    this.apiKey = apiKey
  }

  /**
   * 执行装备强化
   */
  async enhance(request: EnhancementRequest): Promise<EnhancementResponse> {
    const url = `${this.config.baseUrl}/enhance`
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    }

    if (this.apiKey) {
      headers['X-API-Key'] = this.apiKey
    }

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
        signal: AbortSignal.timeout(this.config.timeout)
      })

      const data = await response.json()

      if (!response.ok) {
        throw new ApiError(data.error || 'API request failed', data.errorCode, data)
      }

      return data as EnhancementResponse
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      throw new ApiError(
        error instanceof Error ? error.message : 'Unknown error',
        'NETWORK_ERROR'
      )
    }
  }

  /**
   * 获取API信息
   */
  async getApiInfo(): Promise<any> {
    const url = `${this.config.baseUrl}/enhance`
    
    try {
      const response = await fetch(url, {
        method: 'GET',
        signal: AbortSignal.timeout(this.config.timeout)
      })

      return await response.json()
    } catch (error) {
      throw new ApiError(
        error instanceof Error ? error.message : 'Failed to get API info',
        'NETWORK_ERROR'
      )
    }
  }
}

/**
 * API错误类
 */
export class ApiError extends Error {
  public readonly errorCode: string
  public readonly details?: any

  constructor(message: string, errorCode: string, details?: any) {
    super(message)
    this.name = 'ApiError'
    this.errorCode = errorCode
    this.details = details
  }
}

/**
 * 创建默认API客户端实例
 */
export const enhancementApi = new EnhancementApiClient()

/**
 * 便捷的强化函数
 */
export async function enhanceEquipment(
  equipmentId: string,
  currentLevel: number,
  enhancementType: 'starforce' | 'potential' | 'bonusstat',
  options?: {
    starcatchEnabled?: boolean
    preventEnabled?: boolean
    minigameBonus?: number
    apiKey?: string
  }
): Promise<EnhancementResponse> {
  const client = new EnhancementApiClient(undefined, options?.apiKey)
  
  return client.enhance({
    equipmentId,
    currentLevel,
    enhancementType,
    starcatchEnabled: options?.starcatchEnabled ?? false,
    preventEnabled: options?.preventEnabled ?? false,
    minigameBonus: options?.minigameBonus ?? 0
  })
}

/**
 * 批量强化函数
 */
export async function batchEnhance(
  requests: EnhancementRequest[],
  options?: {
    stopOnFailure?: boolean
    maxConcurrent?: number
    apiKey?: string
  }
): Promise<EnhancementResponse[]> {
  const client = new EnhancementApiClient(undefined, options?.apiKey)
  const maxConcurrent = options?.maxConcurrent ?? 5
  const stopOnFailure = options?.stopOnFailure ?? false
  
  const results: EnhancementResponse[] = []
  const errors: Error[] = []

  // 分批处理请求
  for (let i = 0; i < requests.length; i += maxConcurrent) {
    const batch = requests.slice(i, i + maxConcurrent)
    
    const batchPromises = batch.map(async (request) => {
      try {
        return await client.enhance(request)
      } catch (error) {
        if (stopOnFailure) {
          throw error
        }
        errors.push(error as Error)
        return null
      }
    })

    const batchResults = await Promise.all(batchPromises)
    
    for (const result of batchResults) {
      if (result) {
        results.push(result)
      }
    }

    if (stopOnFailure && errors.length > 0) {
      throw errors[0]
    }
  }

  if (errors.length > 0 && !stopOnFailure) {
    console.warn(`批量强化完成，但有 ${errors.length} 个请求失败:`, errors)
  }

  return results
}

/**
 * 强化统计分析
 */
export function analyzeEnhancementResults(results: EnhancementResponse[]): {
  totalAttempts: number
  successCount: number
  failureCount: number
  majorFailureCount: number
  successRate: number
  totalCost: number
  averageCost: number
} {
  const totalAttempts = results.length
  let successCount = 0
  let failureCount = 0
  let majorFailureCount = 0
  let totalCost = 0

  for (const result of results) {
    totalCost += result.cost.mesos

    switch (result.result.type) {
      case 'success':
        successCount++
        break
      case 'failed':
        failureCount++
        break
      case 'major_failure':
        majorFailureCount++
        break
    }
  }

  return {
    totalAttempts,
    successCount,
    failureCount,
    majorFailureCount,
    successRate: totalAttempts > 0 ? (successCount / totalAttempts) * 100 : 0,
    totalCost,
    averageCost: totalAttempts > 0 ? totalCost / totalAttempts : 0
  }
}

/**
 * 验证强化请求参数
 */
export function validateEnhancementRequest(request: EnhancementRequest): {
  valid: boolean
  errors: string[]
} {
  const errors: string[] = []

  if (!request.equipmentId || request.equipmentId.trim() === '') {
    errors.push('装备ID不能为空')
  }

  if (request.currentLevel < 0 || request.currentLevel > 25) {
    errors.push('当前等级必须在0-25之间')
  }

  if (!['starforce', 'potential', 'bonusstat'].includes(request.enhancementType)) {
    errors.push('强化类型必须是 starforce、potential 或 bonusstat')
  }

  if (request.minigameBonus && (request.minigameBonus < 0 || request.minigameBonus > 20)) {
    errors.push('迷你游戏加成必须在0-20之间')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 格式化强化结果消息
 */
export function formatEnhancementMessage(result: EnhancementResponse): string {
  const { result: enhancementResult, cost } = result
  
  let message = enhancementResult.message
  
  if (cost.mesos > 0) {
    message += `\n消耗金币: ${cost.mesos.toLocaleString()}`
  }

  if (cost.materials && cost.materials.length > 0) {
    const materialText = cost.materials
      .map(m => `${m.name} x${m.quantity}`)
      .join(', ')
    message += `\n消耗材料: ${materialText}`
  }

  return message
}

/**
 * 计算强化成本预估
 */
export function estimateEnhancementCost(
  fromLevel: number,
  toLevel: number,
  enhancementType: 'starforce' | 'potential' | 'bonusstat',
  successRate: number = 0.8
): {
  estimatedAttempts: number
  estimatedCost: number
  minCost: number
  maxCost: number
} {
  const levelDiff = toLevel - fromLevel
  
  if (levelDiff <= 0) {
    return {
      estimatedAttempts: 0,
      estimatedCost: 0,
      minCost: 0,
      maxCost: 0
    }
  }

  // 简化的成本计算
  const baseCost = enhancementType === 'starforce' 
    ? Math.floor(1000000 * Math.pow(1.2, fromLevel))
    : enhancementType === 'potential' 
    ? 500000 
    : 300000

  const estimatedAttempts = Math.ceil(levelDiff / successRate)
  const estimatedCost = estimatedAttempts * baseCost
  const minCost = levelDiff * baseCost
  const maxCost = estimatedAttempts * 2 * baseCost

  return {
    estimatedAttempts,
    estimatedCost,
    minCost,
    maxCost
  }
}
