import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/db'
import { sendVerificationEmail } from '@/lib/email'
import { generateVerificationToken } from '@/lib/tokens'

const resendSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址')
})

// 防刷机制：记录重发请求的时间戳
const resendAttempts = new Map<string, number>()
const RESEND_COOLDOWN = 60 * 1000 // 60秒冷却时间

export async function POST(request: NextRequest) {
  console.log('📧 开始处理重发验证邮件请求')

  try {
    const body = await request.json()
    console.log('📧 接收到重发请求:', { email: body.email })

    const { email } = resendSchema.parse(body)
    console.log('✅ 邮箱格式验证通过')

    // 检查防刷机制
    const lastAttempt = resendAttempts.get(email)
    const now = Date.now()
    
    if (lastAttempt && (now - lastAttempt) < RESEND_COOLDOWN) {
      const remainingTime = Math.ceil((RESEND_COOLDOWN - (now - lastAttempt)) / 1000)
      console.log('❌ 重发请求过于频繁:', email, '剩余冷却时间:', remainingTime)
      return NextResponse.json(
        { 
          error: `请等待 ${remainingTime} 秒后再重新发送`,
          remainingTime 
        },
        { status: 429 }
      )
    }

    // 查找用户
    console.log('🔍 查找用户:', email)
    const user = await prisma.user.findUnique({
      where: { email }
    })

    if (!user) {
      console.log('❌ 用户不存在:', email)
      // 为了安全，不透露用户是否存在
      return NextResponse.json({
        success: true,
        message: '如果该邮箱已注册，验证邮件已发送'
      })
    }

    // 检查用户是否已经验证
    if (user.emailVerified) {
      console.log('✅ 用户邮箱已验证:', email)
      return NextResponse.json(
        { error: '该邮箱已经验证，无需重复验证' },
        { status: 400 }
      )
    }

    console.log('📧 用户邮箱未验证，开始重发流程')

    // 删除旧的验证令牌
    console.log('🗑️ 删除旧的验证令牌')
    await prisma.verificationToken.deleteMany({
      where: { identifier: email }
    })
    console.log('✅ 旧验证令牌删除成功')

    // 生成新的验证令牌
    console.log('🎫 生成新的验证令牌')
    const verificationToken = generateVerificationToken()
    console.log('✅ 新验证令牌生成成功:', verificationToken.substring(0, 8) + '...')

    // 存储新的验证令牌
    console.log('💾 存储新的验证令牌')
    await prisma.verificationToken.create({
      data: {
        identifier: email,
        token: verificationToken,
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时后过期
      }
    })
    console.log('✅ 新验证令牌存储成功')

    // 发送验证邮件
    console.log('📧 开始发送验证邮件到:', email)
    try {
      await sendVerificationEmail(email, verificationToken)
      console.log('✅ 验证邮件发送成功')
    } catch (emailError) {
      console.error('❌ 验证邮件发送失败:', emailError)
      return NextResponse.json(
        { error: '邮件发送失败，请稍后重试' },
        { status: 500 }
      )
    }

    // 记录重发时间（防刷机制）
    resendAttempts.set(email, now)
    
    // 清理过期的重发记录（简单的内存清理）
    setTimeout(() => {
      resendAttempts.delete(email)
    }, RESEND_COOLDOWN)

    console.log('🎉 重发验证邮件流程完成')
    return NextResponse.json({
      success: true,
      message: '验证邮件已重新发送，请查收邮箱',
      cooldownTime: RESEND_COOLDOWN / 1000
    })

  } catch (error) {
    console.error('❌ 重发验证邮件过程发生错误:', error)

    if (error instanceof z.ZodError) {
      console.error('❌ 数据验证错误:', error.errors)
      return NextResponse.json(
        { error: '邮箱格式错误' },
        { status: 400 }
      )
    }

    console.error('❌ 重发验证邮件失败，未知错误:', error)
    return NextResponse.json(
      { error: '重发邮件失败，请稍后重试' },
      { status: 500 }
    )
  }
}

// 获取重发冷却状态的 GET 端点
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const email = searchParams.get('email')

  if (!email) {
    return NextResponse.json(
      { error: '缺少邮箱参数' },
      { status: 400 }
    )
  }

  const lastAttempt = resendAttempts.get(email)
  const now = Date.now()

  if (lastAttempt && (now - lastAttempt) < RESEND_COOLDOWN) {
    const remainingTime = Math.ceil((RESEND_COOLDOWN - (now - lastAttempt)) / 1000)
    return NextResponse.json({
      canResend: false,
      remainingTime,
      cooldownTime: RESEND_COOLDOWN / 1000
    })
  }

  return NextResponse.json({
    canResend: true,
    remainingTime: 0,
    cooldownTime: RESEND_COOLDOWN / 1000
  })
}
