# 1
我有一个 CSV 文件，记录了《冒险岛》装备“星之力”强化系统的概率数据，字段包括：
当前级, 下一级, 成功概率, 失败保级概率, 失败降级概率, 失败损坏概率。
请用 Python 编写一个强化模拟器，要求如下：

1. 从该 CSV 文件中读取并构建强化概率映射（按当前星级索引）。
2. 实现函数 simulate_enhancement(current_star: int) -> Tuple[str, int]，用于模拟一次强化。
3. 每次强化使用对应星级的概率，根据以下逻辑判断结果：
成功：进入“下一级”，返回 "Success"
失败保级：返回 "Fail (Hold)"，星级不变
失败降级：返回 "Fail (Drop)"，星级 -1
失败损坏：返回 "Boom"，星级变为 -6
4. 所有概率总和应为 1（或接近 1）。可使用 random.choices() 或等效方式实现带权重随机选择。
5. 提供一个函数用于连续模拟 N 次强化过程，从 0 星开始，打印每次星级和结果。
无需使用任何外部依赖，仅使用标准库完成。

还要考虑，用户开启了“解锁抓星星”和“防止破坏”选项。

# 2
任务： 添加装备强化API接口

按照`全栈Next.js架构实施方案.md`文档，添加装备强化API接口。一个现代的优雅的API接口。可以被其他第三方使用的API接口。
同时生成一份python代码调用这个接口。

页面模拟强化的代码文件:`components/enhancement-simulator/EnhancementSimulator.tsx`
在"装备强化模拟器"页面，用户点击强化，后就调用这个接口。装备强化模拟器页面通过这个接口返回的成功、失败保级、失败损坏。来显示不同的页面效果。

保持代码符合 strict 模式规范。
用户开启了“解锁抓星星”和“防止破坏”选项。
## 2.1 
任务：为冒险岛情报站添加装备强化API接口

**背景**：
- 项目使用Next.js 14 + TypeScript + Prisma + PostgreSQL架构
- 需要为现有的装备强化模拟器页面(`components/enhancement-simulator/EnhancementSimulator.tsx`)提供后端API支持
- API需要符合RESTful设计原则，支持第三方调用

**具体要求**：

1. **API接口设计**：
    - 创建 `app/api/enhancement/enhance/route.ts` 文件
    - 实现POST方法处理装备强化请求
    - 输入参数：装备ID、当前强化等级、强化类型(星力/潜能等)、启用解锁抓星星、启用防止破坏
    - 输出结果：强化结果(成功/失败保级/失败损坏)、新等级、消耗材料/费用
    - 包含完整的错误处理和参数验证
    - 添加API文档注释和TypeScript类型定义
    - 只需要实现一个简单80%成功、10%失败保级、5%失败损坏,5%失败降级。具体的业务逻辑，后续再开发。

2. **数据库设计**：
    - 暂时不需要。

3. **前端集成**：
    - 修改 `EnhancementSimulator.tsx` 组件，将模拟逻辑替换为API调用
    - 保持现有的UI交互效果(成功/失败动画等)
    - 添加加载状态和错误处理

4. **第三方调用支持**：
    - 提供API密钥验证机制
    - 生成完整的API文档(包含请求/响应示例)
    - 创建Python调用示例代码

5. **代码质量要求**：
    - 严格遵循TypeScript strict模式
    - 符合项目现有的代码规范和架构模式
    - 包含单元测试用例
    - 添加适当的日志记录

**交付物**：
1. 完整的API接口实现
2. 更新后的前端组件
3. Python调用示例代码
4. API文档说明
5. 相关的类型定义和数据库模型(如需要)

请按照项目现有的文件结构和命名规范实施，确保与现有认证系统、错误处理机制等保持一致。



# 2 完善概率算法
我有一个 CSV 文件。文件目录`docs/starforcePB.csv`，记录了《冒险岛》装备“星之力”强化系统的概率数据。
字段包括： 当前级, 下一级, 成功概率, 失败保级概率, 失败降级概率, 失败损坏概率。
抓星星：成功率提升 5%，按当前成功率 × 1.05 计算，其余失败项按比例扣除.
任务1：
一句csv文件`docs/starforcePB.csv`实现一个独立的装备强化算法函数：
1. 从该 CSV 文件中读取并构建强化概率映射（按当前星级索引）。
2. 函数输入参数为：当前级、是否开启了抓星星小游戏、是否开启了防止破坏。
3. 函数输出参数为强化结果。有四个状态：成功、失败保级、失败降级、失败损坏。
4. 每次强化使用对应星级的概率，根据以下逻辑判断结果：
   如果用户玩了抓星星小游戏，成功概率提升百分之5。按当前成功率 × 1.05 计算，其余失败项按比例扣除。最后满足概率总和为1.
   成功：进入“下一级”，返回 "Success"
   失败保级：返回 "Fail (Hold)"，星级不变
   失败降级：返回 "Fail (Drop)"，星级 -1
   失败损坏：返回 "Boom"，星级变为 -6
4. 所有概率总和应为 1（或接近 1）。可使用 random.choices() 或等效方式实现带权重随机选择。
5. 提供一个测试函数用于连续模拟 N 次强化过程，从 0 星开始，打印每次星级和结果。

任务2：
将算法函数运用到强化api代码

要求
1. 严格遵循TypeScript strict模式。
2. 符合项目现有的代码规范和架构模式

## 2.1 rewrite

基于现有的冒险岛情报站装备强化API项目，需要使用真实的星力强化概率数据替换当前的简化算法。

**背景信息**：
- 项目已有装备强化API接口：`app/api/enhancement/enhance/route.ts`
- 当前使用简化的固定概率（80%成功、10%失败保级、5%失败损坏、5%失败降级）
- 需要基于真实游戏数据实现更准确的强化算法

**数据源**：
- CSV文件路径：`docs/starforcePB.csv`
- 字段结构：当前级, 下一级, 成功概率, 失败保级概率, 失败降级概率, 失败损坏概率
- 抓星星机制：成功率提升5%，计算方式为 `当前成功率 × 1.05`，其余失败概率按比例重新分配以确保总和为1

**任务1：创建独立的强化算法模块**

在 `lib/starforce-algorithm.ts` 中实现：

1. **CSV数据读取函数**：
   - 读取 `docs/starforcePB.csv` 文件
   - 解析并构建按星级索引的概率映射表
   - 返回类型化的概率数据结构

2. **核心强化算法函数**：
   ```typescript
   function calculateStarforceResult(
     currentLevel: number,
     starcatchEnabled: boolean,
     preventEnabled: boolean
   ): StarforceResult
   ```

   输入参数：
   - `currentLevel`: 当前星级 (0-25)
   - `starcatchEnabled`: 是否启用抓星星小游戏
   - `preventEnabled`: 是否启用防止破坏

   输出结果枚举：
   - `SUCCESS`: 成功，星级+1
   - `FAIL_HOLD`: 失败保级，星级不变
   - `FAIL_DROP`: 失败降级，星级-1
   - `BOOM`: 失败损坏，星级重置（具体规则按游戏设定）

3. **概率调整逻辑**：
   - 抓星星启用时：成功率 = 原成功率 × 1.05
   - 其余概率按比例缩减：`新概率 = 原概率 × (1 - 成功率增量) / (1 - 原成功率)`
   - 防止破坏启用时：失败损坏概率转移到失败保级
   - 确保所有概率总和为1.0

4. **测试模拟函数**：
   ```typescript
   function simulateStarforceEnhancement(
     startLevel: number,
     targetLevel: number,
     attempts: number,
     options: { starcatchEnabled: boolean, preventEnabled: boolean }
   ): SimulationResult
   ```

**任务2：集成到现有API系统**

修改以下文件以使用新算法：

1. **更新API路由** (`app/api/enhancement/enhance/route.ts`)：
   - 替换 `ENHANCEMENT_PROBABILITIES` 常量为CSV数据驱动
   - 更新 `calculateEnhancementResult` 函数调用新算法
   - 保持现有的API接口契约不变

2. **更新工具函数** (`lib/enhancement-utils.ts`)：
   - 修改 `getEnhancementProbability` 函数使用CSV数据
   - 更新 `calculateEnhancementResult` 函数逻辑
   - 保持与前端组件的兼容性

3. **更新类型定义** (`types/enhancement.ts`)：
   - 添加CSV数据结构类型
   - 更新强化结果类型以匹配新算法
   - 确保类型安全

**技术要求**：
1. 严格遵循TypeScript strict模式，所有函数必须有完整类型注解
2. 使用现有项目的错误处理模式和日志记录方式
3. 保持与现有API接口的完全兼容性
4. 添加适当的单元测试覆盖新算法
5. 确保CSV文件读取的性能优化（考虑缓存机制）
6. 遵循项目现有的代码规范和架构模式

**验证要求**：
1. 新算法的概率分布应与CSV数据完全一致
2. 抓星星和防止破坏功能正确实现
3. 现有的前端强化模拟器功能不受影响
4. API接口的请求/响应格式保持不变
5. 通过单元测试验证算法正确性


# 5
请帮我处理 `__tests__` 目录下的测试文件问题：

1. **分析测试环境配置**：
   - 检查项目是否已配置Jest或其他测试框架
   - 查看 `package.json` 中的测试脚本配置
   - 确认测试相关依赖是否已安装

2. **运行测试并诊断问题**：
   - 尝试运行测试命令（如 `npm test`、`pnpm test`、`yarn test`）
   - 识别并记录所有编译错误和测试失败
   - 特别关注当前打开的 `__tests__\auth-config.test.ts` 文件

3. **解决编译和配置问题**：
   - 修复TypeScript类型错误
   - 解决模块导入问题
   - 配置Jest以支持Next.js项目（如果尚未配置）
   - 处理路径别名和模块解析问题
   - 修复任何与最近的代码更改相关的测试问题（特别是星力强化算法重构后的影响）

4. **确保测试可执行**：
   - 修复所有编译错误
   - 确保测试可以成功运行
   - 验证测试覆盖了关键功能
   - 如果需要，更新测试以匹配当前的代码结构

请按步骤执行，并提供详细的问题诊断和解决方案。