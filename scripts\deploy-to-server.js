#!/usr/bin/env node

/**
 * 部署到服务器脚本
 * 自动化本地构建、打包和上传到服务器的流程
 */

const { execSync, spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

// 服务器配置
const SERVER_CONFIG = {
  host: 'your-server-ip',  // 替换为您的服务器IP
  user: 'root',            // 替换为您的用户名
  port: 22,                // SSH端口
  remotePath: '/root/gits/maplestory-info-station',  // 服务器上的项目路径
  backupPath: '/root/backups'  // 备份路径
}

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
}

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function execCommand(command, options = {}) {
  try {
    log('blue', `执行: ${command}`)
    const result = execSync(command, { 
      stdio: 'inherit', 
      encoding: 'utf8',
      ...options 
    })
    return result
  } catch (error) {
    log('red', `命令执行失败: ${command}`)
    throw error
  }
}

async function checkPrerequisites() {
  log('cyan', '🔍 检查部署前提条件...')
  
  // 检查是否已构建
  if (!fs.existsSync('.next')) {
    log('yellow', '⚠️  未找到构建文件，开始构建...')
    execCommand('pnpm run build')
  } else {
    log('green', '✅ 找到构建文件')
  }
  
  // 检查部署目录
  if (!fs.existsSync('deploy')) {
    log('yellow', '⚠️  未找到部署目录，开始准备...')
    execCommand('node scripts/prepare-deploy.js')
  } else {
    log('green', '✅ 找到部署目录')
  }
  
  // 检查 SSH 连接
  try {
    execCommand(`ssh -o ConnectTimeout=5 ${SERVER_CONFIG.user}@${SERVER_CONFIG.host} "echo 'SSH连接测试成功'"`, { stdio: 'pipe' })
    log('green', '✅ SSH 连接正常')
  } catch (error) {
    log('red', '❌ SSH 连接失败，请检查服务器配置')
    throw new Error('SSH连接失败')
  }
}

async function createBackup() {
  log('cyan', '💾 创建服务器备份...')
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19)
  const backupName = `maplestory-backup-${timestamp}`
  
  try {
    // 创建备份目录
    execCommand(`ssh ${SERVER_CONFIG.user}@${SERVER_CONFIG.host} "mkdir -p ${SERVER_CONFIG.backupPath}"`)
    
    // 备份当前应用
    execCommand(`ssh ${SERVER_CONFIG.user}@${SERVER_CONFIG.host} "cp -r ${SERVER_CONFIG.remotePath} ${SERVER_CONFIG.backupPath}/${backupName}"`)
    
    log('green', `✅ 备份创建成功: ${backupName}`)
    return backupName
  } catch (error) {
    log('yellow', '⚠️  备份创建失败，继续部署...')
    return null
  }
}

async function uploadFiles() {
  log('cyan', '📤 上传文件到服务器...')
  
  // 上传部署文件
  execCommand(`scp -r deploy/* ${SERVER_CONFIG.user}@${SERVER_CONFIG.host}:${SERVER_CONFIG.remotePath}/`)
  
  log('green', '✅ 文件上传完成')
}

async function installDependencies() {
  log('cyan', '📦 安装生产依赖...')
  
  execCommand(`ssh ${SERVER_CONFIG.user}@${SERVER_CONFIG.host} "cd ${SERVER_CONFIG.remotePath} && npm ci --only=production"`)
  
  log('green', '✅ 依赖安装完成')
}

async function restartApplication() {
  log('cyan', '🔄 重启应用...')
  
  try {
    // 尝试使用 PM2 重启
    execCommand(`ssh ${SERVER_CONFIG.user}@${SERVER_CONFIG.host} "cd ${SERVER_CONFIG.remotePath} && pm2 restart maplestory-info-station || pm2 start ecosystem.config.js"`)
    log('green', '✅ 应用重启成功 (PM2)')
  } catch (error) {
    log('yellow', '⚠️  PM2 重启失败，尝试直接启动...')
    try {
      // 停止可能运行的进程
      execCommand(`ssh ${SERVER_CONFIG.user}@${SERVER_CONFIG.host} "pkill -f 'next start' || true"`)
      
      // 启动应用
      execCommand(`ssh ${SERVER_CONFIG.user}@${SERVER_CONFIG.host} "cd ${SERVER_CONFIG.remotePath} && nohup npm start > app.log 2>&1 &"`)
      log('green', '✅ 应用启动成功 (直接启动)')
    } catch (startError) {
      log('red', '❌ 应用启动失败')
      throw startError
    }
  }
}

async function verifyDeployment() {
  log('cyan', '🔍 验证部署...')
  
  // 等待应用启动
  await new Promise(resolve => setTimeout(resolve, 5000))
  
  try {
    // 检查应用状态
    execCommand(`ssh ${SERVER_CONFIG.user}@${SERVER_CONFIG.host} "curl -f http://localhost:3000 > /dev/null"`)
    log('green', '✅ 应用运行正常')
  } catch (error) {
    log('yellow', '⚠️  应用可能还在启动中，请手动检查')
  }
}

async function showDeploymentInfo() {
  log('cyan', '📋 部署信息:')
  console.log(`   服务器: ${SERVER_CONFIG.host}`)
  console.log(`   路径: ${SERVER_CONFIG.remotePath}`)
  console.log(`   时间: ${new Date().toLocaleString('zh-CN')}`)
  console.log('')
  
  log('cyan', '🔗 有用的命令:')
  console.log(`   查看日志: ssh ${SERVER_CONFIG.user}@${SERVER_CONFIG.host} "cd ${SERVER_CONFIG.remotePath} && pm2 logs maplestory-info-station"`)
  console.log(`   查看状态: ssh ${SERVER_CONFIG.user}@${SERVER_CONFIG.host} "cd ${SERVER_CONFIG.remotePath} && pm2 status"`)
  console.log(`   重启应用: ssh ${SERVER_CONFIG.user}@${SERVER_CONFIG.host} "cd ${SERVER_CONFIG.remotePath} && pm2 restart maplestory-info-station"`)
}

async function main() {
  try {
    log('green', '🚀 开始部署到服务器...')
    console.log('')
    
    // 1. 检查前提条件
    await checkPrerequisites()
    
    // 2. 创建备份
    const backupName = await createBackup()
    
    // 3. 上传文件
    await uploadFiles()
    
    // 4. 安装依赖
    await installDependencies()
    
    // 5. 重启应用
    await restartApplication()
    
    // 6. 验证部署
    await verifyDeployment()
    
    // 7. 显示部署信息
    await showDeploymentInfo()
    
    log('green', '🎉 部署完成!')
    
    if (backupName) {
      console.log('')
      log('yellow', `💾 如需回滚，使用备份: ${backupName}`)
      console.log(`   回滚命令: ssh ${SERVER_CONFIG.user}@${SERVER_CONFIG.host} "cp -r ${SERVER_CONFIG.backupPath}/${backupName}/* ${SERVER_CONFIG.remotePath}/ && pm2 restart maplestory-info-station"`)
    }
    
  } catch (error) {
    log('red', `❌ 部署失败: ${error.message}`)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  // 检查命令行参数
  const args = process.argv.slice(2)
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
使用方法: node scripts/deploy-to-server.js [选项]

选项:
  --help, -h     显示帮助信息
  --config       显示服务器配置

在运行前请确保:
1. 已配置 SSH 密钥认证
2. 服务器配置正确
3. 本地已完成构建
    `)
    process.exit(0)
  }
  
  if (args.includes('--config')) {
    console.log('当前服务器配置:')
    console.log(JSON.stringify(SERVER_CONFIG, null, 2))
    process.exit(0)
  }
  
  main()
}

module.exports = { main, SERVER_CONFIG }
