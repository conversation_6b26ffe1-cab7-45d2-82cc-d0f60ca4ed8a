# NextAuth 登出重定向问题解决方案

## 🔍 问题现象

- ✅ 登录功能正常（使用 HTTPS 域名）
- ❌ 登出后重定向到 `http://localhost:3000`

## 🎯 根本原因

NextAuth.js 的 `redirect` 回调函数在处理登出重定向时没有正确使用生产环境的 `NEXTAUTH_URL`。

## ✅ 解决方案

### 🚀 快速修复（推荐）

**在生产服务器上运行：**

```bash
# 1. 上传修复文件
scp fix-production-env.sh user@server:/root/svc/mxd/mxd-tool/
scp lib/auth.ts user@server:/root/svc/mxd/mxd-tool/lib/

# 2. 在服务器上应用修复
ssh user@server
cd /root/svc/mxd/mxd-tool
chmod +x fix-production-env.sh
./fix-production-env.sh

# 3. 重启应用
pkill -f "next start"
pnpm start
```

### 🔧 手动修复步骤

#### 1. 更新 NextAuth 配置

在 `lib/auth.ts` 中的 `redirect` 回调函数：

```typescript
async redirect({ url, baseUrl }) {
  // 强制使用生产环境的 HTTPS 域名
  const productionBaseUrl = process.env.NEXTAUTH_URL || baseUrl
  
  console.log('NextAuth redirect:', { url, baseUrl, productionBaseUrl })
  
  // 处理相对路径
  if (url.startsWith("/")) {
    return `${productionBaseUrl}${url}`
  }
  
  // 处理完整 URL
  try {
    const urlObj = new URL(url)
    const baseUrlObj = new URL(productionBaseUrl)
    
    // 如果是相同域名，允许重定向
    if (urlObj.origin === baseUrlObj.origin) {
      return url
    }
    
    // 如果是 localhost，重定向到生产域名
    if (urlObj.hostname === 'localhost' || urlObj.hostname === '127.0.0.1') {
      return `${productionBaseUrl}${urlObj.pathname}${urlObj.search}${urlObj.hash}`
    }
  } catch (error) {
    console.error('NextAuth redirect URL parsing error:', error)
  }
  
  // 默认重定向到生产环境首页
  return productionBaseUrl
}
```

#### 2. 更新环境变量

确保 `.env.local` 包含正确的生产环境配置：

```bash
# NextAuth 配置 - 生产环境
NEXTAUTH_URL="https://mxd.hyhuman.xyz"
NEXTAUTH_SECRET="your-production-secret-key"

# 信任代理配置
AUTH_TRUST_HOST="true"
NEXTAUTH_URL_INTERNAL="http://localhost:3000"

# 应用配置
NEXT_PUBLIC_APP_URL="https://mxd.hyhuman.xyz"
NODE_ENV="production"
```

#### 3. 重启应用

```bash
pkill -f "next start"
pnpm start
```

## 🧪 测试和调试

### 1. 访问调试页面

访问 `https://mxd.hyhuman.xyz/debug-auth` 进行测试：

- 测试不同的登出方式
- 查看环境变量配置
- 检查会话状态

### 2. API 调试

```bash
# 检查环境变量
curl https://mxd.hyhuman.xyz/api/debug/env

# 测试登出端点
curl -I https://mxd.hyhuman.xyz/api/auth/signout
```

### 3. 浏览器控制台

在浏览器控制台查看 NextAuth 重定向日志：

```javascript
// 查看重定向信息
console.log('NextAuth redirect:', { url, baseUrl, productionBaseUrl })
```

## 🔍 故障排除

### 问题 1: 仍然重定向到 localhost

**检查项**:
- [ ] `NEXTAUTH_URL` 是否设置为 `https://mxd.hyhuman.xyz`
- [ ] 应用是否已重启
- [ ] `lib/auth.ts` 是否已更新

**解决方案**:
```bash
# 检查环境变量
grep NEXTAUTH_URL .env.local

# 重启应用
pkill -f "next start" && pnpm start
```

### 问题 2: 登出后出现错误页面

**检查项**:
- [ ] Nginx 配置是否正确
- [ ] SSL 证书是否有效
- [ ] 防火墙是否阻止访问

**解决方案**:
```bash
# 检查 Nginx 状态
sudo systemctl status nginx

# 检查 SSL 证书
openssl s_client -connect mxd.hyhuman.xyz:443
```

### 问题 3: 会话丢失

**检查项**:
- [ ] Cookie 域名设置
- [ ] `AUTH_TRUST_HOST` 是否为 true
- [ ] `NEXTAUTH_SECRET` 是否一致

## 📋 验证清单

修复后，请验证以下功能：

- [ ] 登录功能正常
- [ ] 登出后重定向到 `https://mxd.hyhuman.xyz`
- [ ] 会话状态正确维护
- [ ] 所有 NextAuth API 端点正常工作

### 测试步骤

1. **登录测试**:
   - 访问 `https://mxd.hyhuman.xyz/login`
   - 使用用户名/密码登录
   - 确认登录成功

2. **登出测试**:
   - 点击登出按钮
   - 确认重定向到 `https://mxd.hyhuman.xyz`
   - 确认会话已清除

3. **API 测试**:
   ```bash
   # 测试会话端点
   curl https://mxd.hyhuman.xyz/api/auth/session
   
   # 测试登出端点
   curl -I https://mxd.hyhuman.xyz/api/auth/signout
   ```

## 🎯 预防措施

### 1. 环境变量管理

创建环境特定的配置文件：

```bash
# 开发环境
.env.development.local

# 生产环境  
.env.production.local
```

### 2. 部署检查脚本

```bash
#!/bin/bash
# deploy-check.sh

echo "检查生产环境配置..."

# 检查关键环境变量
if grep -q "localhost" .env.local; then
    echo "❌ 警告: .env.local 包含 localhost 配置"
    exit 1
fi

if ! grep -q "https://mxd.hyhuman.xyz" .env.local; then
    echo "❌ 错误: NEXTAUTH_URL 未设置为生产域名"
    exit 1
fi

echo "✅ 环境配置检查通过"
```

### 3. 监控和告警

设置监控检查重定向是否正确：

```bash
# 检查登出重定向
curl -I https://mxd.hyhuman.xyz/api/auth/signout | grep -i location
```

## 🎉 成功标志

修复成功后，您应该看到：

- ✅ 登出后重定向到 `https://mxd.hyhuman.xyz`
- ✅ 浏览器地址栏显示正确的 HTTPS 域名
- ✅ 控制台日志显示正确的重定向 URL
- ✅ 所有 NextAuth 功能正常工作

---

**总结**：通过修复 NextAuth 的 `redirect` 回调函数并确保环境变量正确配置，可以解决登出重定向到 localhost 的问题。关键是强制使用 `NEXTAUTH_URL` 作为重定向的基础 URL。
