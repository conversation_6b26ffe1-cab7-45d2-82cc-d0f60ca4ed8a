# 🚀 冒险岛情报站 - Rocky Linux 9.5 快速开始指南

## 📋 概述

本指南帮助您在 Rocky Linux 9.5 系统上快速部署冒险岛情报站应用。

**前提条件：**
- Rocky Linux 9.5 服务器
- PostgreSQL 数据库已配置
- Redis 缓存服务已配置
- 具有 sudo 权限的用户账户

## ⚡ 一键部署

### 方式一：完全自动化部署

```bash
# 1. 下载部署脚本
curl -O https://raw.githubusercontent.com/your-repo/scripts/rocky-setup.sh
curl -O https://raw.githubusercontent.com/your-repo/scripts/rocky-deploy.sh

# 2. 设置执行权限
chmod +x rocky-setup.sh rocky-deploy.sh

# 3. 初始化环境（需要 root 权限）
sudo ./rocky-setup.sh

# 4. 切换到应用用户
sudo su - maplestory

# 5. 克隆应用代码
git clone <your-repository-url> app
cd app

# 6. 配置环境变量
cp .env.production.template .env.production
nano .env.production  # 编辑数据库连接等配置

# 7. 执行部署
./scripts/rocky-deploy.sh
```

### 方式二：手动分步部署

#### 步骤 1: 环境初始化
```bash
# 更新系统
sudo dnf update -y

# 安装基础工具
sudo dnf install -y curl wget git unzip tar gzip bc

# 安装 Node.js 18
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo dnf install -y nodejs

# 安装 PM2
sudo npm install -g pm2

# 创建应用用户
sudo useradd -m -s /bin/bash maplestory
sudo -u maplestory mkdir -p /home/<USER>/{app,logs,backups}
```

#### 步骤 2: 防火墙配置
```bash
# 启用防火墙
sudo systemctl enable --now firewalld

# 开放应用端口（如果不使用 Nginx）
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload

# 或者开放 HTTP/HTTPS 端口（如果使用 Nginx）
# sudo firewall-cmd --permanent --add-service=http
# sudo firewall-cmd --permanent --add-service=https
# sudo firewall-cmd --reload
```

#### 步骤 3: 部署应用
```bash
# 切换到应用用户
sudo su - maplestory

# 克隆代码
git clone <your-repository-url> app
cd app

# 安装依赖
npm ci --only=production

# 配置环境变量
cp .env.example .env.production
nano .env.production
```

#### 步骤 4: 数据库初始化
```bash
# 生成 Prisma 客户端
npx prisma generate

# 推送数据库模式
npx prisma db push

# 运行种子数据
npx prisma db seed
```

#### 步骤 5: 构建和启动
```bash
# 构建应用
npm run build

# 创建 PM2 配置
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'maplestory-info-station',
    script: 'npm',
    args: 'start',
    cwd: '/home/<USER>/app',
    instances: 'max',
    exec_mode: 'cluster',
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    env_file: '.env.production',
    log_file: '/home/<USER>/logs/app.log',
    out_file: '/home/<USER>/logs/out.log',
    error_file: '/home/<USER>/logs/error.log',
    max_memory_restart: '1G',
    autorestart: true
  }]
}
EOF

# 启动应用
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

## 🔧 环境变量配置

### 必需配置项

```env
# 数据库连接（根据实际情况修改）
DATABASE_URL="postgresql://username:password@localhost:5432/mxd_info_db"
REDIS_URL="redis://localhost:6379"

# NextAuth 配置
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-32-character-secret-key"

# 应用配置
NEXT_PUBLIC_APP_URL="https://your-domain.com"
NODE_ENV="production"
```

### 可选配置项

```env
# 邮件服务
RESEND_API_KEY="re_your-api-key"
EMAIL_FROM="<EMAIL>"

# 第三方服务
NEXT_PUBLIC_FINGERPRINT_JS_API_KEY="your-fingerprint-js-key"
STRIPE_SECRET_KEY="sk_live_your-stripe-key"
```

## 📊 验证部署

### 1. 检查应用状态
```bash
# 查看 PM2 进程状态
pm2 status

# 查看应用日志
pm2 logs maplestory-info-station

# 检查端口监听
sudo ss -tlnp | grep :3000
```

### 2. 健康检查
```bash
# 本地健康检查
curl http://localhost:3000/health

# 完整页面测试
curl -I http://localhost:3000

# 如果配置了域名
curl https://your-domain.com/health
```

### 3. 功能测试
```bash
# API 测试
curl http://localhost:3000/api/auth/csrf

# 数据库连接测试
# 查看应用日志确认数据库连接正常
pm2 logs --lines 50
```

## 🔄 日常维护

### 应用管理
```bash
# 查看状态
pm2 status

# 重启应用
pm2 restart maplestory-info-station

# 停止应用
pm2 stop maplestory-info-station

# 查看日志
pm2 logs
pm2 logs --err  # 只看错误日志
```

### 更新部署
```bash
# 使用部署脚本更新
cd /home/<USER>/app
./scripts/rocky-deploy.sh

# 或手动更新
git pull origin main
npm ci --only=production
npm run build
pm2 restart maplestory-info-station
```

### 监控设置
```bash
# 创建监控脚本
nano /home/<USER>/monitor.sh

# 设置定时任务
crontab -e
# 添加: */5 * * * * /home/<USER>/monitor.sh
```

## 🔧 故障排查

### 常见问题

#### 1. 应用无法启动
```bash
# 检查日志
pm2 logs --err

# 检查环境变量
cat .env.production

# 手动启动测试
npm start
```

#### 2. 数据库连接失败
```bash
# 检查数据库服务状态
sudo systemctl status postgresql

# 测试数据库连接
psql -h localhost -U username -d mxd_info_db
```

#### 3. 端口被占用
```bash
# 查看端口占用
sudo ss -tlnp | grep :3000

# 杀死占用进程
sudo kill -9 <PID>
```

#### 4. 内存不足
```bash
# 查看内存使用
free -h
pm2 monit

# 重启应用
pm2 restart maplestory-info-station
```

### 日志位置
```bash
# 应用日志
/home/<USER>/logs/app.log
/home/<USER>/logs/out.log
/home/<USER>/logs/error.log

# 系统日志
sudo journalctl -u firewalld
sudo journalctl -xe
```

## 🔒 安全建议

### 1. 文件权限
```bash
# 设置环境变量文件权限
chmod 600 /home/<USER>/app/.env.production

# 设置应用目录权限
sudo chown -R maplestory:maplestory /home/<USER>/
```

### 2. 防火墙规则
```bash
# 只开放必要端口
sudo firewall-cmd --list-ports

# 移除不需要的规则
sudo firewall-cmd --permanent --remove-port=<port>/tcp
sudo firewall-cmd --reload
```

### 3. 定期更新
```bash
# 更新系统包
sudo dnf update -y

# 更新 Node.js 依赖
npm audit
npm audit fix
```

## 📈 性能优化

### 1. 系统优化
```bash
# 增加文件描述符限制
echo "maplestory soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "maplestory hard nofile 65536" | sudo tee -a /etc/security/limits.conf
```

### 2. PM2 优化
```bash
# 使用集群模式
instances: 'max'  # 在 ecosystem.config.js 中

# 设置内存限制
max_memory_restart: '1G'
```

### 3. 应用优化
```bash
# 启用生产模式
NODE_ENV=production

# 优化 Node.js 内存
node_args: '--max-old-space-size=1024'
```

## 🎯 完成检查清单

- [ ] Rocky Linux 9.5 系统已更新
- [ ] Node.js 18+ 已安装
- [ ] PM2 已安装并配置
- [ ] 防火墙已正确配置
- [ ] 应用用户已创建
- [ ] 应用代码已克隆
- [ ] 环境变量已配置
- [ ] 数据库已初始化
- [ ] 应用已构建并启动
- [ ] 健康检查通过
- [ ] 监控脚本已设置
- [ ] 日志轮转已配置

---

## 📞 技术支持

如果遇到问题，请检查：
1. 系统日志：`sudo journalctl -xe`
2. 应用日志：`pm2 logs`
3. 防火墙状态：`sudo firewall-cmd --list-all`
4. 进程状态：`pm2 status`

部署完成后，您的冒险岛情报站将在 Rocky Linux 9.5 上稳定运行！🎉
