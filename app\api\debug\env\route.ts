import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  // 只在开发环境或特定条件下允许访问
  if (process.env.NODE_ENV === 'production' && !request.headers.get('x-debug-key')) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const envInfo = {
    // NextAuth 相关
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET ? '***已设置***' : '未设置',
    AUTH_TRUST_HOST: process.env.AUTH_TRUST_HOST,
    NEXTAUTH_URL_INTERNAL: process.env.NEXTAUTH_URL_INTERNAL,
    
    // 应用配置
    NODE_ENV: process.env.NODE_ENV,
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    
    // 请求信息
    requestUrl: request.url,
    requestHeaders: {
      host: request.headers.get('host'),
      'x-forwarded-proto': request.headers.get('x-forwarded-proto'),
      'x-forwarded-host': request.headers.get('x-forwarded-host'),
      'x-forwarded-for': request.headers.get('x-forwarded-for'),
      'user-agent': request.headers.get('user-agent'),
    },
    
    // 时间戳
    timestamp: new Date().toISOString()
  }

  return NextResponse.json(envInfo, {
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
    }
  })
}
