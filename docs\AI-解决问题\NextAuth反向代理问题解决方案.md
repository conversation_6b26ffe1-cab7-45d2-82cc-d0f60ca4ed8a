# NextAuth.js 反向代理问题解决方案

## 🔍 问题描述

在使用 Nginx 反向代理的生产环境中，NextAuth.js 出现以下问题：
1. 设置 `NEXTAUTH_URL="https://mxd.hyhuman.xyz"` 后无法登录
2. 使用 `http://localhost:3000` 可以登录，但退出时跳转到 localhost

## 🎯 根本原因

1. **协议不匹配**：NextAuth.js 无法正确识别 HTTPS 请求
2. **代理头部缺失**：Nginx 没有传递正确的请求头信息
3. **重定向逻辑错误**：NextAuth.js 使用错误的 baseUrl 进行重定向
4. **Cookie 安全设置**：HTTPS 环境下的 cookie 配置问题

## ✅ 完整解决方案

### 🚀 快速修复（推荐）

在您的 Rocky Linux 服务器上运行：

```bash
# 1. 修复 NextAuth 配置
chmod +x fix-nextauth-proxy.sh
./fix-nextauth-proxy.sh

# 2. 检查 Nginx 配置
chmod +x check-nginx-config.sh
./check-nginx-config.sh

# 3. 调试问题（如果仍有问题）
chmod +x debug-nextauth.sh
./debug-nextauth.sh
```

### 🔧 手动修复步骤

#### 1. 更新环境变量

确保 `.env.local` 包含以下配置：

```bash
# NextAuth 配置
NEXTAUTH_URL="https://mxd.hyhuman.xyz"
NEXTAUTH_SECRET="your-secure-secret-key"

# 信任代理配置 - 关键！
AUTH_TRUST_HOST="true"
NEXTAUTH_URL_INTERNAL="http://localhost:3000"

# 强制使用 HTTPS cookies
NEXTAUTH_COOKIE_SECURE="true"

# 应用配置
NODE_ENV="production"
```

#### 2. 验证 Nginx 配置

确保 `/etc/nginx/sites-available/mxd.https.conf` 包含：

```nginx
server {
    listen 443 ssl http2;
    server_name mxd.hyhuman.xyz;
    
    # SSL 配置...
    
    location / {
        proxy_pass http://localhost:3000;
        
        # 基本代理配置
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
        
        # 关键：NextAuth.js 需要的头部
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
    }
}
```

#### 3. 更新 NextAuth 配置

确保 `lib/auth.ts` 包含：

```typescript
export const { handlers, auth, signIn, signOut } = NextAuth({
  trustHost: true, // 信任代理服务器
  useSecureCookies: process.env.NODE_ENV === "production",
  
  callbacks: {
    async redirect({ url, baseUrl }) {
      // 处理重定向逻辑
      if (url.startsWith("/")) {
        return `${baseUrl}${url}`
      }
      
      if (new URL(url).origin === baseUrl) {
        return url
      }
      
      return baseUrl
    }
  }
})
```

#### 4. 重启服务

```bash
# 重新加载 Nginx
sudo systemctl reload nginx

# 重启应用
pkill -f "next start"
pnpm start
```

## 🔍 故障排除

### 检查清单

- [ ] `NEXTAUTH_URL` 设置为 `https://mxd.hyhuman.xyz`
- [ ] `AUTH_TRUST_HOST` 设置为 `true`
- [ ] Nginx 包含所有必要的代理头部
- [ ] SSL 证书有效且正确配置
- [ ] 应用在端口 3000 正常运行

### 调试命令

```bash
# 1. 测试本地连接
curl -I http://localhost:3000/api/auth/signin

# 2. 测试 HTTPS 连接
curl -I https://mxd.hyhuman.xyz/api/auth/signin

# 3. 检查重定向
curl -I https://mxd.hyhuman.xyz/api/auth/signout

# 4. 查看应用日志
tail -f ~/.pm2/logs/maplestory-info-out.log

# 5. 查看 Nginx 日志
sudo tail -f /var/log/nginx/error.log
```

### 常见问题

#### 1. 登录后立即退出
**原因**：Cookie 安全设置问题
**解决**：确保 `NEXTAUTH_COOKIE_SECURE="true"`

#### 2. 重定向到 localhost
**原因**：NextAuth.js 无法识别真实域名
**解决**：检查 Nginx 代理头部配置

#### 3. CSRF 错误
**原因**：请求来源验证失败
**解决**：确保 `trustHost: true` 和正确的环境变量

#### 4. Session 丢失
**原因**：Cookie 域名不匹配
**解决**：检查 cookie 域名设置

## 📋 验证步骤

修复后，按以下步骤验证：

1. **访问登录页面**：`https://mxd.hyhuman.xyz/login`
2. **尝试登录**：使用用户名/密码登录
3. **检查会话**：访问 `https://mxd.hyhuman.xyz/api/auth/session`
4. **测试登出**：点击登出按钮
5. **验证重定向**：确保重定向到正确的 HTTPS 地址

## 🚨 紧急修复

如果问题紧急，可以临时使用以下配置：

```bash
# 临时环境变量
export NEXTAUTH_URL="https://mxd.hyhuman.xyz"
export AUTH_TRUST_HOST="true"
export NODE_ENV="production"

# 启动应用
pnpm start
```

## 📞 技术支持

如果问题仍然存在：

1. **收集日志**：
   ```bash
   # 应用日志
   DEBUG="next-auth:*" pnpm start
   
   # Nginx 日志
   sudo tail -f /var/log/nginx/access.log
   sudo tail -f /var/log/nginx/error.log
   ```

2. **检查网络**：
   ```bash
   # 检查 DNS 解析
   nslookup mxd.hyhuman.xyz
   
   # 检查 SSL 证书
   openssl s_client -connect mxd.hyhuman.xyz:443
   ```

3. **验证配置**：
   ```bash
   # 检查环境变量
   printenv | grep NEXTAUTH
   
   # 检查 Nginx 配置
   sudo nginx -t
   ```

## 🎉 成功标志

修复成功后，您应该能够：

- ✅ 在 `https://mxd.hyhuman.xyz` 正常登录
- ✅ 登录后保持会话状态
- ✅ 登出后重定向到正确的 HTTPS 地址
- ✅ 所有 NextAuth API 端点正常工作

---

**总结**：这个问题主要是由于 NextAuth.js 在反向代理环境下无法正确识别请求协议和域名导致的。通过配置正确的环境变量、Nginx 代理头部和 NextAuth 选项，可以完全解决这个问题。
