import { NextResponse } from 'next/server'
import { getLoginFieldConfig, isAuthProviderEnabled } from '@/lib/auth-config'

export async function GET() {
  try {
    const fieldConfig = getLoginFieldConfig()
    
    const config = {
      showEmailField: fieldConfig.showEmailField,
      showUsernameField: fieldConfig.showUsernameField,
      showGoogleButton: fieldConfig.showGoogleButton,
      emailPlaceholder: fieldConfig.emailPlaceholder,
      loginFieldLabel: fieldConfig.loginFieldLabel,
      credentialsEnabled: isAuthProviderEnabled('credentials'),
      googleEnabled: isAuthProviderEnabled('google')
    }

    return NextResponse.json(config)
  } catch (error) {
    console.error('Error getting auth config:', error)
    
    // 返回默认配置
    return NextResponse.json({
      showEmailField: true,
      showUsernameField: false,
      showGoogleButton: false,
      emailPlaceholder: '邮箱地址',
      loginFieldLabel: '邮箱地址',
      credentialsEnabled: true,
      googleEnabled: false
    })
  }
}
