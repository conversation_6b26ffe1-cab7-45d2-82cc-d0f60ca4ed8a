import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { v4 as uuidv4 } from 'uuid'
import { 
  EnhancementRequest, 
  EnhancementResponse, 
  EnhancementErrorResponse,
  EnhancementErrorCode 
} from '@/types/enhancement-api'
import { EnhancementType, EnhancementResultType } from '@/types/enhancement'

/**
 * 装备强化API接口
 * 
 * @description 处理装备强化请求，支持星力强化、潜能重设、额外属性强化
 * @route POST /api/enhancement/enhance
 * @access Public (支持API密钥验证)
 * 
 * @param {EnhancementRequest} body - 强化请求参数
 * @returns {EnhancementResponse} 强化结果
 * 
 * @example
 * ```typescript
 * const response = await fetch('/api/enhancement/enhance', {
 *   method: 'POST',
 *   headers: {
 *     'Content-Type': 'application/json',
 *     'X-API-Key': 'your-api-key' // 可选
 *   },
 *   body: JSON.stringify({
 *     equipmentId: 'weapon_001',
 *     currentLevel: 10,
 *     enhancementType: 'starforce',
 *     starcatchEnabled: false,
 *     preventEnabled: true
 *   })
 * })
 * ```
 */

// 请求参数验证schema
const enhancementRequestSchema = z.object({
  equipmentId: z.string().min(1, '装备ID不能为空'),
  currentLevel: z.number().int().min(0, '当前等级不能小于0').max(25, '当前等级不能大于25'),
  enhancementType: z.enum(['starforce', 'potential', 'bonusstat'], {
    errorMap: () => ({ message: '强化类型必须是 starforce、potential 或 bonusstat' })
  }),
  starcatchEnabled: z.boolean().optional().default(false),
  preventEnabled: z.boolean().optional().default(false),
  minigameBonus: z.number().min(0).max(20).optional().default(0)
})

// 简化的强化概率配置
const ENHANCEMENT_PROBABILITIES = {
  starforce: {
    success: 80,
    failure: 10,
    majorFailure: 5,
    failureDrop: 5
  },
  potential: {
    success: 100,
    failure: 0,
    majorFailure: 0,
    failureDrop: 0
  },
  bonusstat: {
    success: 100,
    failure: 0,
    majorFailure: 0,
    failureDrop: 0
  }
}

// 强化费用配置
const ENHANCEMENT_COSTS = {
  starforce: (level: number) => Math.floor(1000000 * Math.pow(1.2, level)),
  potential: () => 500000,
  bonusstat: () => 300000
}

/**
 * 验证API密钥（简化版）
 */
async function validateApiKey(apiKey: string | null): Promise<boolean> {
  if (!apiKey) return true // 允许无密钥访问
  
  // 简化的API密钥验证
  // 在实际项目中，这里应该查询数据库验证密钥
  const validKeys = ['demo-key-123', 'test-key-456']
  return validKeys.includes(apiKey)
}

/**
 * 计算强化结果
 */
function calculateEnhancementResult(
  enhancementType: EnhancementType,
  currentLevel: number,
  options: {
    starcatchEnabled: boolean
    preventEnabled: boolean
    minigameBonus: number
  }
): {
  type: EnhancementResultType
  newLevel: number
  message: string
} {
  const probabilities = ENHANCEMENT_PROBABILITIES[enhancementType]
  const random = Math.random() * 100
  
  // 应用迷你游戏加成
  let adjustedSuccessRate = probabilities.success + options.minigameBonus
  
  if (random < adjustedSuccessRate) {
    // 成功
    const newLevel = enhancementType === 'starforce' ? currentLevel + 1 : currentLevel
    return {
      type: 'success',
      newLevel,
      message: enhancementType === 'starforce' 
        ? `强化成功！星力等级提升至 ${newLevel}` 
        : `${enhancementType === 'potential' ? '潜能重设' : '额外属性强化'}成功！`
    }
  } else if (random < adjustedSuccessRate + probabilities.failure) {
    // 失败保级
    return {
      type: 'failed',
      newLevel: currentLevel,
      message: '强化失败，装备等级未发生变化'
    }
  } else if (random < adjustedSuccessRate + probabilities.failure + probabilities.majorFailure) {
    // 大失败（装备损坏）
    if (options.preventEnabled && enhancementType === 'starforce') {
      // 防止破坏生效
      return {
        type: 'failed',
        newLevel: currentLevel,
        message: '强化失败，防止破坏生效，装备未损坏'
      }
    }
    
    const newLevel = Math.max(0, currentLevel - 1)
    return {
      type: 'major_failure',
      newLevel,
      message: `装备损坏！星力等级降低至 ${newLevel}`
    }
  } else {
    // 失败降级
    const newLevel = Math.max(0, currentLevel - 1)
    return {
      type: 'failed',
      newLevel,
      message: `强化失败！星力等级降低至 ${newLevel}`
    }
  }
}

/**
 * 生成错误响应
 */
function createErrorResponse(
  error: string,
  errorCode: EnhancementErrorCode,
  requestId: string,
  details?: Record<string, any>
): NextResponse<EnhancementErrorResponse> {
  return NextResponse.json({
    success: false,
    error,
    errorCode,
    details,
    requestId,
    timestamp: new Date().toISOString()
  }, { status: 400 })
}

/**
 * POST /api/enhancement/enhance
 * 处理装备强化请求
 */
export async function POST(request: NextRequest) {
  const requestId = uuidv4()
  const startTime = Date.now()
  
  console.log(`🔨 [${requestId}] 开始处理装备强化请求`)

  try {
    // 验证API密钥
    const apiKey = request.headers.get('X-API-Key')
    const isValidKey = await validateApiKey(apiKey)
    
    if (!isValidKey) {
      console.log(`❌ [${requestId}] API密钥验证失败`)
      return createErrorResponse(
        'Invalid API key',
        EnhancementErrorCode.INVALID_API_KEY,
        requestId
      )
    }

    // 解析请求体
    const body = await request.json()
    console.log(`📝 [${requestId}] 请求参数:`, { 
      ...body, 
      equipmentId: body.equipmentId?.substring(0, 10) + '...' 
    })

    // 验证请求参数
    const validationResult = enhancementRequestSchema.safeParse(body)
    if (!validationResult.success) {
      console.log(`❌ [${requestId}] 参数验证失败:`, validationResult.error.errors)
      return createErrorResponse(
        '请求参数验证失败',
        EnhancementErrorCode.VALIDATION_ERROR,
        requestId,
        { validationErrors: validationResult.error.errors }
      )
    }

    const {
      equipmentId,
      currentLevel,
      enhancementType,
      starcatchEnabled,
      preventEnabled,
      minigameBonus
    } = validationResult.data

    // 检查等级限制
    if (enhancementType === 'starforce' && currentLevel >= 25) {
      console.log(`❌ [${requestId}] 已达到最大强化等级`)
      return createErrorResponse(
        '装备已达到最大强化等级',
        EnhancementErrorCode.MAX_LEVEL_REACHED,
        requestId,
        { currentLevel, maxLevel: 25 }
      )
    }

    // 计算强化费用
    const cost = ENHANCEMENT_COSTS[enhancementType](currentLevel)
    
    // 获取强化概率
    const probabilities = ENHANCEMENT_PROBABILITIES[enhancementType]
    
    // 计算强化结果
    const enhancementResult = calculateEnhancementResult(
      enhancementType,
      currentLevel,
      { starcatchEnabled, preventEnabled, minigameBonus }
    )

    const duration = Date.now() - startTime
    console.log(`✅ [${requestId}] 强化完成 - 结果: ${enhancementResult.type}, 耗时: ${duration}ms`)

    // 构建响应
    const response: EnhancementResponse = {
      success: true,
      result: {
        type: enhancementResult.type,
        message: enhancementResult.message,
        newLevel: enhancementResult.newLevel,
        previousLevel: currentLevel,
        isSuccess: enhancementResult.type === 'success'
      },
      cost: {
        mesos: cost,
        materials: enhancementType === 'starforce' ? [
          { name: '星之力', quantity: 1 }
        ] : enhancementType === 'potential' ? [
          { name: '潜能重设卷轴', quantity: 1 }
        ] : [
          { name: '额外属性重设卷轴', quantity: 1 }
        ]
      },
      probability: {
        success: probabilities.success + minigameBonus,
        failure: probabilities.failure,
        majorFailure: probabilities.majorFailure,
        failureDrop: probabilities.failureDrop
      },
      requestId,
      timestamp: new Date().toISOString()
    }

    return NextResponse.json(response)

  } catch (error) {
    const duration = Date.now() - startTime
    console.error(`❌ [${requestId}] 强化处理异常 (耗时: ${duration}ms):`, error)

    return NextResponse.json({
      success: false,
      error: '服务器内部错误',
      errorCode: EnhancementErrorCode.INTERNAL_ERROR,
      requestId,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * GET /api/enhancement/enhance
 * 获取API文档信息
 */
export async function GET() {
  return NextResponse.json({
    name: 'Enhancement API',
    version: '1.0.0',
    description: '冒险岛装备强化API接口',
    endpoints: {
      enhance: {
        method: 'POST',
        path: '/api/enhancement/enhance',
        description: '执行装备强化',
        parameters: {
          equipmentId: 'string - 装备ID',
          currentLevel: 'number - 当前强化等级 (0-25)',
          enhancementType: 'string - 强化类型 (starforce|potential|bonusstat)',
          starcatchEnabled: 'boolean - 是否启用解锁抓星星 (可选)',
          preventEnabled: 'boolean - 是否启用防止破坏 (可选)',
          minigameBonus: 'number - 迷你游戏加成 (0-20, 可选)'
        },
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': 'string - API密钥 (可选)'
        }
      }
    },
    examples: {
      starforce: {
        request: {
          equipmentId: 'weapon_001',
          currentLevel: 10,
          enhancementType: 'starforce',
          starcatchEnabled: false,
          preventEnabled: true
        }
      },
      potential: {
        request: {
          equipmentId: 'armor_001',
          currentLevel: 0,
          enhancementType: 'potential'
        }
      }
    }
  })
}
