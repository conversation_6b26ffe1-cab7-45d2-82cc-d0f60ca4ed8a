import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { v4 as uuidv4 } from 'uuid'
import {
  EnhancementRequest,
  EnhancementResponse,
  EnhancementErrorResponse,
  EnhancementErrorCode
} from '@/types/enhancement-api'
import { EnhancementType, EnhancementResultType } from '@/types/enhancement'
import {
  calculateStarforceResult,
  StarforceResult,
  getStarforceInfo
} from '@/lib/starforce-algorithm'
import { exportProbabilityDataAsJSON } from '@/lib/starforce-data-loader'

/**
 * 装备强化API接口
 * 
 * @description 处理装备强化请求，支持星力强化、潜能重设、额外属性强化
 * @route POST /api/enhancement/enhance
 * @access Public (支持API密钥验证)
 * 
 * @param {EnhancementRequest} body - 强化请求参数
 * @returns {EnhancementResponse} 强化结果
 * 
 * @example
 * ```typescript
 * const response = await fetch('/api/enhancement/enhance', {
 *   method: 'POST',
 *   headers: {
 *     'Content-Type': 'application/json',
 *     'X-API-Key': 'your-api-key' // 可选
 *   },
 *   body: JSON.stringify({
 *     equipmentId: 'weapon_001',
 *     currentLevel: 10,
 *     enhancementType: 'starforce',
 *     starcatchEnabled: false,
 *     preventEnabled: true
 *   })
 * })
 * ```
 */

// 请求参数验证schema
const enhancementRequestSchema = z.object({
  equipmentId: z.string().min(1, '装备ID不能为空'),
  currentLevel: z.number().int().min(0, '当前等级不能小于0').max(25, '当前等级不能大于25'),
  enhancementType: z.enum(['starforce', 'potential', 'bonusstat'], {
    errorMap: () => ({ message: '强化类型必须是 starforce、potential 或 bonusstat' })
  }),
  starcatchEnabled: z.boolean().optional().default(false),
  preventEnabled: z.boolean().optional().default(false),
  minigameBonus: z.number().min(0).max(20).optional().default(0)
})

// 非星力强化的固定概率配置
const NON_STARFORCE_PROBABILITIES = {
  potential: {
    success: 100,
    failure: 0,
    majorFailure: 0,
    failureDrop: 0
  },
  bonusstat: {
    success: 100,
    failure: 0,
    majorFailure: 0,
    failureDrop: 0
  }
}

// 星力强化费用配置（基于真实游戏数据）
const STARFORCE_COSTS: Record<number, number> = {
  0: 1000000, 1: 2000000, 2: 4000000, 3: 8000000, 4: 16000000,
  5: 32000000, 6: 64000000, 7: 128000000, 8: 256000000, 9: 512000000,
  10: 1024000000, 11: 2048000000, 12: 4096000000, 13: 8192000000, 14: 16384000000,
  15: 32768000000, 16: 65536000000, 17: 131072000000, 18: 262144000000, 19: 524288000000,
  20: 1048576000000, 21: 2097152000000, 22: 4194304000000, 23: 8388608000000, 24: 16777216000000,
}

// 强化费用配置
const ENHANCEMENT_COSTS = {
  starforce: (level: number) => STARFORCE_COSTS[level] || 0,
  potential: () => 500000,
  bonusstat: () => 300000
}

/**
 * 验证API密钥（简化版）
 */
async function validateApiKey(apiKey: string | null): Promise<boolean> {
  if (!apiKey) return true // 允许无密钥访问
  
  // 简化的API密钥验证
  // 在实际项目中，这里应该查询数据库验证密钥
  const validKeys = ['demo-key-123', 'test-key-456']
  return validKeys.includes(apiKey)
}

/**
 * 计算强化结果
 */
function calculateEnhancementResult(
  enhancementType: EnhancementType,
  currentLevel: number,
  options: {
    starcatchEnabled: boolean
    preventEnabled: boolean
    minigameBonus: number
  }
): {
  type: EnhancementResultType
  newLevel: number
  message: string
} {
  if (enhancementType === 'starforce') {
    try {
      // 获取概率数据
      const probabilityData = exportProbabilityDataAsJSON()

      // 使用真实的星力强化算法
      const starforceResult = calculateStarforceResult(
        currentLevel,
        options.starcatchEnabled,
        options.preventEnabled,
        probabilityData
      )

      // 转换星力结果到API结果格式
      switch (starforceResult.result) {
        case StarforceResult.SUCCESS:
          return {
            type: 'success',
            newLevel: starforceResult.newLevel,
            message: `强化成功！星力等级提升至 ${starforceResult.newLevel}`
          }
        case StarforceResult.FAIL_HOLD:
          return {
            type: 'failed',
            newLevel: starforceResult.newLevel,
            message: '强化失败，装备等级未发生变化'
          }
        case StarforceResult.FAIL_DROP:
          return {
            type: 'failed',
            newLevel: starforceResult.newLevel,
            message: `强化失败！星力等级降低至 ${starforceResult.newLevel}`
          }
        case StarforceResult.BOOM:
          if (options.preventEnabled) {
            // 防止破坏生效，转为失败保级
            return {
              type: 'failed',
              newLevel: currentLevel,
              message: '强化失败，防止破坏生效，装备未损坏'
            }
          } else {
            return {
              type: 'major_failure',
              newLevel: starforceResult.newLevel,
              message: `装备损坏！星力等级重置至 ${starforceResult.newLevel}`
            }
          }
        default:
          throw new Error(`未知的星力强化结果: ${starforceResult.result}`)
      }
    } catch (error) {
      console.error('星力强化计算失败:', error)
      // 降级到简化算法
      return {
        type: 'failed',
        newLevel: currentLevel,
        message: '强化失败，系统错误'
      }
    }
  } else {
    // 潜能和额外属性总是成功
    return {
      type: 'success',
      newLevel: currentLevel,
      message: `${enhancementType === 'potential' ? '潜能重设' : '额外属性强化'}成功！`
    }
  }
}

/**
 * 生成错误响应
 */
function createErrorResponse(
  error: string,
  errorCode: EnhancementErrorCode,
  requestId: string,
  details?: Record<string, any>
): NextResponse<EnhancementErrorResponse> {
  return NextResponse.json({
    success: false,
    error,
    errorCode,
    details,
    requestId,
    timestamp: new Date().toISOString()
  }, { status: 400 })
}

/**
 * POST /api/enhancement/enhance
 * 处理装备强化请求
 */
export async function POST(request: NextRequest) {
  const requestId = uuidv4()
  const startTime = Date.now()
  
  console.log(`🔨 [${requestId}] 开始处理装备强化请求`)

  try {
    // 验证API密钥
    const apiKey = request.headers.get('X-API-Key')
    const isValidKey = await validateApiKey(apiKey)
    
    if (!isValidKey) {
      console.log(`❌ [${requestId}] API密钥验证失败`)
      return createErrorResponse(
        'Invalid API key',
        EnhancementErrorCode.INVALID_API_KEY,
        requestId
      )
    }

    // 解析请求体
    const body = await request.json()
    console.log(`📝 [${requestId}] 请求参数:`, { 
      ...body, 
      equipmentId: body.equipmentId?.substring(0, 10) + '...' 
    })

    // 验证请求参数
    const validationResult = enhancementRequestSchema.safeParse(body)
    if (!validationResult.success) {
      console.log(`❌ [${requestId}] 参数验证失败:`, validationResult.error.errors)
      return createErrorResponse(
        '请求参数验证失败',
        EnhancementErrorCode.VALIDATION_ERROR,
        requestId,
        { validationErrors: validationResult.error.errors }
      )
    }

    const {
      equipmentId,
      currentLevel,
      enhancementType,
      starcatchEnabled,
      preventEnabled,
      minigameBonus
    } = validationResult.data

    // 检查等级限制
    if (enhancementType === 'starforce' && currentLevel >= 25) {
      console.log(`❌ [${requestId}] 已达到最大强化等级`)
      return createErrorResponse(
        '装备已达到最大强化等级',
        EnhancementErrorCode.MAX_LEVEL_REACHED,
        requestId,
        { currentLevel, maxLevel: 25 }
      )
    }

    // 计算强化费用
    const cost = ENHANCEMENT_COSTS[enhancementType](currentLevel)
    
    // 获取强化概率
    let probabilities: any
    if (enhancementType === 'starforce') {
      try {
        const probabilityData = exportProbabilityDataAsJSON()
        const starforceInfo = getStarforceInfo(currentLevel, {
          starcatchEnabled,
          preventEnabled
        }, probabilityData)
        // 转换概率格式（从0-1转为0-100）
        probabilities = {
          success: starforceInfo.finalProbability.success * 100,
          failure: starforceInfo.finalProbability.failHold * 100,
          majorFailure: starforceInfo.finalProbability.boom * 100,
          failureDrop: starforceInfo.finalProbability.failDrop * 100
        }
      } catch (error) {
        console.error('获取星力概率失败:', error)
        // 降级到默认概率
        probabilities = { success: 50, failure: 30, majorFailure: 10, failureDrop: 10 }
      }
    } else {
      probabilities = NON_STARFORCE_PROBABILITIES[enhancementType]
    }
    
    // 计算强化结果
    const enhancementResult = calculateEnhancementResult(
      enhancementType,
      currentLevel,
      { starcatchEnabled, preventEnabled, minigameBonus }
    )

    const duration = Date.now() - startTime
    console.log(`✅ [${requestId}] 强化完成 - 结果: ${enhancementResult.type},${enhancementResult.message}, 耗时: ${duration}ms`)

    // 构建响应
    const response: EnhancementResponse = {
      success: true,
      result: {
        type: enhancementResult.type,
        message: enhancementResult.message,
        newLevel: enhancementResult.newLevel,
        previousLevel: currentLevel,
        isSuccess: enhancementResult.type === 'success'
      },
      cost: {
        mesos: cost,
        materials: enhancementType === 'starforce' ? [
          { name: '星之力', quantity: 1 }
        ] : enhancementType === 'potential' ? [
          { name: '潜能重设卷轴', quantity: 1 }
        ] : [
          { name: '额外属性重设卷轴', quantity: 1 }
        ]
      },
      probability: {
        success: probabilities.success + minigameBonus,
        failure: probabilities.failure,
        majorFailure: probabilities.majorFailure,
        failureDrop: probabilities.failureDrop
      },
      requestId,
      timestamp: new Date().toISOString()
    }

    return NextResponse.json(response)

  } catch (error) {
    const duration = Date.now() - startTime
    console.error(`❌ [${requestId}] 强化处理异常 (耗时: ${duration}ms):`, error)

    return NextResponse.json({
      success: false,
      error: '服务器内部错误',
      errorCode: EnhancementErrorCode.INTERNAL_ERROR,
      requestId,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * GET /api/enhancement/enhance
 * 获取API文档信息
 */
export async function GET() {
  return NextResponse.json({
    name: 'Enhancement API',
    version: '1.0.0',
    description: '冒险岛装备强化API接口',
    endpoints: {
      enhance: {
        method: 'POST',
        path: '/api/enhancement/enhance',
        description: '执行装备强化',
        parameters: {
          equipmentId: 'string - 装备ID',
          currentLevel: 'number - 当前强化等级 (0-25)',
          enhancementType: 'string - 强化类型 (starforce|potential|bonusstat)',
          starcatchEnabled: 'boolean - 是否启用解锁抓星星 (可选)',
          preventEnabled: 'boolean - 是否启用防止破坏 (可选)',
          minigameBonus: 'number - 迷你游戏加成 (0-20, 可选)'
        },
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': 'string - API密钥 (可选)'
        }
      }
    },
    examples: {
      starforce: {
        request: {
          equipmentId: 'weapon_001',
          currentLevel: 10,
          enhancementType: 'starforce',
          starcatchEnabled: false,
          preventEnabled: true
        }
      },
      potential: {
        request: {
          equipmentId: 'armor_001',
          currentLevel: 0,
          enhancementType: 'potential'
        }
      }
    }
  })
}
