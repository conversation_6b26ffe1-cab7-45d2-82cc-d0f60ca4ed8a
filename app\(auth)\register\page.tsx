import { Metadata } from 'next'
import { EnhancedRegisterForm } from '@/components/auth/EnhancedRegisterForm'
import { getAuthConfig, isAuthProviderEnabled } from '@/lib/auth-config'

export const metadata: Metadata = {
  title: '用户注册 - 冒险岛情报站',
  description: '创建新账户以享受完整功能',
}

export default function RegisterPage() {
  const config = getAuthConfig()

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-lg shadow-lg p-8">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          创建新账户
        </h2>
        <p className="text-gray-600">
          注册以享受完整的功能体验
        </p>
        {(isAuthProviderEnabled('email') && isAuthProviderEnabled('username')) && (
          <p className="text-sm text-gray-500 mt-2">
            支持邮箱和用户名两种注册方式
          </p>
        )}
      </div>

      <EnhancedRegisterForm />

      <div className="mt-6 text-center">
        <p className="text-sm text-gray-600">
          已有账户？{' '}
          <a
            href="/login"
            className="text-blue-600 hover:text-blue-500 font-medium"
          >
            立即登录
          </a>
        </p>
      </div>
    </div>
  )
}
