写一个AI编程工具的提示词。内容是：写出当前工作目录项目的详细的说明文档和开发文档

正常的流程应该是先把你的框架让AI写出详细的说明文档和开发文档
然后工具也要详细的说明文档和开发文档，让AI阅读两个文档，进行合并
AI读文件都为了省事只读一点点，所以代码量大一点的项目，AI是不懂装懂
但是通过说明文档可以让他不知道咋办的时候自己去文档里找


# 分析整个 MapleStory 信息站项目，并创建一个包含以下详细章节的综合 README.md 文件：

1. **项目结构分析**：
- 检查目录组织和文件夹层次结构
- 确定组件和页面的组织方式
- 分析所使用的模块化设计模式
- 记录代码库不同部分之间的关​​注点分离

2. **页面和路由分析**：
- 分析 pages 目录中的路由结构
- 确定项目使用的是 Next.js 应用路由器 (13+) 还是 Pages 路由器
- 记录所有动态路由（使用 [param] 语法）
- 记录所有嵌套路由模式
- 规划完整的路由结构

3. **功能实现分析**：
- 识别并记录所有主要功能模块（例如，用户身份验证、数据显示、搜索功能、详情页面等）
- 对于每个功能，描述：
- 涉及哪些页面和组件
- 数据流和状态管理方法
- 使用的 API 端点或数据源
- 使用的关键依赖项和库

4. **文档输出**：
- 在项目根目录中创建或更新 README.md 文件
- 使用清晰的标题和章节组织内容
- 在相关位置添加代码示例
- 提供清晰的概述，帮助新开发者快速理解项目

请首先探索代码库结构，然后系统地分析路由和功能，最后将所有发现整理成一个条理清晰的 README.md 文件。


# 基于你刚才完成的 MapleStory 信息站项目分析，现在需要将一个现有的 HTML 装备强化模拟器集成到当前的 Next.js 项目中。

**任务目标：**
将位于 `html/mxd-tools-enhancement` 目录的 MSU 装备强化模拟器完整迁移并集成到当前 Next.js 项目中。

**前置条件检查：**
1. 首先检查 `html/mxd-tools-enhancement` 目录是否存在且可访问
2. 查看该目录下的 README.md 文件了解项目详情
3. 分析现有 HTML/CSS/JavaScript 代码结构和功能
4. 确认图片资源是否位于 `public/assets/UIEquipEnchant/` 目录

**具体实施要求：**

1. **入口集成：**
    - 在现有的 `/tools` 页面中找到"装备工具栏"区域
    - 添加"装备强化模拟器"入口，保持与现有工具一致的视觉风格
    - 创建路由 `/tools/enhancement` 作为模拟器页面

2. **功能迁移：**
    - 完整保留原 HTML 版本的所有功能特性
    - 使用 React 组件化架构重构所有交互逻辑
    - 保持原有的用户界面布局和交互体验
    - 确保所有计算逻辑和数据处理准确迁移

3. **技术实现：**
    - 使用 TypeScript 进行类型安全开发
    - 采用 Tailwind CSS 进行样式实现
    - 遵循项目现有的组件结构和命名规范
    - 使用 shadcn/ui 组件库保持设计一致性

4. **代码组织：**
    - 主页面组件：`app/tools/enhancement/page.tsx`
    - 模拟器组件：`components/enhancement-simulator/` 目录
    - 类型定义：`types/enhancement.ts`
    - 工具函数：`lib/enhancement-utils.ts`
    - 静态资源：确认并使用 `public/assets/UIEquipEnchant/` 中的图片

5. **集成步骤：**
    - 分析原始代码的功能模块和数据结构
    - 设计 React 组件架构
    - 实现核心业务逻辑
    - 集成到现有项目结构
    - 测试功能完整性和用户体验

**输出要求：**
如果无法访问源代码目录，请明确说明并停止任务。
如果可以访问，请提供：
1. 源代码分析报告
2. 详细的迁移计划和步骤
3. 完整的代码实现
4. 集成测试建议

请按照这个顺序逐步执行，确保每个步骤都符合当前项目的架构和代码规范。




# 3

请你担任一位专业的技术文档工程师和资深开发者。
基于当前工作目录中的项目文件结构、源代码和配置文件内容，为我生成两部分文档：项目说明文档（README 级别）、开发文档（Developer Guide）

📘 项目说明文档（README 级别）
项目名称与简介
项目功能与特性
技术栈说明
依赖环境与安装步骤
启动与使用方式
示例截图或接口演示（如果能分析出来）

🧑‍💻 开发文档（Developer Guide）
项目目录结构说明
主要模块/组件功能与职责
配置文件作用说明
开发、调试、构建流程说明
接口或 API 说明（如有）
常见问题与调试建议

基于工作区中当前的 MapleStory 信息站项目，分析完整的代码库结构、源代码、配置文件和现有文档，生成两个全面的文档文件：
要求：使用中文生成文档。
1. **项目 README 文档** (`README.md`)：
- 项目概述和目的（此 MapleStory 工具/信息站的功能）
- 主要特性和功能
- 技术栈（Next.js、React、TypeScript、Tailwind CSS、shadcn/ui 等）
- 安装和设置说明
- 使用示例和屏幕截图（如适用）
- 项目结构概述
- 贡献指南
- 许可证信息

2. **开发者指南文档** (`DEVELOPER_GUIDE.md`)：
- 详细的项目架构和文件组织
- 所使用的代码结构和模式（尤其是工具集成模式：app/tools/[tool]/page.tsx、components/[tool]-simulator/ 等）
- 组件层次结构和关系
- 数据模型和类型（尤其是 MapleStory 相关类型）
- API端点和数据流
- 开发工作流程和最佳实践
- 测试方法和指南
- 构建和部署流程
- 常见问题排查
- 代码风格和规范

请彻底分析现有代码库，包括所有 React 组件、TypeScript 类型、实用函数、配置文件以及所有现有文档。确保文档准确反映项目的当前状态并遵循专业的文档标准。请重点关注 MapleStory 特有的功能和工具，因为它们似乎是此应用程序的核心目的。

# 4
基于当前的冒险岛情报站项目（使用 Next.js 14 App Router，配置为静态导出 SSG），我需要了解以下关于 API 集成和部署的详细信息：

1. **API 集成可行性分析**：
   - 在当前 SSG 配置下添加 API 路由是否可行？
   - 如果添加 API 路由，对现有的静态生成配置（output: 'export'）会产生什么影响？
   - 哪些类型的 API 功能与 SSG 兼容，哪些不兼容？

2. **架构调整方案**：
   - 如果要保持部分页面的 SSG 特性同时支持 API，需要如何调整 next.config.mjs 配置？
   - 混合部署模式（SSG + API）的最佳实践是什么？
   - 是否需要将项目拆分为前端静态站点和独立的 API 服务？

3. **部署策略对比**：
   - 纯 SSG 部署 vs 混合部署的优缺点分析
   - 不同部署方案对应的托管平台选择（Vercel、Netlify、nginx、自建服务器等）
   - 各种部署方案的成本、性能和维护复杂度对比

4. **具体实施路径**：
   - 从当前 SSG 配置迁移到支持 API 的配置需要哪些步骤？
   - 如何确保迁移过程中不影响现有功能？
   - 部署流程和 CI/CD 配置需要做哪些调整？

请提供详细的技术说明和建议，重点关注对现有项目的影响和最优的解决方案。暂时不需要提供具体的代码实现，只需要清晰的技术方案和决策指导。

# 5
基于我们之前讨论的冒险岛情报站项目（Next.js 14 + SSG配置），我现在需要为项目添加复杂的后端功能，包括用户认证和权限管理系统。考虑到API的复杂性，我正在评估是否应该采用Python FastAPI作为后端解决方案。

请为我提供以下详细分析：

1. **技术栈选择分析**：
   - 对比 Next.js API Routes vs Python FastAPI 在处理复杂认证系统方面的优劣
   - 分析 FastAPI 相比其他 Python 框架（Django、Flask）的优势
   - 评估 Python 后端与现有 Next.js 前端的集成复杂度

2. **FastAPI 用户认证系统架构设计**：
   - 完整的用户认证流程设计（注册、登录、JWT令牌管理、刷新机制）
   - 权限管理系统架构（RBAC模型、权限继承、资源访问控制）
   - 数据库设计方案（用户表、角色表、权限表的关系设计）
   - 安全性考虑（密码加密、CORS配置、API限流、输入验证）

3. **与现有项目的集成方案**：
   - 前后端分离架构下的通信机制设计
   - 现有 SSG 页面如何与认证系统集成
   - 用户状态管理在前端的实现策略
   - 开发环境和生产环境的配置差异

4. **部署和运维策略**：
   - 前后端分离部署的最佳实践
   - 数据库选择和部署策略
   - 监控、日志和备份方案

5. **迁移路径和风险评估**：
   - 从当前纯前端项目到前后端分离的迁移步骤
   - 开发团队技能要求和学习成本
   - 项目复杂度和维护成本的变化
   - 性能影响和优化策略
6. 在当前项目添加backend目录，然后添加fastapi相关代码。你作为AI编程agent，能否同时胜任前端后端的开发。且开发语言是两种。

请重点关注技术方案的可行性、实施难度、长期维护性，以及对现有冒险岛情报站项目功能和用户体验的影响。不需要提供具体代码实现，只需要清晰的架构设计思路和技术决策指导。


我的api比较复杂，包含用户认证，权限管理。是不是使用python写后端比较好一些？比如使用fastapi。
如果使用fastapi实现一个完整的用户认证系统。你打算怎么做？
在当前项目添加backend目录，然后添加fastapi相关代码。你作为AI编程agent，能否同时胜任前端后端的开发。且开发语言是两种。
请提供详细的技术说明和建议，重点关注对现有项目的影响和最优的解决方案。 暂时不需要提供具体的代码实现，只需要清晰的技术方案和决策指导。


基于当前的冒险岛情报站项目（Next.js 14 + TypeScript + SSG配置），我需要为项目添加复杂的后端API功能，包括用户认证和权限管理系统。请分析以下技术方案并提供详细的架构建议：

1. **后端技术栈选择分析**：
   - 对比 FastAPI (Python) vs Next.js API Routes (Node.js) 在以下方面的优劣：
      * 用户认证系统的实现复杂度
      * 权限管理和角色控制的灵活性
      * 与现有 Next.js 前端的集成难度
      * 开发和维护成本
      * 性能和扩展性考虑

2. **项目架构设计方案**：
   - 在当前项目根目录下添加 `backend/` 目录的可行性分析
   - 前后端分离架构 vs 全栈 Next.js 架构的对比
   - 数据库选择建议（考虑用户数据、游戏数据存储需求）
   - API 设计规范和版本管理策略
   - 跨域处理和安全性考虑

3. **用户认证系统设计**：
   - JWT vs Session 认证方案的选择
   - 用户注册、登录、密码重置流程设计
   - 多级权限系统设计（普通用户、VIP用户、管理员等）
   - 第三方登录集成可能性（QQ、微信等）
   - 安全性措施（密码加密、防暴力破解、CSRF防护等）

4. **开发工作流和技术栈管理**：
   - 前端（TypeScript/Next.js）+ 后端（Python/FastAPI）的开发环境配置
   - 代码仓库管理策略（单仓库 vs 多仓库）
   - API 接口文档自动生成和维护
   - 前后端联调和测试策略
   - CI/CD 流程设计（支持双语言技术栈）

5. **部署和运维考虑**：
   - 前端静态部署 + 后端服务部署的最佳实践
   - 数据库部署和备份策略
   - 监控、日志和错误追踪方案
   - 成本控制和性能优化建议

请重点分析：
- 对现有 Next.js SSG 项目的影响程度
- 技术栈复杂度增加后的维护成本
- 作为 AI 编程助手在多语言技术栈项目中的协助能力和局限性
- 渐进式迁移路径，确保现有功能不受影响
- 在当前项目添加backend目录，然后添加fastapi相关代码。你作为AI编程agent，能否同时胜任前端后端的开发。且开发语言是两种。

不需要提供具体代码实现，重点提供架构决策指导和技术方案对比分析。

# 后端

基于当前的冒险岛情报站项目（Next.js 14 + TypeScript + SSG配置），我需要为项目添加复杂的后端API功能，包括用户认证和权限管理系统。请分析以下技术方案并提供详细的架构建议：

1. **后端技术栈选择**：
   - FastAPI (Python) ：
      * 用户认证系统的实现复杂度
      * 权限管理和角色控制的灵活性
      * 与现有 Next.js 前端的集成难度
      * 开发和维护成本
      * 性能和扩展性考虑

2. **项目架构设计方案**：
   - 在当前项目根目录下添加 `backend/` 目录的可行性分析
   - 前后端分离架构
   - 数据库选择建议（考虑用户数据、游戏数据存储需求）
   - API 设计规范和版本管理策略
   - 跨域处理和安全性考虑

3. **用户认证系统设计**：
   - JWT 认证方案的选择
   - 用户注册、登录、密码重置流程设计
   - 多级权限系统设计（普通用户、VIP用户、管理员等）
   - 第三方登录集成可能性（QQ、微信等）
   - 安全性措施（密码加密、防暴力破解、CSRF防护等）



# 7
Next.js 14 + FastAPI 对你有些挑战。如果换成 Next.js 14 + Express，对你来说，哪个更好？
我需要认证系统、会员系统（游客、黄金用户、钻石用户）。
游客用户通过fingerprintjs库的指纹来识别用户。
通过用户购买的欢乐豆来控制是否还能继续使用强化武器装备
请做出对比。



基于我们之前讨论的冒险岛情报站项目（Next.js 14 + TypeScript + SSG配置），我需要你对比分析两种后端技术栈方案，并提供详细的技术建议：

**技术栈对比分析**：
1. 方案A：Next.js 14 + FastAPI (Python)
2. 方案B：Next.js 14 + Express.js (Node.js)

**具体需求**：
- 用户认证系统（注册、登录、JWT令牌管理）
- 三级会员系统：
   * 游客用户：使用 FingerprintJS 库进行设备指纹识别，无需注册
   * 黄金用户：注册用户，享有基础会员权益
   * 钻石用户：付费用户，享有高级功能权限
- 权限管理系统（基于用户等级的功能访问控制）
- 通过用户购买的欢乐豆来控制是否还能继续使用强化武器装备

**对比维度**：
1. **开发复杂度**：哪种方案对你作为AI编程助手更容易协助开发？
2. **技术栈一致性**：统一使用JavaScript/TypeScript vs 混合Python的优劣
3. **认证系统实现**：两种方案在实现用户认证和会员系统方面的难易程度
4. **FingerprintJS集成**：设备指纹识别在两种架构中的实现方式
5. **维护成本**：长期维护和功能扩展的复杂度对比
6. **性能考虑**：在当前SSG配置下，两种方案的性能表现
7. **部署复杂度**：考虑到现有的静态部署配置

**输出要求**：
- 明确推荐其中一种方案并说明理由
- 提供详细的架构设计建议
- 说明你作为AI助手在协助开发过程中的能力差异
- 考虑项目的渐进式迁移路径和现有功能保护





基于我们之前讨论的冒险岛情报站项目（Next.js 14 + TypeScript + SSG配置），我需要你对比分析两种后端技术栈方案，并提供详细的技术建议：

**技术栈对比分析**：
1. 方案A：Next.js 14 + FastAPI (Python)
2. 方案B：Next.js 14 + Express.js (Node.js)

**具体功能需求**：
1. **用户认证系统**：
   - 注册、登录、密码重置功能
   - JWT token 管理
   - 安全性措施（密码加密、防暴力破解等）

2. **三级会员系统**：
   - 游客用户：使用 FingerprintJS 库进行设备指纹识别和追踪
   - 黄金用户：付费用户，享有中级权限
   - 钻石用户：高级付费用户，享有最高权限

3. **虚拟货币系统**：
   - "欢乐豆"作为虚拟货币
   - 用户通过购买获得欢乐豆
   - 使用装备强化模拟器等功能需要消耗欢乐豆
   - 需要实现余额检查、消费记录、充值功能

**对比分析要求**：
请从以下维度进行详细对比：
1. **开发复杂度**：哪种方案实现上述功能更简单？
2. **技术栈一致性**：对项目维护和团队协作的影响
3. **AI编程助手协助能力**：你在哪种技术栈下能提供更好的开发支持？
4. **性能和扩展性**：处理用户认证、会员管理、虚拟货币交易的能力
5. **生态系统支持**：相关库和工具的成熟度
6. **部署和运维**：与现有SSG前端的集成难度
7. **开发和维护成本**：长期项目维护的考虑

**特别关注点**：
- FingerprintJS 与后端的集成方案
- 虚拟货币系统的安全性和可靠性
- 会员权限控制的灵活性
- 与现有Next.js SSG配置的兼容性

请提供明确的推荐方案和理由。








